import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsOptional } from 'class-validator';

export class SwitchTeamContextDto {
  @ApiProperty({
    description: 'Team ID to switch to',
    required: true,
  })
  @IsUUID()
  teamId: string;

  @ApiProperty({
    description: 'Organization ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  organizationId?: string;
}

export class GetUserTeamsDto {
  @ApiProperty({
    description: 'Organization ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  organizationId?: string;
}
