"use client";

import { useEffect, useState } from "react";
import axios from "axios";
import { useSearchParams } from "next/navigation";

export default function VerifyPage() {

    const [isVerified, setIsVerified] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isVerifying, setIsVerifying] = useState(false);
    const [error, setError] = useState(null);

    const searchParams = useSearchParams();
    const token = searchParams.get("token");

    useEffect(() => {
        axios.post(`${process.env.NEXT_PUBLIC_API_URL}/user/verify-email`, { token })
            .then((res) => {
                setIsVerified(res.data.result);
                setIsLoading(false);
            })
            .catch((err) => {
                setError(err.response.data.error);
                setIsLoading(false);
            });
    }, [token]);



    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-white rounded-xl shadow-xl p-8 transform transition-all duration-500 hover:shadow-2xl">
                {isLoading ? (
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
                        <p className="mt-6 text-gray-600 text-lg animate-pulse">Verifying your email...</p>
                    </div>
                ) : error ? (
                    <div className="text-center transform animate-fade-in">
                        <div className="text-red-500 text-7xl mb-6">✕</div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">Verification Failed</h1>
                        <p className="text-gray-600 text-lg">{error}</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="mt-6 px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-300"
                        >
                            Try Again
                        </button>
                    </div>
                ) : isVerified ? (
                    <div className="text-center transform animate-fade-in">
                        <div className="text-green-500 text-7xl mb-6">✓</div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">Email Verified!</h1>
                        <p className="text-gray-600 text-lg mb-2">Your email has been successfully verified.</p>
                        <p className="text-gray-600 text-lg">You can now close this window and continue to the app.</p>
                        <button
                            onClick={() => window.close()}
                            className="mt-6 px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-300"
                        >
                            Close Window
                        </button>
                    </div>
                ) : (
                    <div className="text-center transform animate-fade-in">
                        <div className="text-yellow-500 text-7xl mb-6">!</div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">Verification Error</h1>
                        <p className="text-gray-600 text-lg">Unable to verify your email. Please try again.</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="mt-6 px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors duration-300"
                        >
                            Retry Verification
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
}
