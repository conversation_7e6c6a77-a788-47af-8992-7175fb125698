import { create } from 'zustand';
import { Message } from '@/types/chat';

interface SourcesDrawerState {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  currentMessage: Message | null;
  setCurrentMessage: (message: Message | null) => void;
  rawSources: any[];
  setRawSources: (sources: any[]) => void;
  messageSources: Record<string, { count: number; sources: any[] }>;
  setMessageSources: (messageId: string, sources: any[]) => void;
  getMessageSourceCount: (messageId: string) => number;
  getMessageSources: (messageId: string) => any[];
}

function isToolSourceContainer(item: any): item is { tool_name: string; sources: any[] } {
  return item !== null && typeof item === 'object' && typeof item.tool_name === 'string' && Array.isArray(item.sources);
}

function isGoogleDriveToolOutput(item: any): item is { tool_name: string; output: string } {
  return item !== null && typeof item === 'object' &&
         typeof item.tool_name === 'string' &&
         item.tool_name === 'search_drive_files' &&
         typeof item.output === 'string';
}

function isKnowledgeBaseOutput(item: any): item is { Agent: string; sources: any[][] } {
  return item !== null && typeof item === 'object' &&
         typeof item.Agent === 'string' &&
         item.Agent === 'Knowledge Base' &&
         Array.isArray(item.sources);
}

const parseGoogleDriveToolOutput = (item: { tool_name: string; output: string }): any[] => {
  console.log('Parsing Google Drive tool output...');

  try {
    let outputStr = item.output;

    try {
      const outputData = JSON.parse(outputStr);
      if (outputData && outputData.items && Array.isArray(outputData.items)) {
        return outputData.items.map((driveItem: any) => ({
          type: 'Google Drive',
          id: driveItem.id,
          name: driveItem.name,
          title: driveItem.name,
          mime_type: driveItem.mime_type,
          created_time: driveItem.created_time,
          modified_time: driveItem.modified_time,
          size: driveItem.size ? parseInt(driveItem.size) : null,
          web_view_link: driveItem.web_view_link,
          icon_link: driveItem.icon_link,
          owners: driveItem.owners || [],
          shared: driveItem.shared || false,
          description: driveItem.description || '',
          content: `File: ${driveItem.name} (${driveItem.mime_type})`,
          capabilities: driveItem.capabilities,
          parents: driveItem.parents,
          starred: driveItem.starred,
          trashed: driveItem.trashed
        }));
      }
    } catch (jsonError) {
      console.log('Not valid JSON, trying Python dict parsing...');
    }

    const driveItems: any[] = [];

    const driveItemPattern = /DriveItem\(([^)]+(?:\([^)]*\)[^)]*)*)\)/g;
    let match;

    while ((match = driveItemPattern.exec(outputStr)) !== null) {
      const driveItemStr = match[1];
      console.log('Found DriveItem:', driveItemStr.substring(0, 100) + '...');

      try {
        const driveItem = parseDriveItemString(driveItemStr);
        if (driveItem) {
          driveItems.push({
            type: 'Google Drive',
            ...driveItem,
            title: driveItem.name,
            content: `File: ${driveItem.name} (${driveItem.mime_type})`
          });
        }
      } catch (e) {
        console.warn('Error parsing DriveItem:', e);
      }
    }

    console.log(`Parsed ${driveItems.length} Google Drive items`);
    return driveItems;

  } catch (e) {
    console.error('Error parsing Google Drive tool output:', e);
    return [];
  }
};

const parseDriveItemString = (driveItemStr: string): any | null => {
  const fields: any = {};
  const patterns = {
    id: /id='([^']*)'/,
    name: /name='([^']*)'/,
    mime_type: /mime_type='([^']*)'/,
    created_time: /created_time='([^']*)'/,
    modified_time: /modified_time='([^']*)'/,
    size: /size='([^']*)'/,
    web_view_link: /web_view_link='([^']*)'/,
    icon_link: /icon_link='([^']*)'/,
    shared: /shared=(True|False)/,
    starred: /starred=(True|False)/,
    trashed: /trashed=(True|False)/,
    description: /description=([^,)]*)/,
    owners: /owners=(\[[^\]]*\])/,
    parents: /parents=(\[[^\]]*\])/,
    capabilities: /capabilities=(\{[^}]*\})/
  };

  for (const [key, pattern] of Object.entries(patterns)) {
    const match = driveItemStr.match(pattern);
    if (match) {
      if (key === 'shared' || key === 'starred' || key === 'trashed') {
        fields[key] = match[1] === 'True';
      } else if (key === 'size') {
        fields[key] = match[1] ? parseInt(match[1]) : null;
      } else if (key === 'description') {
        fields[key] = match[1] === 'None' ? '' : match[1];
      } else if (key === 'owners' || key === 'parents' || key === 'capabilities') {
        // For complex objects, store as string for now
        fields[key] = match[1];
      } else {
        fields[key] = match[1];
      }
    }
  }

  return fields.id && fields.name ? fields : null;
};

const parseKnowledgeBaseOutput = (item: { Agent: string; sources: any[][] }): any[] => {
  console.log('Parsing Knowledge Base output...');
  const structured: any[] = [];

  try {
    // Knowledge Base sources are nested arrays: sources: [[{...}, {...}]]
    item.sources.forEach((sourceArray, arrayIndex) => {
      if (Array.isArray(sourceArray)) {
        sourceArray.forEach((source, sourceIndex) => {
          if (source && typeof source === 'object') {
            let metadata: any = {};

            // Parse metadata string if it exists
            if (source.metadata && typeof source.metadata === 'string') {
              try {
                // Handle Python-style string representation
                const metadataStr = source.metadata
                  .replace(/'/g, '"')  // Replace single quotes with double quotes
                  .replace(/True/g, 'true')
                  .replace(/False/g, 'false')
                  .replace(/None/g, 'null');
                metadata = JSON.parse(metadataStr);
              } catch (e) {
                console.warn('Failed to parse Knowledge Base metadata:', e);
                metadata = {};
              }
            }

            const structuredSource = {
              type: 'Knowledge Base',
              text: source.text || '',
              content: source.text || '',
              title: metadata.file_name || metadata.name || 'Knowledge Base Document',
              file_name: metadata.file_name || metadata.name || '',
              page_number: metadata.page_number || '',
              connector_id: metadata.connector_id || '',
              connector_type: metadata.connector_type || 'knowledgebase',
              link: metadata.link || '',
              name: metadata.name || metadata.file_name || '',
              score: source.score || 0,
              snippet: source.text ? source.text.substring(0, 200) + '...' : '',
              metadata: metadata
            };

            console.log(`Parsed Knowledge Base source ${arrayIndex}-${sourceIndex}:`, structuredSource.title);
            structured.push(structuredSource);
          }
        });
      }
    });
  } catch (e) {
    console.error('Error parsing Knowledge Base output:', e);
  }

  console.log(`Parsed ${structured.length} Knowledge Base sources`);
  return structured;
};

const parseSourceString = (sourceStr: string): any | null => {
  const trimmedStr = sourceStr.trim();
  if (!trimmedStr) return null;

  console.log('Parsing source string:', trimmedStr.substring(0, 200) + '...');

  const webMatch = trimmedStr.match(/^WebSearchResult\(url='([^']*)', title='([^']*)', content=(['"])((?:(?!\3)[^\\]|\\.)*)(\3), score=([^)]*)\)/s);
  if (webMatch && webMatch[1] && webMatch[2] && webMatch[4]) {
    return {
      type: 'Web Search',
      url: webMatch[1],
      title: webMatch[2],
      content: webMatch[4],
      score: parseFloat(webMatch[6])
    };
  }

  const webMatchSimple = trimmedStr.match(/^WebSearchResult\(url='(.*?)', title='(.*?)', content='(.*?)', score=(.*?)\)/);
  if (webMatchSimple && webMatchSimple[1] && webMatchSimple[2] && webMatchSimple[3]) {
    return {
      type: 'Web Search',
      url: webMatchSimple[1],
      title: webMatchSimple[2],
      content: webMatchSimple[3],
      score: parseFloat(webMatchSimple[4])
    };
  }

  const gmailMatch = trimmedStr.match(/^GmailMessage\(id='([^']*)', thread_id='([^']*)', subject=(['"])((?:(?!\3)[^\\]|\\.)*)(\3), sender='([^']*)', recipients=\[([^\]]*)\], body='([^']*)', date='([^']*)', labels=\[([^\]]*)\], attachments=([^)]*)\)/s);
  if (gmailMatch) {
    const id = gmailMatch[1];
    const thread_id = gmailMatch[2];
    const subject = gmailMatch[4]; // The actual subject content
    const sender = gmailMatch[6];
    const recipientsStr = gmailMatch[7];
    const body = gmailMatch[8] || '';
    const date = gmailMatch[9] || '';
    const labelsStr = gmailMatch[10];
    const attachments = gmailMatch[11];

    const recipients = recipientsStr ?
      recipientsStr.split(', ').map(r => r.replace(/^'(.*)'$/, '$1')) : [];

    const labels = labelsStr ?
      labelsStr.split(', ').map(l => l.replace(/^'(.*)'$/, '$1')) : [];

    return {
      type: 'Gmail',
      id,
      thread_id,
      subject,
      sender,
      recipients,
      body,
      date,
      labels,
      attachments: attachments === 'None' ? null : attachments,
      snippet: body || subject, // Use body as snippet, fallback to subject
      from: sender,
    };
  }

  const gmailMatchAlt = trimmedStr.match(/^GmailMessage\(([^)]+)\)/s);
  if (gmailMatchAlt) {
    try {
      const content = gmailMatchAlt[1];
      const fields: any = {};

      const patterns = {
        id: /id='([^']*)'/,
        thread_id: /thread_id='([^']*)'/,
        subject: /subject=(['"])((?:(?!\1)[^\\]|\\.)*)(\1)/,
        sender: /sender='([^']*)'/,
        recipients: /recipients=\[([^\]]*)\]/,
        body: /body='([^']*)'/,
        date: /date='([^']*)'/,
        labels: /labels=\[([^\]]*)\]/,
        attachments: /attachments=([^,)]*)/
      };

      for (const [key, pattern] of Object.entries(patterns)) {
        const match = content.match(pattern);
        if (match) {
          if (key === 'recipients' || key === 'labels') {
            fields[key] = match[1] ?
              match[1].split(', ').map((item: string) => item.replace(/^'(.*)'$/, '$1')) : [];
          } else if (key === 'attachments') {
            fields[key] = match[1] === 'None' ? null : match[1];
          } else if (key === 'subject') {
            fields[key] = match[2] || match[1];
          } else {
            fields[key] = match[1];
          }
        }
      }

      if (fields.id && fields.subject) {
        return {
          type: 'Gmail',
          id: fields.id,
          thread_id: fields.thread_id || '',
          subject: fields.subject,
          sender: fields.sender || '',
          recipients: fields.recipients || [],
          body: fields.body || '',
          date: fields.date || '',
          labels: fields.labels || [],
          attachments: fields.attachments,
          snippet: fields.body || fields.subject,
          from: fields.sender || '',
        };
      }
    } catch (e) {
      console.warn('Error parsing Gmail message with fallback method:', e);
    }
  }

  // Try to parse GoogleDriveFile
  const gdriveMatch = trimmedStr.match(/^GoogleDriveFile\(id='(.*?)', name='(.*?)', content='(.*?)'(?:, mime_type='(.*?)')?(?:, url='(.*?)')?(?:, size=(.*?))?(?:, modified_time='(.*?)')?(?:, created_time='(.*?)')?\)/s);
  if (gdriveMatch) {
    return {
      type: 'Google Drive',
      id: gdriveMatch[1],
      name: gdriveMatch[2],
      content: gdriveMatch[3],
      mime_type: gdriveMatch[4] || '',
      url: gdriveMatch[5] || '',
      size: gdriveMatch[6] ? parseInt(gdriveMatch[6]) : null,
      modified_time: gdriveMatch[7] || '',
      created_time: gdriveMatch[8] || '',
      title: gdriveMatch[2], // Use name as title for consistency
    };
  }

  const calendarMatch = trimmedStr.match(/^CalendarEvent\(id='([^']*)', summary='([^']*)', organizer_email='([^']*)', start_time='([^']*)', end_time='([^']*)', location='([^']*)', description='([^']*)', attendees=\[([^\]]*)\], html_link='([^']*)', meet_link='([^']*)'\)/s);
  if (calendarMatch) {
    const attendeesStr = calendarMatch[8];
    const attendees = attendeesStr ?
      attendeesStr.split(', ').map(a => a.replace(/^'(.*)'$/, '$1')) : [];

    return {
      type: 'Google Calendar',
      id: calendarMatch[1],
      summary: calendarMatch[2],
      title: calendarMatch[2], 
      organizer_email: calendarMatch[3],
      start_time: calendarMatch[4],
      end_time: calendarMatch[5],
      location: calendarMatch[6],
      description: calendarMatch[7],
      attendees,
      html_link: calendarMatch[9],
      meet_link: calendarMatch[10],
      content: `${calendarMatch[2]} - ${calendarMatch[4]} to ${calendarMatch[5]}`, 
    };
  }

  console.warn('Could not parse source string:', trimmedStr.substring(0, 100) + '...');
  return null;
};

const processMatchedSources = (matches: string[]): any[] => {
  const structured: any[] = [];

  matches.forEach((match, index) => {
    console.log(`Processing match ${index + 1}:`, match.substring(0, 100) + '...');
    const parsedSource = parseSourceString(match.trim());
    if (parsedSource) {
      structured.push(parsedSource);
      console.log(`Successfully parsed source ${index + 1}:`, parsedSource.type);
    } else {
      console.warn(`Failed to parse source ${index + 1}:`, match.substring(0, 100) + '...');
    }
  });

  return structured;
};

const processSourceCollectionString = (collectionStr: string): any[] => {
  const structured: any[] = [];

  console.log('Processing source collection string:', collectionStr.substring(0, 300) + '...');

  const sourcePattern = /(WebSearchResult|GmailMessage|GoogleDriveFile|CalendarEvent)\((?:[^()]*(?:\([^()]*\)[^()]*)*)\)/g;
  const matches = collectionStr.match(sourcePattern);

  if (!matches || matches.length === 0) {
    console.log('Trying alternative pattern matching...');
    const alternativePattern = /(WebSearchResult|GmailMessage|GoogleDriveFile|CalendarEvent)\([^]*?(?=(?:WebSearchResult|GmailMessage|GoogleDriveFile|CalendarEvent)\(|$)/g;
    const altMatches = collectionStr.match(alternativePattern);
    if (altMatches) {
      const cleanedMatches = altMatches.map(match => {
        if (!match.endsWith(')')) {
          const lastParen = match.lastIndexOf(')');
          if (lastParen > 0) {
            return match.substring(0, lastParen + 1);
          }
        }
        return match;
      });
      return processMatchedSources(cleanedMatches);
    }
  }

  if (matches) {
    console.log(`Found ${matches.length} source matches`);
    return processMatchedSources(matches);
  } else {
    console.log('No regex matches found, trying fallback splitting method');
    const sourceStrings = collectionStr.split(/\),\s*(?=(WebSearchResult|GmailMessage|GoogleDriveFile|CalendarEvent)\()/);

    sourceStrings.forEach((sourceStr, strIndex) => {
      const fullSourceStr = sourceStr.trim().length > 0 ?
        (strIndex === sourceStrings.length - 1 ? sourceStr : sourceStr + ')') : '';

      if (fullSourceStr) {
        const parsedSource = parseSourceString(fullSourceStr);
        if (parsedSource) {
          structured.push(parsedSource);
        } else {
          console.warn('Unhandled source string format:', fullSourceStr.substring(0, 100) + '...');
        }
      }
    });
  }

  console.log(`Processed ${structured.length} sources from collection`);
  return structured;
};

const processSingleSourceString = (item: string): any[] => {
  console.log('Processing string item length:', item.length);
  console.log('First 200 chars:', item.substring(0, 200) + '...');

  const sourceCollectionMatch = item.match(/^\[(.*)\]$/s);

  if (sourceCollectionMatch && sourceCollectionMatch[1]) {
    const contentString = sourceCollectionMatch[1];
    console.log('Found source collection string, processing...');

    if (contentString.trim().length === 0) {
      console.log('Content string is empty, skipping.');
      return [];
    }

    return processSourceCollectionString(contentString);
  } else {
    if (item.includes('GmailMessage(') || item.includes('WebSearchResult(') || item.includes('GoogleDriveFile(') || item.includes('CalendarEvent(')) {
      console.log('Found raw source collection without brackets, processing...');
      return processSourceCollectionString(item);
    }
    console.log('Processing as single source string...');
    const parsedSource = parseSourceString(item);
    return parsedSource ? [parsedSource] : [];
  }
};

const parseRawSources = (rawSources: any): any[] => {
  console.log('=== Starting parseRawSources ===');
  console.log('Input rawSources type:', typeof rawSources);
  console.log('Input rawSources:', rawSources);

  const structured: any[] = [];

  if (!Array.isArray(rawSources)) {
    console.log('parseRawSources: rawSources is not an array, treating as single item');

    if (rawSources === null || rawSources === undefined) {
      console.log('parseRawSources: rawSources is null/undefined');
      return structured;
    }

    if (typeof rawSources === 'string') {
      console.log('parseRawSources: Converting single string to array');
      rawSources = [rawSources];
    }
    else if (typeof rawSources === 'object') {
      console.log('parseRawSources: Converting single object to array');
      rawSources = [rawSources];
    }
    else {
      console.warn('parseRawSources: Unexpected rawSources type:', typeof rawSources);
      return structured;
    }
  }

  console.log('Input rawSources length:', rawSources.length);
  console.log('First item type:', rawSources.length > 0 ? typeof rawSources[0] : 'N/A');

  if (rawSources.length === 0) {
    console.log('parseRawSources: empty array provided');
    return structured;
  }

  const sourcesToProcess = Array.isArray(rawSources[0]) ? rawSources[0] : rawSources;
  console.log(`Processing ${sourcesToProcess.length} source items`);

  sourcesToProcess.forEach((item: any, index: number) => {
    console.log(`\n--- Processing item ${index + 1}/${sourcesToProcess.length} ---`);
    console.log('Item type:', typeof item);

    if (typeof item === 'string') {
      console.log('Processing string item...');
      const parsedSources = processSingleSourceString(item);
      console.log(`Parsed ${parsedSources.length} sources from string`);
      structured.push(...parsedSources);

    } else if (Array.isArray(item)) {
      console.log(`Processing array with ${item.length} items...`);
      item.forEach((stringItem, stringIndex) => {
        if (typeof stringItem === 'string') {
          console.log(`Processing string ${stringIndex + 1} in array...`);
          const parsedSources = processSingleSourceString(stringItem);
          console.log(`Parsed ${parsedSources.length} sources from array string`);
          structured.push(...parsedSources);
        } else {
          console.warn('Expected string in array but got:', typeof stringItem);
        }
      });

    } else if (isGoogleDriveToolOutput(item)) {
      console.log('Processing Google Drive tool output...');
      const driveItems = parseGoogleDriveToolOutput(item);
      console.log(`Parsed ${driveItems.length} Google Drive items`);
      structured.push(...driveItems);

    } else if (isKnowledgeBaseOutput(item)) {
      console.log('Processing Knowledge Base output...');
      const kbItems = parseKnowledgeBaseOutput(item);
      console.log(`Parsed ${kbItems.length} Knowledge Base items`);
      structured.push(...kbItems);

    } else if (isToolSourceContainer(item)) {
      console.log('Processing tool source container...');
      item.sources.forEach((sourceItem: any) => {
        const structuredItem = {
          type: item.tool_name === 'Knowledge Base Documents' ? 'Knowledge Base' : item.tool_name,
          ...sourceItem
        };
        console.log('Added structured item from container:', structuredItem.type);
        structured.push(structuredItem);
      });
    } else if (item && typeof item === 'object' && 'type' in item) {
      console.log('Adding pre-structured object:', item.type);
      structured.push(item);
    } else {
      console.warn('Skipping unexpected item type:', typeof item);
    }
  });

  console.log(`\n=== Final result: ${structured.length} structured sources ===`);
  structured.forEach((source, index) => {
    console.log(`${index + 1}. ${source.type}: ${source.subject || source.title || source.name || 'Unknown'}`);
  });

  return structured;
};

export { parseRawSources };

export const useSourcesDrawer = create<SourcesDrawerState>((set, get) => ({
  isOpen: false,
  setIsOpen: (isOpen) => set({ isOpen }),
  currentMessage: null,
  setCurrentMessage: (message) => set({ currentMessage: message }),
  rawSources: [],
  setRawSources: (rawSources) => {
    console.log('\n=== Setting raw sources in store ===');
    console.log(JSON.stringify(rawSources, null, 2));
    set({ rawSources });
  },
  messageSources: {},
  setMessageSources: (messageId: string, sources: any[]) => {
    console.log(`[SourcesStore] ===== Setting sources for message ${messageId} =====`);
    console.log(`[SourcesStore] Raw sources received:`, sources);
    console.log(`[SourcesStore] Raw sources length: ${Array.isArray(sources) ? sources.length : 'Not array'}`);

    const parsed = parseRawSources(sources);
    console.log(`[SourcesStore] Parsed ${parsed.length} structured sources for message ${messageId}`);
    console.log(`[SourcesStore] Parsed sources:`, parsed);

    const currentSources = get().messageSources;
    const newSources = {
      ...currentSources,
      [messageId]: { count: parsed.length, sources: parsed }
    };

    console.log(`[SourcesStore] Updated store with count ${parsed.length} for message ${messageId}`);
    console.log(`[SourcesStore] Store now has ${Object.keys(newSources).length} messages`);

    set({ messageSources: newSources });
  },
  getMessageSourceCount: (messageId: string): number => {
    const messageData = get().messageSources[messageId];
    const count = messageData ? messageData.count : 0;
    console.log(`[SourcesStore] Getting source count for message ${messageId}: ${count}`);
    return count;
  },
  getMessageSources: (messageId: string): any[] => {
    const messageData = get().messageSources[messageId];
    return messageData ? messageData.sources : [];
  },
}));

export const selectStructuredSources = (state: SourcesDrawerState) => {
  console.log('\n=== Selecting structured sources ===');
  const result = parseRawSources(state.rawSources);
  console.log('Selected structured sources:', JSON.stringify(result, null, 2));
  return result;
};
