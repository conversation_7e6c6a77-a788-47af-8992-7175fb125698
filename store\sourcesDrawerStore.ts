import { create } from 'zustand';
import { Message } from '@/types/chat';

interface SourcesDrawerState {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  currentMessage: Message | null;
  setCurrentMessage: (message: Message | null) => void;
  rawSources: any[];
  setRawSources: (sources: any[]) => void;
}

function isToolSourceContainer(item: any): item is { tool_name: string; sources: any[] } {
  return item !== null && typeof item === 'object' && typeof item.tool_name === 'string' && Array.isArray(item.sources);
}

// Helper function to parse individual source strings
const parseSourceString = (sourceStr: string): any | null => {
  const trimmedStr = sourceStr.trim();
  if (!trimmedStr) return null;

  console.log('Parsing source string:', trimmedStr.substring(0, 200) + '...');

  // Try to parse WebSearchResult
  const webMatch = trimmedStr.match(/^WebSearchResult\(url='(.*?)', title='(.*?)', content='(.*?)', score=(.*?)\)/);
  if (webMatch && webMatch[1] && webMatch[2] && webMatch[3]) {
    return {
      type: 'Web Search',
      url: webMatch[1],
      title: webMatch[2],
      content: webMatch[3],
      score: parseFloat(webMatch[4])
    };
  }

  // Enhanced Gmail parsing to handle both single and double quotes
  const gmailMatch = trimmedStr.match(/^GmailMessage\(id='([^']*)', thread_id='([^']*)', subject=(['"])((?:(?!\3)[^\\]|\\.)*)(\3), sender='([^']*)', recipients=\[([^\]]*)\], body='([^']*)', date='([^']*)', labels=\[([^\]]*)\], attachments=([^)]*)\)/s);
  if (gmailMatch) {
    const id = gmailMatch[1];
    const thread_id = gmailMatch[2];
    const subject = gmailMatch[4]; // The actual subject content
    const sender = gmailMatch[6];
    const recipientsStr = gmailMatch[7];
    const body = gmailMatch[8] || '';
    const date = gmailMatch[9] || '';
    const labelsStr = gmailMatch[10];
    const attachments = gmailMatch[11];

    // Parse recipients array
    const recipients = recipientsStr ?
      recipientsStr.split(', ').map(r => r.replace(/^'(.*)'$/, '$1')) : [];

    // Parse labels array
    const labels = labelsStr ?
      labelsStr.split(', ').map(l => l.replace(/^'(.*)'$/, '$1')) : [];

    return {
      type: 'Gmail',
      id,
      thread_id,
      subject,
      sender,
      recipients,
      body,
      date,
      labels,
      attachments: attachments === 'None' ? null : attachments,
      snippet: body || subject, // Use body as snippet, fallback to subject
      from: sender,
    };
  }

  // Fallback Gmail parsing for variations in format
  const gmailMatchAlt = trimmedStr.match(/^GmailMessage\(([^)]+)\)/s);
  if (gmailMatchAlt) {
    try {
      const content = gmailMatchAlt[1];
      const fields: any = {};

      // Parse key-value pairs more flexibly, handling both single and double quotes
      const patterns = {
        id: /id='([^']*)'/,
        thread_id: /thread_id='([^']*)'/,
        subject: /subject=(['"])((?:(?!\1)[^\\]|\\.)*)(\1)/,
        sender: /sender='([^']*)'/,
        recipients: /recipients=\[([^\]]*)\]/,
        body: /body='([^']*)'/,
        date: /date='([^']*)'/,
        labels: /labels=\[([^\]]*)\]/,
        attachments: /attachments=([^,)]*)/
      };

      for (const [key, pattern] of Object.entries(patterns)) {
        const match = content.match(pattern);
        if (match) {
          if (key === 'recipients' || key === 'labels') {
            fields[key] = match[1] ?
              match[1].split(', ').map((item: string) => item.replace(/^'(.*)'$/, '$1')) : [];
          } else if (key === 'attachments') {
            fields[key] = match[1] === 'None' ? null : match[1];
          } else if (key === 'subject') {
            // For subject, use the captured content (match[2] for the quoted content)
            fields[key] = match[2] || match[1];
          } else {
            fields[key] = match[1];
          }
        }
      }

      if (fields.id && fields.subject) {
        return {
          type: 'Gmail',
          id: fields.id,
          thread_id: fields.thread_id || '',
          subject: fields.subject,
          sender: fields.sender || '',
          recipients: fields.recipients || [],
          body: fields.body || '',
          date: fields.date || '',
          labels: fields.labels || [],
          attachments: fields.attachments,
          snippet: fields.body || fields.subject,
          from: fields.sender || '',
        };
      }
    } catch (e) {
      console.warn('Error parsing Gmail message with fallback method:', e);
    }
  }

  // Try to parse GoogleDriveFile
  const gdriveMatch = trimmedStr.match(/^GoogleDriveFile\(id='(.*?)', name='(.*?)', content='(.*?)'(?:, mime_type='(.*?)')?(?:, url='(.*?)')?(?:, size=(.*?))?(?:, modified_time='(.*?)')?(?:, created_time='(.*?)')?\)/s);
  if (gdriveMatch) {
    return {
      type: 'Google Drive',
      id: gdriveMatch[1],
      name: gdriveMatch[2],
      content: gdriveMatch[3],
      mime_type: gdriveMatch[4] || '',
      url: gdriveMatch[5] || '',
      size: gdriveMatch[6] ? parseInt(gdriveMatch[6]) : null,
      modified_time: gdriveMatch[7] || '',
      created_time: gdriveMatch[8] || '',
      title: gdriveMatch[2], // Use name as title for consistency
    };
  }

  console.warn('Could not parse source string:', trimmedStr.substring(0, 100) + '...');
  return null;
};

// Helper function to process source collection strings
const processSourceCollectionString = (collectionStr: string): any[] => {
  const structured: any[] = [];

  console.log('Processing source collection string:', collectionStr.substring(0, 300) + '...');

  // Enhanced splitting to handle complex nested structures
  // Look for patterns like "GmailMessage(...), GmailMessage(...)" etc.
  const sourcePattern = /(WebSearchResult|GmailMessage|GoogleDriveFile)\([^)]*(?:\([^)]*\)[^)]*)*\)/g;
  const matches = collectionStr.match(sourcePattern);

  if (matches) {
    console.log(`Found ${matches.length} source matches`);
    matches.forEach((match, index) => {
      console.log(`Processing match ${index + 1}:`, match.substring(0, 100) + '...');
      const parsedSource = parseSourceString(match.trim());
      if (parsedSource) {
        structured.push(parsedSource);
        console.log(`Successfully parsed source ${index + 1}:`, parsedSource.type);
      } else {
        console.warn(`Failed to parse source ${index + 1}:`, match.substring(0, 100) + '...');
      }
    });
  } else {
    // Fallback to original splitting method
    console.log('No regex matches found, trying fallback splitting method');
    const sourceStrings = collectionStr.split(/\),\s*(?=(WebSearchResult|GmailMessage|GoogleDriveFile)\()/);

    sourceStrings.forEach((sourceStr, strIndex) => {
      // Add closing parenthesis if not the last item
      const fullSourceStr = sourceStr.trim().length > 0 ?
        (strIndex === sourceStrings.length - 1 ? sourceStr : sourceStr + ')') : '';

      if (fullSourceStr) {
        const parsedSource = parseSourceString(fullSourceStr);
        if (parsedSource) {
          structured.push(parsedSource);
        } else {
          console.warn('Unhandled source string format:', fullSourceStr.substring(0, 100) + '...');
        }
      }
    });
  }

  console.log(`Processed ${structured.length} sources from collection`);
  return structured;
};

// Helper function to process a single source string (either raw or collection)
const processSingleSourceString = (item: string): any[] => {
  console.log('Processing string item length:', item.length);
  console.log('First 200 chars:', item.substring(0, 200) + '...');

  // Check if it's a source collection format [...]
  const sourceCollectionMatch = item.match(/^\[(.*)\]$/s);

  if (sourceCollectionMatch && sourceCollectionMatch[1]) {
    const contentString = sourceCollectionMatch[1];
    console.log('Found source collection string, processing...');

    if (contentString.trim().length === 0) {
      console.log('Content string is empty, skipping.');
      return [];
    }

    return processSourceCollectionString(contentString);
  } else {
    // Check if it's a raw collection without brackets (like your Gmail example)
    if (item.includes('GmailMessage(') || item.includes('WebSearchResult(') || item.includes('GoogleDriveFile(')) {
      console.log('Found raw source collection without brackets, processing...');
      return processSourceCollectionString(item);
    }

    // Try to parse as single source
    console.log('Processing as single source string...');
    const parsedSource = parseSourceString(item);
    return parsedSource ? [parsedSource] : [];
  }
};

const parseRawSources = (rawSources: any[]): any[] => {
  console.log('=== Starting parseRawSources ===');
  console.log('Input rawSources type:', typeof rawSources);
  console.log('Input rawSources length:', Array.isArray(rawSources) ? rawSources.length : 'N/A');
  console.log('First item type:', Array.isArray(rawSources) && rawSources.length > 0 ? typeof rawSources[0] : 'N/A');

  const structured: any[] = [];

  if (!Array.isArray(rawSources)) {
    console.warn('parseRawSources: rawSources is not an array', rawSources);
    return structured;
  }

  if (rawSources.length === 0) {
    console.log('parseRawSources: empty array provided');
    return structured;
  }

  // Handle both nested array and single array cases
  const sourcesToProcess = Array.isArray(rawSources[0]) ? rawSources[0] : rawSources;
  console.log(`Processing ${sourcesToProcess.length} source items`);

  sourcesToProcess.forEach((item, index) => {
    console.log(`\n--- Processing item ${index + 1}/${sourcesToProcess.length} ---`);
    console.log('Item type:', typeof item);

    if (typeof item === 'string') {
      console.log('Processing string item...');
      const parsedSources = processSingleSourceString(item);
      console.log(`Parsed ${parsedSources.length} sources from string`);
      structured.push(...parsedSources);

    } else if (Array.isArray(item)) {
      console.log(`Processing array with ${item.length} items...`);
      item.forEach((stringItem, stringIndex) => {
        if (typeof stringItem === 'string') {
          console.log(`Processing string ${stringIndex + 1} in array...`);
          const parsedSources = processSingleSourceString(stringItem);
          console.log(`Parsed ${parsedSources.length} sources from array string`);
          structured.push(...parsedSources);
        } else {
          console.warn('Expected string in array but got:', typeof stringItem);
        }
      });

    } else if (isToolSourceContainer(item)) {
      console.log('Processing tool source container...');
      item.sources.forEach((sourceItem: any) => {
        const structuredItem = {
          type: item.tool_name === 'Knowledge Base Documents' ? 'Knowledge Base' : item.tool_name,
          ...sourceItem
        };
        console.log('Added structured item from container:', structuredItem.type);
        structured.push(structuredItem);
      });
    } else if (item && typeof item === 'object' && 'type' in item) {
      console.log('Adding pre-structured object:', item.type);
      structured.push(item);
    } else {
      console.warn('Skipping unexpected item type:', typeof item);
    }
  });

  console.log(`\n=== Final result: ${structured.length} structured sources ===`);
  structured.forEach((source, index) => {
    console.log(`${index + 1}. ${source.type}: ${source.subject || source.title || source.name || 'Unknown'}`);
  });

  return structured;
};

export const useSourcesDrawer = create<SourcesDrawerState>((set) => ({
  isOpen: false,
  setIsOpen: (isOpen) => set({ isOpen }),
  currentMessage: null,
  setCurrentMessage: (message) => set({ currentMessage: message }),
  rawSources: [],
  setRawSources: (rawSources) => {
    console.log('\n=== Setting raw sources in store ===');
    console.log(JSON.stringify(rawSources, null, 2));
    set({ rawSources });
  },
}));

export const selectStructuredSources = (state: SourcesDrawerState) => {
  console.log('\n=== Selecting structured sources ===');
  const result = parseRawSources(state.rawSources);
  console.log('Selected structured sources:', JSON.stringify(result, null, 2));
  return result;
};
