import { create } from 'zustand';
import { Message } from '@/types/chat';

interface SourcesDrawerState {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  currentMessage: Message | null;
  setCurrentMessage: (message: Message | null) => void;
  rawSources: any[];
  setRawSources: (sources: any[]) => void;
}

function isToolSourceContainer(item: any): item is { tool_name: string; sources: any[] } {
  return item !== null && typeof item === 'object' && typeof item.tool_name === 'string' && Array.isArray(item.sources);
}

// Helper function to parse individual source strings
const parseSourceString = (sourceStr: string): any | null => {
  const trimmedStr = sourceStr.trim();
  if (!trimmedStr) return null;

  // Try to parse WebSearchResult
  const webMatch = trimmedStr.match(/^WebSearchResult\(url='(.*?)', title='(.*?)', content='(.*?)', score=(.*?)\)/);
  if (webMatch && webMatch[1] && webMatch[2] && webMatch[3]) {
    return {
      type: 'Web Search',
      url: webMatch[1],
      title: webMatch[2],
      content: webMatch[3],
      score: parseFloat(webMatch[4])
    };
  }
  // Try to parse GmailMessage
  const gmailMatch = trimmedStr.match(/^GmailMessage\(id='(.*?)', thread_id='(.*?)', subject='(.*?)', sender='(.*?)', recipients=\[(.*?)\](?:, body='(.*?)')?(?:, date='(.*?)')?(?:, labels=\[(.*?)\])?(?:, attachments=(.*?))?\)/s);
  if (gmailMatch) {
    const id = gmailMatch[1];
    const thread_id = gmailMatch[2];
    const subject = gmailMatch[3];
    const sender = gmailMatch[4];
    const recipientsStr = gmailMatch[5];
    const body = gmailMatch[6] || '';
    const date = gmailMatch[7] || '';
    const labelsStr = gmailMatch[8];
    const attachments = gmailMatch[9] || null;

    const recipients = recipientsStr ? recipientsStr.split(', ').map(r => r.replace(/^'(.*)'$/, '$1')) : [];
    const labels = labelsStr ? labelsStr.split(', ').map(l => l.replace(/^'(.*)'$/, '$1')) : [];

    return {
      type: 'Gmail',
      id,
      thread_id,
      subject,
      sender,
      recipients,
      body,
      date,
      labels,
      attachments,
      snippet: body,
      from: sender,
    };
  }

  // Try to parse GoogleDriveFile
  const gdriveMatch = trimmedStr.match(/^GoogleDriveFile\(id='(.*?)', name='(.*?)', content='(.*?)'(?:, mime_type='(.*?)')?(?:, url='(.*?)')?(?:, size=(.*?))?(?:, modified_time='(.*?)')?(?:, created_time='(.*?)')?\)/s);
  if (gdriveMatch) {
    return {
      type: 'Google Drive',
      id: gdriveMatch[1],
      name: gdriveMatch[2],
      content: gdriveMatch[3],
      mime_type: gdriveMatch[4] || '',
      url: gdriveMatch[5] || '',
      size: gdriveMatch[6] ? parseInt(gdriveMatch[6]) : null,
      modified_time: gdriveMatch[7] || '',
      created_time: gdriveMatch[8] || '',
      title: gdriveMatch[2], // Use name as title for consistency
    };
  }

  return null;
};

// Helper function to process source collection strings
const processSourceCollectionString = (collectionStr: string): any[] => {
  const structured: any[] = [];
  
  // Split by source types and handle each
  const sourceStrings = collectionStr.split(/\),\s*(?=(WebSearchResult|GmailMessage|GoogleDriveFile)\()/);
  
  sourceStrings.forEach((sourceStr, strIndex) => {
    // Add closing parenthesis if not the last item
    const fullSourceStr = sourceStr.trim().length > 0 ? 
      (strIndex === sourceStrings.length - 1 ? sourceStr : sourceStr + ')') : '';
    
    if (fullSourceStr) {
      const parsedSource = parseSourceString(fullSourceStr);
      if (parsedSource) {
        structured.push(parsedSource);
      } else {
        console.warn('Unhandled source string format:', fullSourceStr);
      }
    }
  });
  
  return structured;
};

// Helper function to process a single source string (either raw or collection)
const processSingleSourceString = (item: string): any[] => {
  console.log('Processing string item:', item);
  
  // Check if it's a source collection format [...]
  const sourceCollectionMatch = item.match(/^\[(.*)\]$/s);
  
  if (sourceCollectionMatch && sourceCollectionMatch[1]) {
    const contentString = sourceCollectionMatch[1];
    console.log('Found source collection string, processing...');
    
    if (contentString.trim().length === 0) {
      console.log('Content string is empty, skipping.');
      return [];
    }
    
    return processSourceCollectionString(contentString);
  } else {
    // Try to parse as single source
    console.log('Processing as single source string...');
    const parsedSource = parseSourceString(item);
    return parsedSource ? [parsedSource] : [];
  }
};

const parseRawSources = (rawSources: any[]): any[] => {
  console.log('=== Starting parseRawSources ===');
  console.log('Input rawSources:', JSON.stringify(rawSources, null, 2));

  const structured: any[] = [];

  if (!Array.isArray(rawSources)) {
    console.warn('parseRawSources: rawSources is not an array', rawSources);
    return structured;
  }

  // Handle both nested array and single array cases
  const sourcesToProcess = Array.isArray(rawSources[0]) ? rawSources[0] : rawSources;

  sourcesToProcess.forEach((item, index) => {
    console.log(`\nProcessing item ${index}:`, JSON.stringify(item, null, 2));

    if (typeof item === 'string') {
      // Handle single string
      const parsedSources = processSingleSourceString(item);
      structured.push(...parsedSources);
      
    } else if (Array.isArray(item)) {
      // Handle array of strings
      console.log('item is an array of strings, processing each...');
      item.forEach((stringItem, stringIndex) => {
        if (typeof stringItem === 'string') {
          console.log(`Processing string ${stringIndex} in array:`, stringItem);
          const parsedSources = processSingleSourceString(stringItem);
          structured.push(...parsedSources);
        } else {
          console.warn('Expected string in array but got:', typeof stringItem, stringItem);
        }
      });
      
    } else if (isToolSourceContainer(item)) {
      console.log('item is a tool source container object:', item);
      item.sources.forEach((sourceItem: any) => {
        const structuredItem = {
          type: item.tool_name === 'Knowledge Base Documents' ? 'Knowledge Base' : item.tool_name,
          ...sourceItem
        };
        console.log('Added structured item from object:', structuredItem);
        structured.push(structuredItem);
      });
    } else if (item && typeof item === 'object' && 'type' in item) {
      console.log('Adding already structured object:', item);
      structured.push(item);
    } else {
      console.warn('Skipping unexpected item type:', typeof item, item);
    }
  });

  console.log('\n=== Final structured sources ===');
  console.log(JSON.stringify(structured, null, 2));
  return structured;
};

export const useSourcesDrawer = create<SourcesDrawerState>((set) => ({
  isOpen: false,
  setIsOpen: (isOpen) => set({ isOpen }),
  currentMessage: null,
  setCurrentMessage: (message) => set({ currentMessage: message }),
  rawSources: [],
  setRawSources: (rawSources) => {
    console.log('\n=== Setting raw sources in store ===');
    console.log(JSON.stringify(rawSources, null, 2));
    set({ rawSources });
  },
}));

export const selectStructuredSources = (state: SourcesDrawerState) => {
  console.log('\n=== Selecting structured sources ===');
  const result = parseRawSources(state.rawSources);
  console.log('Selected structured sources:', JSON.stringify(result, null, 2));
  return result;
};
