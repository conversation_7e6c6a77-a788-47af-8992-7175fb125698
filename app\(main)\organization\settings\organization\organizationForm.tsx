"use client"
import { useEffect, useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@steps-ai/ui"
import { Building, Users, Calendar, Shield, Crown, User, AlertCircle, Info } from "lucide-react"

import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@steps-ai/ui"
import { Textarea } from "@/components/ui/textarea"
import { Skeleton } from "@steps-ai/ui"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { useUser } from '@/contexts/UserContext'
import { organizationApi } from '@/lib/api/organization'
import { OrganizationDetails } from '@/types/organization'

const organizationFormSchema = z.object({
  description: z
    .string()
    .max(500, {
      message: "Description must not be longer than 500 characters.",
    })
    .optional(),
  logo: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal(""))
})

type OrganizationFormValues = z.infer<typeof organizationFormSchema>

interface OrganizationDetailsWithStats extends OrganizationDetails {
  memberCount?: number; 
  teamCount?: number;  
}

export function OrganizationForm() {
  const { user, isAdmin, isLoading: userLoading } = useUser()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [organizationDetails, setOrganizationDetails] = useState<OrganizationDetailsWithStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues: {
      description: "",
      logo: "",
    },
  })

  useEffect(() => {
    if (user.organizationId && !userLoading) {
      fetchOrganizationDetails()
    }
  }, [user.organizationId, userLoading])

  const fetchOrganizationDetails = async () => {
    try {
      setIsLoading(true)
      const details = await organizationApi.getOrganizationDetails()

      if (!details) {
        throw new Error('Organization data not found')
      }
      const organizationData: OrganizationDetailsWithStats = {
        ...details,
        memberCount: details._count?.users || 0,
        teamCount: details._count?.teams || 0
      }

      setOrganizationDetails(organizationData)
      form.reset({
        description: details.description || "",
        logo: details.logo || "",
      })
    } catch (error) {
      console.error('Failed to fetch organization details:', error)
      toast.error('Failed to load organization details')

      try {
        const [users, teams] = await Promise.all([
          organizationApi.getOrganizationUsers().catch(() => []),
          organizationApi.getTeams().catch(() => [])
        ])

        const fallbackData: OrganizationDetailsWithStats = {
          id: user.organizationId!,
          name: 'Organization',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          memberCount: Array.isArray(users) ? users.filter(u => u.hasOnboarded).length : 0,
          teamCount: Array.isArray(teams) ? teams.length : 0
        }

        setOrganizationDetails(fallbackData)
      } catch (fallbackError) {
        console.error('Fallback method also failed:', fallbackError)
      }
    } finally {
      setIsLoading(false)
    }
  }

  async function onSubmit(values: OrganizationFormValues) {
    if (!isAdmin) {
      toast.error('You do not have permission to update organization details.')
      return
    }

    try {
      setIsSubmitting(true)

      const updateData: { description?: string; logo?: string } = {}

      if (values.description?.trim()) {
        updateData.description = values.description.trim()
      }

      if (values.logo?.trim()) {
        updateData.logo = values.logo.trim()
      }

      await organizationApi.updateOrganization(updateData)

      await fetchOrganizationDetails()

      toast.success('Organization details updated successfully')
    } catch (error: any) {
      console.error('Failed to update organization:', error)

      if (error.response?.status === 403) {
        toast.error('You do not have permission to update organization details. Only admins can make changes.')
      } else if (error.response?.status === 404) {
        toast.error('Organization not found. Please try refreshing the page.')
      } else {
        toast.error('Failed to update organization details. Please try again.')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-destructive/10 text-destructive'
      case 'TEAMADMIN':
        return 'bg-primary/10 text-primary'
      case 'USER':
        return 'bg-secondary/10 text-secondary-foreground'
      default:
        return 'bg-muted text-muted-foreground'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return <Crown className="h-3 w-3" />
      case 'TEAMADMIN':
        return <Shield className="h-3 w-3" />
      default:
        return <User className="h-3 w-3" />
    }
  }

  if (userLoading || isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-card rounded-xl border p-6 space-y-6">
          <div className="flex flex-col sm:flex-row items-start gap-6">
            <div className="relative">
              <Skeleton className="h-20 w-20 rounded-full" />
              <Skeleton className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full" />
            </div>
            <div className="flex-1 space-y-3">
              <div className="space-y-2">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-64" />
              </div>
              <div className="flex items-center gap-3">
                <Skeleton className="h-6 w-20 rounded-full" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-muted/30 p-4 rounded-xl border">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-xl" />
                  <div className="space-y-1">
                    <Skeleton className="h-6 w-8" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-card rounded-xl border p-6 space-y-6">
          <div className="space-y-1">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-80" />
          </div>
          <div className="space-y-6">
            <div className="space-y-3">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-11 w-full" />
              <Skeleton className="h-3 w-64" />
            </div>
            <div className="space-y-3">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-28 w-full" />
              <Skeleton className="h-3 w-72" />
            </div>
            <div className="space-y-3">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-11 w-full" />
              <Skeleton className="h-3 w-80" />
            </div>
          </div>
          <div className="flex items-center justify-between pt-6 border-t">
            <Skeleton className="h-4 w-64" />
            <Skeleton className="h-11 w-32" />
          </div>
        </div>
      </div>
    )
  }

  if (!organizationDetails) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load organization details. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <>
      {!isAdmin && (
        <Alert className="border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950/30">
          <Info className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertDescription className="text-amber-800 dark:text-amber-200">
            Only organization administrators can edit these details.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col sm:flex-row items-start gap-6 pb-6">
        <Avatar className="h-20 w-20 border-2 border-border shadow-sm">
          <AvatarImage src={organizationDetails.logo} alt={organizationDetails.name} />
          <AvatarFallback className="text-2xl font-semibold bg-gradient-to-br from-primary/10 to-primary/5 text-primary">
            {organizationDetails.name?.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 space-y-3">
          <h3 className="text-2xl font-bold tracking-tight">{organizationDetails.name}</h3>
          <p className="text-muted-foreground text-lg leading-relaxed">
            {organizationDetails.description || "No description provided"}
          </p>
          
          <div className="flex items-center gap-3">
            <Badge variant="outline" className={`${getRoleColor(user.role || 'USER')} border-0 px-3 py-1.5 font-medium`}>
              {getRoleIcon(user.role || 'USER')}
              <span className="ml-1.5">{user.role}</span>
            </Badge>
            <span className="text-sm text-muted-foreground">Your role in this organization</span>
          </div>
        </div>
      </div>

      <Separator className="my-6" />

      {/* <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 pb-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 p-4 rounded-xl border border-blue-200/50 dark:border-blue-800/50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Users className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{organizationDetails.memberCount}</p>
              <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">Members</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 p-4 rounded-xl border border-green-200/50 dark:border-green-800/50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-500 rounded-lg">
              <Building className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">{organizationDetails.teamCount}</p>
              <p className="text-sm text-green-700 dark:text-green-300 font-medium">Teams</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 p-4 rounded-xl border border-purple-200/50 dark:border-purple-800/50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-500 rounded-lg">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="text-lg font-bold text-purple-900 dark:text-purple-100">
                {new Date(organizationDetails.createdAt).toLocaleDateString()}
              </p>
              <p className="text-sm text-purple-700 dark:text-purple-300 font-medium">Created</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/30 dark:to-amber-900/30 p-4 rounded-xl border border-amber-200/50 dark:border-amber-800/50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-500 rounded-lg">
              <Shield className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="text-lg font-bold text-amber-900 dark:text-amber-100">
                {new Date(organizationDetails.updatedAt).toLocaleDateString()}
              </p>
              <p className="text-sm text-amber-700 dark:text-amber-300 font-medium">Updated</p>
            </div>
          </div>
        </div>
      </div> */}

      {/* <Separator className="my-6" /> */}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold">Organization Settings</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Configure your organization's basic information and preferences
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-2">
                <label className="text-sm font-medium">Organization Name</label>
                <Input
                  value={organizationDetails.name}
                  disabled
                  className="bg-muted/50 h-11 border-dashed"
                />
                <p className="text-xs text-muted-foreground">
                  Organization name cannot be changed from this interface.
                </p>
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a brief description of your organization..."
                        {...field}
                        disabled={isSubmitting || !isAdmin}
                        className={`${!isAdmin ? "bg-muted/50 border-dashed" : ""} min-h-[120px] resize-none`}
                        rows={5}
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      A brief description visible to all organization members.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Logo URL</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://example.com/logo.png"
                        {...field}
                        disabled={isSubmitting || !isAdmin}
                        className={`${!isAdmin ? "bg-muted/50 border-dashed" : ""} h-11`}
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      URL to your organization's logo. Recommended: 200x200px or larger, square format.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {isAdmin && (
            <>
              <Separator />
              <div className="flex items-center justify-between pt-2">
                <p className="text-sm text-muted-foreground">
                  Changes will be visible to all organization members.
                </p>
                <Button
                  type="submit"
                  disabled={isSubmitting || !form.formState.isDirty}
                  className="px-8 h-11"
                >
                  {isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </>
          )}
        </form>
      </Form>
    </>
  )
}
