import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../../prisma/prisma.service';
import * as bcrypt from 'bcrypt';
import * as speakeasy from 'speakeasy';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { GoogleDto } from '../dtos/social.dto';
import { LoginDto } from '../dtos/login.dto';
import { randomBytes } from 'crypto';
import { RefreshDto } from '../dtos/refresh.dto';
import { EmailService } from 'src/email/email.service';
import { resetPasswordTemplate } from 'src/email/templates/reset-password';
export interface AuthResult {
  id: string;
  email: string;
  name: string;
  image?: string;
  role?: string;
  hasOnboarded: boolean;
  initialOnboardingCompleted: boolean;
  organization?: string;
  organizationId?: string;
}

@Injectable()
export class AuthService {
  private saltRounds = 10;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private jwtService: JwtService,
    private emailService: EmailService,
  ) { }

  async loginWithCredentials(credentials: LoginDto): Promise<AuthResult> {
    const { email, password } = credentials;

    const user = await this.prisma.user.findUnique({
      where: { email },
      include: {
        organization: true,
      },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (!user.password) {
      throw new UnauthorizedException('Invalid login method');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      throw new UnauthorizedException('Incorrect password');
    }

    if (user.twoFactorEnabled) {
      if (!credentials.code) {
        throw new UnauthorizedException('2FA code is required');
      }

      const verified = speakeasy.totp.verify({
        secret: user.twoFactorsecret,
        encoding: 'base32',
        token: credentials.code,
      });

      if (!verified) {
        throw new UnauthorizedException('Invalid 2FA code');
      }
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      image: user.profileImageUrl,
      hasOnboarded: user.hasOnboarded,
      initialOnboardingCompleted: user.onboardingCompleted,
      organization: user?.organization?.name || null,
      organizationId: user.organizationId,
    };
  }

  async registerWithCredentials(data: {
    name: string;
    email: string;
    password: string;
  }): Promise<AuthResult> {
    const { name, email, password } = data;

    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    const freePlan = await this.prisma.plan.findFirst({
      where: { type: 'FREE' },
    });

    if (!freePlan) {
      throw new BadRequestException('Free plan not configured in the system');
    }

    const hashedPassword = await bcrypt.hash(password, this.saltRounds);

    const user = await this.prisma.$transaction(async (prisma) => {
      const newUser = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          planId: freePlan.id,
        },
      });

      await prisma.subscription.create({
        data: {
          userId: newUser.id,
          planId: freePlan.id,
          isActive: true,
          endDate: null,
        },
      });

      // Fetch the user again with organization included
      const userWithOrg = await prisma.user.findUnique({
        where: { id: newUser.id },
        include: { organization: true },
      });

      return userWithOrg;
    });

    await this.emailService.sendWelcomeEmail(email, name);

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      image: user.profileImageUrl,
      hasOnboarded: user.hasOnboarded,
      initialOnboardingCompleted: user.onboardingCompleted,
      organization: user?.organization?.name || null,
      organizationId: user.organizationId,
    };
  }

  async googleAuthenticate(userData: GoogleDto): Promise<AuthResult> {
    try {
      const { email, name, profileImageUrl: picture } = userData;

      let user = await this.prisma.user.findUnique({
        where: { email },
        include: {
          organization: true,
        },
      });
      console.log(user);

      if (!user) {
        const freePlan = await this.prisma.plan.findFirst({
          where: { type: 'FREE' },
        });

        if (!freePlan) {
          throw new BadRequestException(
            'Free plan not configured in the system',
          );
        }

        user = await this.prisma.$transaction(async (prisma) => {
          const newUser = await prisma.user.create({
            data: {
              email,
              name,
              profileImageUrl: picture,
              emailVerified: true,
              planId: freePlan.id,
            },
          });

          await prisma.subscription.create({
            data: {
              userId: newUser.id,
              planId: freePlan.id,
              isActive: true,
              endDate: null,
            },
          });

          const userWithOrg = await prisma.user.findUnique({
            where: { id: newUser.id },
            include: { organization: true },
          });

          return userWithOrg;
        });

        await this.emailService.sendWelcomeEmail(email, name);
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.profileImageUrl,
        role: user.role,
        hasOnboarded: user.hasOnboarded,
        initialOnboardingCompleted: user.onboardingCompleted,
        organization: user?.organization?.name || null,
        organizationId: user?.organizationId || null,
      };
    } catch (error) {
      console.log(error);
      throw new UnauthorizedException('Google authentication failed');
    }
  }

  async checkMfa(email: string) {
    const user = await this.prisma.user.findUnique({
      where: { email: email },
      select: {
        twoFactorEnabled: true,
      },
    });

    if (user.twoFactorEnabled) {
      return true;
    }
    return false;
  }

  async generateTokens(
    user: AuthResult,
    orgId?: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    // Get the user's organization if not provided
    if (!orgId) {
      const dbUser = await this.prisma.user.findUnique({
        where: { id: user.id },
        select: { organizationId: true },
      });
      orgId = dbUser?.organizationId || null;
    }

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      name: user.name,
      orgId,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get('auth.accessTokenKey.secret'),
      expiresIn: '7d',
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get('auth.refreshTokenKey.secret'),
      expiresIn: '30d',
    });

    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        refreshToken,
      },
    });

    return { accessToken, refreshToken };
  }

  async createEmailVerificationToken(userId: string): Promise<string> {
    const verificationToken = randomBytes(32).toString('hex');

    return this.jwtService.signAsync({
      verificationToken,
      userId,
      type: 'email-verification',
    });
  }

  async verifyEmailToken(
    token: string,
  ): Promise<{ userId: string; verificationToken: string }> {
    try {
      const decoded = await this.jwtService.verifyAsync(token);

      if (decoded.type !== 'email-verification') {
        throw new Error('Invalid token type');
      }

      return {
        userId: decoded.userId,
        verificationToken: decoded.verificationToken,
      };
    } catch (error) {
      throw new BadRequestException('Invalid or expired token');
    }
  }

  async refreshTokens(
    data: RefreshDto,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      const { refreshToken } = data;
      const payload = await this.jwtService.verifyAsync(refreshToken, {
        secret: this.configService.get('auth.refreshTokenKey.secret'),
      });

      const user = await this.prisma.user.findFirst({
        where: {
          id: payload.sub,
          refreshToken,
        },
      });

      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Preserve the orgId from the original token if it exists
      const orgId = payload.orgId;

      const newAccessToken = this.jwtService.sign(
        {
          sub: user.id,
          email: user.email,
          name: user.name,
          orgId,
        },
        {
          secret: this.configService.get('auth.accessTokenKey.secret'),
          expiresIn: '7d',
        },
      );
      const newRefreshToken = this.jwtService.sign(
        {
          sub: user.id,
          email: user.email,
          name: user.name,
          orgId,
        },
        {
          secret: this.configService.get('auth.refreshTokenKey.secret'),
          expiresIn: '30d',
        },
      );

      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          refreshToken: newRefreshToken,
        },
      });

      return { accessToken: newAccessToken, refreshToken: newRefreshToken };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async sendResetPasswordEmail(email: string) {
    const user = await this.prisma.user.findUnique({
      where: { email },
      select: { id: true, name: true, email: true, profileImageUrl: true },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const token = this.jwtService.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.name,
        image: user.profileImageUrl,
        type: 'password-reset',
      },
      {
        secret: this.configService.get('auth.accessTokenKey.secret'),
        expiresIn: '1h',
      },
    );

    const resetPasswordUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    await this.emailService.sendResetPasswordEmail(
      email,
      user.name,
      resetPasswordUrl,
    );
  }

  async verifyResetPasswordToken(token: string) {
    try {
      const decoded = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('auth.accessTokenKey.secret'),
      });

      if (decoded.type !== 'password-reset') {
        throw new Error('Invalid token type');
      }

      return {
        userId: decoded.sub,
        email: decoded.email,
        name: decoded.name,
        image: decoded.image,
      };
    } catch (error) {
      throw new BadRequestException('Invalid or expired token');
    }
  }

  async resetPassword(userId: string, newPassword: string) {
    const hashedPassword = await bcrypt.hash(newPassword, this.saltRounds);

    await this.prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        refreshToken: null,
      },
    });
  }

  // Public methods for JWT operations
  async signToken(payload: any, options?: any): Promise<string> {
    return this.jwtService.signAsync(payload, options);
  }

  async verifyToken(token: string, options?: any): Promise<any> {
    return this.jwtService.verifyAsync(token, options);
  }
}
