import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class ZendeskConnectDto {
  @ApiProperty({
    description: 'The email of the user',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The zendesk subdomain for user',
    example: 'xyz',
  })
  @IsString()
  @IsNotEmpty()
  subdomain: string;

  @ApiProperty({
    description: 'The token from Zendesk',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  token: string;
}

export class CrawlZendeskDto {
  @ApiProperty({
    description: 'user connector id',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  userConnectorId: string;
}
