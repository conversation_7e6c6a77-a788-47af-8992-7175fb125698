import { Controller, Get, Post, Query, Body, UseGuards, HttpStatus, HttpException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SlackService } from './slack.v1.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../common/decorators/getUser.decorator';
import { JwtPayload } from '../../common/types/jwt-payload';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SlackCallbackDto, SlackRemoveIntegrationDto, SlackTeamIdDto } from './dtos/slack.v1.dto';
import * as crypto from 'crypto';

@ApiTags('Slack-Integration')
@Controller({ version: '1', path: 'integrations/slack' })
export class SlackController {
    constructor(
        private readonly slackService: SlackService,
        private readonly configService: ConfigService,
    ) { }

    @Post('auth-url')
    @ApiOperation({ summary: 'Generate Slack Bot Auth URL' })
    @ApiResponse({
        status: 200,
        description: 'Successfully generated Slack Bot Auth URL',
    })
    @ApiBearerAuth()
    @UseGuards(JwtAuthGuard)
    async getAuthUrl(@GetCurrentUser() user: JwtPayload) {
        const authUrl = this.slackService.getAuthUrl(user.sub);
        return { authUrl };
    }

    @Post('callback')
    @ApiOperation({ summary: 'Handle Slack Bot OAuth Callback' })
    @ApiResponse({
        status: 200,
        description: 'Successfully processed Slack Bot OAuth Callback',
    })
    @UseGuards(JwtAuthGuard)
    async handleOAuthCallback(@Body() body: SlackCallbackDto, @GetCurrentUser() user: JwtPayload) {

        try {
            const credentials = await this.slackService.handleCallback(body.code, body.state);

            return {
                success: true,
                team: {
                    id: credentials.team_id,
                    name: credentials.team_name,
                },
            };
        } catch (err) {
            console.error('Error in OAuth callback:', err);
            throw new HttpException('Failed to complete Slack integration', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @Post('remove')
    @ApiOperation({ summary: 'Remove Slack Integration' })
    @ApiResponse({
        status: 200,
        description: 'Successfully removed Slack integration',
    })
    @ApiBearerAuth()
    @UseGuards(JwtAuthGuard)
    async removeIntegration(@Body() body: SlackRemoveIntegrationDto, @GetCurrentUser() user: JwtPayload) {
        if (!body.team_id || !user.sub) {
            throw new HttpException('Team ID and User ID are required', HttpStatus.BAD_REQUEST);
        }

        try {
            await this.slackService.deleteCredentials(body.team_id, user.sub);
            return {
                success: true,
                message: 'Slack integration removed successfully',
            };
        } catch (error) {
            console.error('Error removing Slack integration:', error);
            throw new HttpException('Failed to remove Slack integration', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
} 