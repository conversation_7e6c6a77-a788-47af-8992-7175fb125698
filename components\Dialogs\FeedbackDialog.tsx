import { motion } from "framer-motion";
import { But<PERSON>, cn } from "@steps-ai/ui";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@steps-ai/ui";
import { Input } from "@steps-ai/ui";
import { Textarea } from "@steps-ai/ui";
import { useState } from "react";
import { feedbackClient } from "@/lib/feedbackClient";
import { toast } from "sonner";
import { Star } from "lucide-react";

interface FeedbackDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const StarRating = ({ value, onChange }: { value: number; onChange: (rating: number) => void }) => {
  return (
    <div className="flex space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            "w-6 h-6 cursor-pointer transition-colors",
            star <= value ? "text-yellow-500 fill-yellow-500" : "text-gray-300"
          )}
          onClick={() => onChange(star)}
        />
      ))}
    </div>
  );
};

export function FeedbackDialog({ open, onOpenChange }: FeedbackDialogProps) {
  const [feedbackForm, setFeedbackForm] = useState({
    rating: 0,
    message: '',
    email: '',
    area: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await feedbackClient.submitFeedback(
        feedbackForm.message,
        feedbackForm.rating.toString(),
        feedbackForm.email,
        feedbackForm.area
      );
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Share Feedback</DialogTitle>
            <DialogDescription className="text-muted-foreground">
              We value your feedback to improve our service.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4 mt-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Rating</label>
              <StarRating 
                value={feedbackForm.rating} 
                onChange={(rating) => setFeedbackForm({ ...feedbackForm, rating })} 
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="feedback-area" className="text-sm font-medium">Area</label>
              <Input
                id="feedback-area"
                placeholder="Feedback area (e.g., UI, Performance, Features)"
                value={feedbackForm.area}
                onChange={(e) => setFeedbackForm({ ...feedbackForm, area: e.target.value })}
                className="w-full"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="feedback-message" className="text-sm font-medium">Message</label>
              <Textarea
                id="feedback-message"
                placeholder="Share your thoughts..."
                value={feedbackForm.message}
                onChange={(e) => setFeedbackForm({ ...feedbackForm, message: e.target.value })}
                className="min-h-[100px] w-full"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="feedback-email" className="text-sm font-medium">Email (Optional)</label>
              <Input
                id="feedback-email"
                type="email"
                placeholder="<EMAIL>"
                value={feedbackForm.email}
                onChange={(e) => setFeedbackForm({ ...feedbackForm, email: e.target.value })}
                className="w-full"
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button type="submit" className="bg-primary text-primary-foreground hover:bg-primary/90">
                Submit Feedback
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </motion.div>
    </Dialog>
  );
} 