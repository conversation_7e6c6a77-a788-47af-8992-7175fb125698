'use client';

import { useState, useEffect } from 'react';
import { organizationApi, type OrganizationUser, type Team, type UserConnectors, type Connector } from '@/lib/api/organization';
import { useUser } from '@/contexts/UserContext';
import { toast } from 'sonner';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  User,
  Crown,
  Shield,
  Users,
  Clock,
  Mail,
  Calendar,
  Settings,
  Trash2,
  UserCheck,
  Building,
  Link as LinkIcon,
  Loader2
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';

interface UserDetailDrawerProps {
  user: OrganizationUser | null;
  isOpen: boolean;
  onClose: () => void;
  onUserUpdate: () => void;
  organizationId: string;
}

export default function UserDetailDrawer({
  user,
  isOpen,
  onClose,
  onUserUpdate,
  organizationId
}: UserDetailDrawerProps) {
  const [userTeams, setUserTeams] = useState<Team[]>([]);
  const [userConnectors, setUserConnectors] = useState<Connector[]>([]);
  const [isLoadingTeams, setIsLoadingTeams] = useState(false);
  const [isLoadingConnectors, setIsLoadingConnectors] = useState(false);
  const [isUpdatingRole, setIsUpdatingRole] = useState(false);
  const [isRemovingUser, setIsRemovingUser] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const { user: currentUser } = useUser();

  useEffect(() => {
    if (user && isOpen) {
      loadUserTeams();
      loadUserConnectors();
    }
  }, [user, isOpen]);

  const loadUserTeams = async () => {
    if (!user) return;

    try {
      setIsLoadingTeams(true);
      const teams = await organizationApi.getUserTeams(user.id);
      setUserTeams(teams);
    } catch (error) {
      console.error('Failed to load user teams:', error);
      toast.error('Failed to load user teams');
    } finally {
      setIsLoadingTeams(false);
    }
  };

  const loadUserConnectors = async () => {
    if (!user) return;

    try {
      setIsLoadingConnectors(true);
      const userConnectorData = await organizationApi.getUserConnectors(user.id);
      setUserConnectors(userConnectorData.connectors || []);
    } catch (error) {
      console.error('Failed to load user connectors:', error);
      // Don't show error toast for connectors as it might not be critical
      setUserConnectors([]);
    } finally {
      setIsLoadingConnectors(false);
    }
  };

  const handleRoleUpdate = async (newRole: 'ADMIN' | 'TEAMADMIN' | 'USER') => {
    if (!user) return;

    try {
      setIsUpdatingRole(true);
      await organizationApi.updateUserRole(user.id, newRole);
      toast.success(`User role updated to ${newRole}`);
      onUserUpdate();
      onClose();
    } catch (error) {
      console.error('Failed to update user role:', error);
      toast.error('Failed to update user role');
    } finally {
      setIsUpdatingRole(false);
    }
  };

  const handleRemoveUser = async () => {
    if (!user) return;

    try {
      setIsRemovingUser(true);
      // Note: This would require a remove user API endpoint
      // For now, we'll show a placeholder message
      toast.error('Remove user functionality is not yet implemented');
      setShowRemoveDialog(false);
    } catch (error) {
      console.error('Failed to remove user:', error);
      toast.error('Failed to remove user');
    } finally {
      setIsRemovingUser(false);
    }
  };

  const getRoleConfig = (role: OrganizationUser['role']) => {
    switch (role) {
      case 'ADMIN':
        return {
          color: 'bg-red-50 dark:bg-red-950/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800',
          icon: <Crown className="h-4 w-4" />,
          label: 'Administrator'
        };
      case 'TEAMADMIN':
        return {
          color: 'bg-blue-50 dark:bg-blue-950/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800',
          icon: <Shield className="h-4 w-4" />,
          label: 'Team Administrator'
        };
      case 'USER':
        return {
          color: 'bg-green-50 dark:bg-green-950/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800',
          icon: <User className="h-4 w-4" />,
          label: 'User'
        };
      default:
        return {
          color: 'bg-gray-50 dark:bg-gray-950/30 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-800',
          icon: <User className="h-4 w-4" />,
          label: 'Unknown'
        };
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!user) return null;

  const roleConfig = getRoleConfig(user.role);
  const canModifyUser = currentUser.id !== user.id; // Can't modify yourself

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[480px] overflow-y-auto">
        <SheetHeader className="space-y-4 pb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Avatar className="h-16 w-16 border-2 border-border">
                <AvatarImage src={user.profileImageUrl} alt={user.name} />
                <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/20 text-primary text-xl font-semibold">
                  {user.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-background flex items-center justify-center ${roleConfig.color}`}>
                {roleConfig.icon}
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <SheetTitle className="text-xl font-semibold truncate">{user.name}</SheetTitle>
              <SheetDescription className="text-sm text-muted-foreground truncate">{user.email}</SheetDescription>
              <div className="mt-2">
                <span className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium ${roleConfig.color}`}>
                  {roleConfig.icon}
                  {roleConfig.label}
                </span>
              </div>
            </div>
          </div>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* Quick Admin Actions */}
          {canModifyUser && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRoleUpdate(user.role === 'ADMIN' ? 'USER' : 'ADMIN')}
                disabled={isUpdatingRole}
                className="flex-1"
              >
                {isUpdatingRole ? (
                  <Loader2 className="h-3 w-3 animate-spin mr-2" />
                ) : user.role === 'ADMIN' ? (
                  <User className="h-3 w-3 mr-2" />
                ) : (
                  <Crown className="h-3 w-3 mr-2" />
                )}
                {user.role === 'ADMIN' ? 'Remove Admin' : 'Make Admin'}
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setShowRemoveDialog(true)}
                disabled={isRemovingUser}
                className="flex-1"
              >
                <Trash2 className="h-3 w-3 mr-2" />
                Remove User
              </Button>
            </div>
          )}

          {/* User Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Information</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">{user.email}</p>
                  <p className="text-xs text-muted-foreground">Email Address</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">{formatDate(user.lastActive)}</p>
                  <p className="text-xs text-muted-foreground">Last Active</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <UserCheck className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs font-mono text-muted-foreground break-all">{user.id}</p>
                  <p className="text-xs text-muted-foreground">User ID</p>
                </div>
              </div>
            </div>
          </div>

          {/* Teams */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Teams</h3>
              <span className="text-xs text-muted-foreground">{userTeams.length} teams</span>
            </div>
            {isLoadingTeams ? (
              <div className="space-y-2">
                {Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                    <Skeleton className="h-4 w-4 rounded" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                ))}
              </div>
            ) : userTeams.length > 0 ? (
              <div className="space-y-2">
                {userTeams.map((team) => (
                  <div key={team.id} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{team.name}</p>
                      {team.description && (
                        <p className="text-xs text-muted-foreground truncate">{team.description}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <div className="flex flex-col items-center gap-2">
                  <Building className="h-8 w-8 text-muted-foreground/50" />
                  <p className="text-sm text-muted-foreground">No teams assigned</p>
                </div>
              </div>
            )}
          </div>

          {/* Connected Services */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Connected Services</h3>
              <span className="text-xs text-muted-foreground">{userConnectors.length} services</span>
            </div>
            {isLoadingConnectors ? (
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                    <Skeleton className="h-8 w-8 rounded" />
                    <div className="flex-1 space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                    <Skeleton className="h-4 w-12" />
                  </div>
                ))}
              </div>
            ) : userConnectors.length > 0 ? (
              <div className="space-y-2">
                {userConnectors.map((connector) => (
                  <div key={connector.connectorId} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                    <div className="relative">
                      <img
                        src={connector.logo}
                        alt={connector.name}
                        className="h-8 w-8 rounded object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                      <div className="hidden h-8 w-8 bg-primary/10 rounded items-center justify-center">
                        <LinkIcon className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium truncate">{connector.name}</p>
                        <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                          connector.status === 'ACTIVE'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : connector.status === 'ERROR'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                        }`}>
                          {connector.status}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground truncate">{connector.category}</p>
                      <p className="text-xs text-muted-foreground">
                        Connected {new Date(connector.connectedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <div className="flex flex-col items-center gap-2">
                  <LinkIcon className="h-8 w-8 text-muted-foreground/50" />
                  <p className="text-sm text-muted-foreground">No services connected</p>
                </div>
              </div>
            )}
          </div>

        </div>

        {/* Remove User Dialog */}
        <Dialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Remove User</DialogTitle>
              <DialogDescription>
                Are you sure you want to remove {user.name} from the organization?
                This action cannot be undone and will revoke all access immediately.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRemoveDialog(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleRemoveUser}
                disabled={isRemovingUser}
              >
                {isRemovingUser ? 'Removing...' : 'Remove User'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </SheetContent>
    </Sheet>
  );
}
