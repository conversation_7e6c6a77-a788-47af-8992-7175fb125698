import {
  BadRequestException,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateKnowledgeBaseDto } from '../dtos/create-knowledgebase.dto';
import { CommonService } from 'src/common/common.service';
import { UpdateKnowledgeBaseDto } from '../dtos/update-knowledgebase.dto';
import { ShareKnowledgeBaseDto } from '../dtos/share-knowledgebase.dto';
import * as bcrypt from 'bcrypt';
import { randomBytes } from 'crypto';
import { AddFilesDto } from '../dtos/add-files.dto';
import { DeleteFilesDto } from '../dtos/delete-files.dto';
import { EmailService } from 'src/email/email.service';
import { EtlService } from '@/etl/etl.service';
import { EtlStatus, Status, Prisma } from '@prisma/client';
import { ConnectorName } from '@/etl/etl.service';
import { CrawlKnowledgeBaseDto } from '../dtos/crawl-knowledgebase.dto';
import { UpdateStatusDto } from '../dtos/update-status.dto';

interface PaginationQuery {
  page: string;
  pageSize: string;
  search?: string;
  sortBy?: string;
  sortOrder?: string;
}

@Injectable()
export class KnowledgebaseService {
  constructor(
    private prisma: PrismaService,
    private commonService: CommonService,
    private readonly etlService: EtlService,
    private readonly emailService: EmailService,
  ) { }

  async createKnowledgebase(userId: string, data: CreateKnowledgeBaseDto) {
    try {
      const knowledgebase = await this.prisma.knowledgeBase.create({
        data: {
          name: data.name,
          description: data.description,
          owner: {
            connect: {
              id: userId,
            },
          },
        },
      });
      return knowledgebase;
    } catch (error) {
      throw new BadRequestException('An Error occurred');
    }
  }

  async getAllKnowledgebase(userId: string) {
    try {
      const knowledgebases = await this.prisma.user.findUnique({
        where: {
          id: userId,
        },
        select: {
          knowledgeBases: {
            select: {
              id: true,
              name: true,
              owner: {
                select: {
                  name: true,
                },
              },
              files: true,
              createdAt: true,
              updatedAt: true,
              etls: {
                select: {
                  id: true,
                  status: true,
                  startDate: true,
                },
                orderBy: {
                  startDate: 'desc',
                },
                take: 1,
              },
            },
          },
          sharedKnowledgebases: {
            select: {
              id: true,
              name: true,
              owner: {
                select: {
                  name: true,
                },
              },
              files: true,
              createdAt: true,
              updatedAt: true,
              etls: {
                select: {
                  id: true,
                  status: true,
                  startDate: true,
                },
                orderBy: {
                  startDate: 'desc',
                },
                take: 1,
              },
            },
          },
          plan: {
            select: {
              maxKnowledgeBases: true,
              maxStorage: true,
            },
          },
        },
      });

      const mapWithStatus = (kbs: any) =>
        kbs.map((kb: any) => ({
          ...kb,
          status: kb.etls && kb.etls.length > 0 ? kb.etls[0].status : 'NEW',
        }));

      return {
        ...knowledgebases,
        knowledgeBases: mapWithStatus(knowledgebases.knowledgeBases),
        sharedKnowledgebases: mapWithStatus(knowledgebases.sharedKnowledgebases),
      };
    } catch (error) {
      throw new BadRequestException('An Error occurred');
    }
  }

  async getKnowledgebaseById(userId: string, kid: string, query: PaginationQuery) {
    try {
      const knowledgebase = await this.prisma.knowledgeBase.findUnique({
        where: {
          id: kid,
          OR: [{ ownerId: userId }, { members: { some: { id: userId } } }],
        },
        select: {
          id: true,
          name: true,
          description: true,
          owner: {
            select: {
              name: true,
              email: true,
              id: true,
              profileImageUrl: true,
            },
          },
          members: {
            select: {
              name: true,
              email: true,
              id: true,
              profileImageUrl: true,
            },
          },
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!knowledgebase) {
        throw new ForbiddenException('Knowledgebase not found');
      }

      const latestEtl = await this.prisma.etl.findFirst({
        where: { knowledgeBaseId: kid },
        orderBy: { startDate: 'desc' },
        select: {
          id: true,
          status: true,
          startDate: true,
          endDate: true,
        },
      });

      const page = parseInt(query.page, 10);
      const pageSize = parseInt(query.pageSize, 10);
      const skip = (page - 1) * pageSize;
      const where: Prisma.FileWhereInput = {
        knowledgeBaseId: kid,
        ...(query.search && {
          OR: [
            { name: { contains: query.search, mode: Prisma.QueryMode.insensitive } },
            { mimeType: { contains: query.search, mode: Prisma.QueryMode.insensitive } },
          ],
        }),
      };
      const totalFiles = await this.prisma.file.count({ where });
      const totalPages = Math.ceil(totalFiles / pageSize);
      const files = await this.prisma.file.findMany({
        where,
        select: {
          id: true,
          name: true,
          size: true,
          mimeType: true,
          url: true,
          createdAt: true,
          updatedAt: true,
          user: {
            select: {
              name: true,
              email: true,
              profileImageUrl: true,
            },
          },
        },
        skip,
        take: pageSize,
        orderBy: query.sortBy ? { [query.sortBy]: query.sortOrder || 'asc' } : { createdAt: 'desc' },
      });
      const filesData = {
        files,
        totalFiles,
        totalPages,
        currentPage: page,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      const totalFileSize = files.reduce(
        (acc, file) => acc + file.size,
        0,
      );

      const plan = await this.prisma.user.findUnique({
        where: {
          id: userId,
        },
        select: {
          plan: {
            select: {
              maxKnowledgeBases: true,
              maxStorage: true,
            },
          },
        },
      });

      return {
        ...knowledgebase,
        ...filesData,
        shared: knowledgebase.owner.id !== userId,
        totalFileSize,
        plan,
        status: latestEtl?.status ?? EtlStatus.NEW,
      };
    } catch (error) {
      throw new BadRequestException('An Error occurred');
    }
  }

  async updateKnowledgebase(
    userId: string,
    data: UpdateKnowledgeBaseDto,
    kid: string,
  ) {
    try {
      const knowledgebase = await this.prisma.knowledgeBase.findUnique({
        where: {
          id: kid,
          OR: [{ ownerId: userId }, { members: { some: { id: userId } } }],
        },
      });

      if (!knowledgebase) {
        throw new ForbiddenException(
          'Not authorized to update this knowledgebase',
        );
      }

      if (knowledgebase.ownerId !== userId) {
        throw new ForbiddenException(
          'Only the owner can modify knowledgebase details',
        );
      }

      const updatedKnowledgebase = await this.prisma.knowledgeBase.update({
        where: {
          id: kid,
        },
        data: {
          name: data.name,
          description: data.description,
        },
      });
      return updatedKnowledgebase;
    } catch (error) {
      throw new BadRequestException('An Error occurred');
    }
  }

  async deleteKnowledgebase(userId: string, kid: string) {
    try {
      const knowledgebase = await this.prisma.knowledgeBase.findUnique({
        where: {
          id: kid,
          OR: [{ ownerId: userId }, { members: { some: { id: userId } } }],
        },
      });

      if (!knowledgebase) {
        throw new ForbiddenException(
          'Not authorized to update this knowledgebase',
        );
      }

      if (knowledgebase.ownerId !== userId) {
        throw new ForbiddenException(
          'Only the owner can modify knowledgebase details',
        );
      }

      const files = await this.prisma.file.findMany({
        where: {
          knowledgeBaseId: kid,
        },
      });

      const s3DeletePromises = files.map((file) => {
        try {
          const url = new URL(file.url);
          const key = url.pathname.substring(1);
          return this.commonService.deleteFilesFromS3([key]);
        } catch (error) {
          return this.commonService.deleteFilesFromS3([file.url]);
        }
      });

      await Promise.all(s3DeletePromises);

      await this.prisma.file.deleteMany({
        where: {
          knowledgeBaseId: kid,
        },
      });

      await this.prisma.knowledgeBase.delete({
        where: {
          id: kid,
        },
      });

      return 'Successfully deleted knowledgebase';
    } catch (error) {
      throw new BadRequestException('An Error occurred');
    }
  }

  async shareKnowledgebase(userId: string, data: ShareKnowledgeBaseDto) {
    try {
      const knowledgebase = await this.prisma.knowledgeBase.findUnique({
        where: {
          id: data.kid,
          OR: [{ ownerId: userId }, { members: { some: { id: userId } } }],
        },
      });
      if (!knowledgebase) {
        throw new ForbiddenException(
          'Not authorized to share this knowledgebase',
        );
      }

      const user = await this.prisma.user.findUnique({
        where: {
          email: data.email,
        },
      });

      if (user) {
        await this.prisma.knowledgeBase.update({
          where: {
            id: data.kid,
          },
          data: {
            members: {
              connect: {
                id: user.id,
              },
            },
          },
        });
        await this.emailService.sendShareKnowledgebaseEmail(
          data.email,
          user.name,
          knowledgebase.name,
        );
        return 'Successfully shared knowledgebase';
      }

      const password = this.generateRandomPassword();
      const newUser = await this.prisma.user.create({
        data: {
          email: data.email,
          password: password,
        },
      });

      await this.prisma.knowledgeBase.update({
        where: {
          id: data.kid,
        },
        data: {
          members: {
            connect: {
              id: newUser.id,
            },
          },
        },
      });
      await this.emailService.sendShareKnowledgebaseNewUserEmail(
        data.email,
        user.name,
        knowledgebase.name,
        password,
      );
      return 'Successfully shared knowledgebase';
    } catch (error) {
      throw new BadRequestException('An Error occurred');
    }
  }
  async deleteFilesFromKnowledgeBase(userId: string, data: DeleteFilesDto) {
    try {
      const knowledgeBase = await this.prisma.knowledgeBase.findUnique({
        where: { id: data.kid },
        include: { owner: true },
      });

      if (!knowledgeBase || knowledgeBase.ownerId !== userId) {
        throw new Error('Knowledge base not found or access denied');
      }

      await this.commonService.deleteFilesFromS3(
        data.files.map((file) => file.url),
      );

      await this.prisma.file.deleteMany({
        where: {
          knowledgeBaseId: data.kid,
          id: { in: data.files.map((file) => file.id) },
        },
      });
      return 'Successfully deleted files';
    } catch (error) {
      console.error('Failed to delete files:', error);
      throw new BadRequestException('An Error occurred');
    }
  }
  async addFilesToKnowledgeBase(userId: string, data: AddFilesDto) {
    const { kid, files } = data;

    const knowledgeBase = await this.prisma.knowledgeBase.findUnique({
      where: { id: kid },
      include: { owner: true, organization: { select: { id: true } } },
    });

    if (!knowledgeBase || knowledgeBase.ownerId !== userId) {
      throw new Error('Knowledge base not found or access denied');
    }

    const BATCH_SIZE = 500;
    const fileChunks = Array(Math.ceil(files.length / BATCH_SIZE))
      .fill(null)
      .map((_, index) =>
        files.slice(index * BATCH_SIZE, (index + 1) * BATCH_SIZE),
      );

    const results = [];
    for (const fileChunk of fileChunks) {
      await this.prisma.$transaction(async (tx) => {
        const createdFiles = await tx.file.createMany({
          data: fileChunk.map((file) => ({
            name: Buffer.from(file.name, 'latin1').toString('utf-8'),
            size: file.size,
            mimeType: file.mimeType,
            url: file.url,
            knowledgeBaseId: kid,
            userId,
            status: 'PROCESSING',
          })),
        });

        results.push(createdFiles);
      });
    }

    try {
      const s3Uris = files.map((file) => {
        try {
          const url = new URL(file.url);
          const pathParts = url.pathname.split('/');
          const bucketName = url.hostname.split('.')[0];
          const key = pathParts.slice(1).join('/');
          return file.url;
        } catch (error) {
          console.error('Error parsing URL:', file.url, error);
          return file.url;
        }
      });

      const etlRun = await this.prisma.etl.findFirst({
        where: {
          knowledgeBaseId: kid,
        },
      });


      if (etlRun && etlRun.status === EtlStatus.FAILED) {
        await this.prisma.etl.delete({
          where: {
            id: etlRun.id,
          },
        });
      }

      const etlResponse = await this.etlService.publish(
        userId,
        ConnectorName.KNOWLEDGE_BASE,
        kid,
        knowledgeBase.id,
        knowledgeBase.id,
        {
          s3_uris: s3Uris,
        },
      );


      await this.prisma.etl.create({
        data: {
          taskId: etlResponse.taskId,
          status: EtlStatus.QUEUED,
          startDate: new Date().toISOString(),
          knowledgeBaseId: kid,
        },
      });
    } catch (error) {
      console.error('Failed to publish etl:', error);
    }

    return {
      message: 'Files added successfully',
      totalFiles: files.length,
      results,
    };
  }

  generateRandomPassword = () => {
    const randomPassword = randomBytes(16).toString('base64');
    const saltRounds = 10;
    return bcrypt.hashSync(randomPassword, saltRounds);
  };

  async getCrawlStatus(userId: string, kid: string) {
    const etlRun = await this.prisma.etl.findFirst({
      where: {
        userConnectorId: kid,
      },
    });
    if (!etlRun) {
      throw new BadRequestException('Crawl not found');
    }
    // const status = await this.etlService.getStatus(etlRun.dagRunId);
    // await this.prisma.etl.update({
    //   where: {
    //     id: etlRun.id,
    //   },
    //   data: {
    //     status: EtlStatus[status.toUpperCase()],
    //   },
    // });
    return {
      status: etlRun.status,
    };
  }

  async updateStatus(data: UpdateStatusDto) {
    const { id, status } = data;
    await this.prisma.knowledgeBase.update({
      where: { id },
      data: { status: Status[status.toUpperCase()] },
    });
    await this.prisma.etl.update({
      where: { taskId: id },
      data: { status: EtlStatus[status.toUpperCase()] },
    });
    return 'Status updated successfully';
  }
}