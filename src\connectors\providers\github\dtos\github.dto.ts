import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class GitHubCallbackDto {
  @ApiProperty({
    description: 'The code received from GitHub',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'The state received from GitHub',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  state: string;
}
