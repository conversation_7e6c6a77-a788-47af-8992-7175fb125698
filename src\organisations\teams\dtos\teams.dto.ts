import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>D, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTeamDto {
  @ApiProperty({ description: 'Name of the team' })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the team',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Organization ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  organizationId?: string;

  @ApiProperty({
    description: 'Team members',
    required: false,
  })
  @IsArray()
  @IsOptional()
  members?: string[];
}

export class UpdateTeamDto {
  @ApiProperty({ description: 'Name of the team' })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the team',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class AddMemberToTeamDto {
  @ApiProperty({ description: 'Member ID' })
  @IsUUID()
  memberId: string;
}

export class AssignTeamLeaderDto {
  @ApiProperty({
    description: 'User ID of the team member to be promoted to TeamAdmin',
    example: 'user-uuid'
  })
  @IsUUID()
  userId: string;
}
