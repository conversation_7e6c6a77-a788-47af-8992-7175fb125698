export interface Config {
  nest: NestConfig;
  cors: CorsConfig;
  swagger: SwaggerConfig;
  auth: AuthConfig;
  resend: ResendConfig;
  encryption: EncryptionConfig;
  environment: EnvironmentConfig;
  slack: SlackConfig;
  github: GitHubConfig;
  notion: NotionConfig;
  hubspot: HubspotConfig;
  airflow: AirflowConfig;
  slackIntegration: SlackIntegrationConfig;
  celery: CeleryConfig;
  milvus: MilvusConfig;
}

export interface NestConfig {
  port: number;
}

export interface CorsConfig {
  enabled: boolean;
}

export interface SwaggerConfig {
  enabled: boolean;
  title: string;
  description: string;
  version: string;
  path: string;
}

export interface AuthConfig {
  accessTokenKey: JwtConfig;
  refreshTokenKey: JwtConfig;
  google: GoogleConfig;
  microsoft: MicrosoftConfig;
  jwt: JwtConfig;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
}

export interface GoogleConfig {
  clientId: string;
}

export interface ResendConfig {
  apiKey: string;
}

export interface GoogleConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface EncryptionConfig {
  key: string;
}

export interface EnvironmentConfig {
  environment: string;
}

export interface SlackConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface SlackIntegrationConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface GitHubConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface MicrosoftConfig {
  clientId: string;
  clientSecret: string;
  tenantId: string;
}

export interface NotionConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface HubspotConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  appId: string;
}

export interface AirflowConfig {
  username: string;
  password: string;
  baseUrl: string;
}

export interface CeleryConfig {
  baseUrl: string;
}

export interface MilvusConfig {
  baseUrl: string;
}
