{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src//.ts\" \"test//.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:migrate:prod": "prisma migrate deploy && npm run start:prod", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}//*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "create-admin": "ts-node src/scripts/create-admin.ts", "prisma:seed": "ts-node --compiler-options \"{\\\"module\\\":\\\"CommonJS\\\"}\" prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.731.1", "@aws-sdk/client-s3": "^3.701.0", "@aws-sdk/lib-dynamodb": "^3.731.1", "@aws-sdk/s3-request-presigner": "^3.817.0", "@azure/msal-node": "^3.2.1", "@nestjs/common": "^11.0.13", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.13", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.4", "@nestjs/platform-express": "^11.0.12", "@nestjs/swagger": "^11.1.1", "@prisma/client": "^5.22.0", "backend": "file:", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "google-auth-library": "^9.15.0", "googleapis": "^144.0.0", "ioredis": "^5.4.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.0", "resend": "^4.0.1", "rxjs": "^7.8.1", "speakeasy": "^2.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^11.0.12", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.22.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}