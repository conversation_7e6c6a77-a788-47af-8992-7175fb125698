{"name": "web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start:migrate:prod": "node server.js", "docker:dev": "npm run dev", "docker:compose:dev": "docker-compose down && docker compose up -d --build", "lint": "next lint", "email": "email dev --port 3001"}, "dependencies": {"@ai-sdk/google": "^0.0.52", "@auth/prisma-adapter": "^2.9.0", "@aws-sdk/client-dynamodb": "^3.797.0", "@aws-sdk/client-s3": "^3.797.0", "@aws-sdk/s3-request-presigner": "^3.797.0", "@azure/msal-browser": "^4.11.0", "@calcom/embed-react": "^1.5.2", "@hookform/resolvers": "^3.10.0", "@portabletext/react": "^3.2.1", "@portabletext/types": "^2.0.13", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-tooltip": "^1.2.4", "@react-email/components": "0.0.22", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/highlight": "^3.12.0", "@react-pdf-viewer/search": "^3.12.0", "@react-pdf/renderer": "^4.3.0", "@steps-ai/ui": "^1.1.19", "@tanstack/react-query": "^5.74.7", "@types/lodash": "^4.17.16", "@types/uuid": "^10.0.0", "ai": "^3.4.33", "airtable": "^0.12.2", "aos": "^2.3.4", "axios": "^1.9.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cobe": "^0.6.3", "date-fns": "^4.1.0", "formik": "^2.4.6", "framer-motion": "^11.18.2", "fuse.js": "^7.1.0", "http2": "^3.3.7", "ioredis": "^5.6.1", "katex": "^0.16.22", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.414.0", "marked": "^15.0.11", "net": "^1.0.2", "next": "14.2.5", "next-auth": "^4.24.11", "next-sanity": "^9.10.2", "next-themes": "^0.3.0", "nextjs-toploader": "^3.8.16", "openai": "^4.96.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^3.11.174", "posthog-js": "^1.236.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-email": "2.1.6", "react-google-drive-picker": "^1.2.2", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-joyride": "^2.9.3", "react-markdown": "^9.1.0", "react-pdf": "^9.2.1", "react-pdf-highlighter": "8.0.0-rc.0", "react-query": "^3.39.3", "react-resizable-panels": "^2.1.9", "react-select": "^5.10.1", "react-spring": "^9.7.5", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "rehype-mathjax": "^6.0.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "slug": "^9.1.0", "sonner": "^1.7.4", "styled-components": "^6.1.17", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "web-app": "file:", "yup": "^1.6.1", "zod": "^3.24.3", "zustand": "^4.5.6"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/aos": "^3.0.7", "@types/bcrypt": "^5.0.2", "@types/node": "^20.17.32", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/slug": "^5.0.9", "eslint": "^8.57.1", "eslint-config-next": "14.2.5", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}