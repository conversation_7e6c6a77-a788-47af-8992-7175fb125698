import { Module } from '@nestjs/common';
import { ActivitiesController } from './v1/activities.v1.controller';
import { ActivitiesService } from './v1/activities.v1.service';
import { ActivityLoggerService } from './activity-logger.service';
import { PrismaModule } from 'src/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [ActivitiesController],
  providers: [ActivitiesService, ActivityLoggerService],
  exports: [ActivitiesService, ActivityLoggerService],
})
export class ActivitiesModule {}
