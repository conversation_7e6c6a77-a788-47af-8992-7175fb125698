'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import BlobBackground from '@/components/Organization/BlobBackground';
import {
    UserPlus,
    Upload,
    FileText,
    Mail,
    X,
    Check,
    AlertCircle,
    Users,
    Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { organizationApi } from '@/lib/api/organization';

interface AddUserModalProps {
    isOpen: boolean;
    onClose: () => void;
    organizationId: string;
    onUserAdded: () => void;
}

interface ImportedEmail {
    email: string;
    isValid: boolean;
    source: 'csv' | 'manual';
}

export default function AddUserModal({
    isOpen,
    onClose,
    organizationId,
    onUserAdded
}: AddUserModalProps) {
    const [activeTab, setActiveTab] = useState('manual');
    const [emails, setEmails] = useState<ImportedEmail[]>([]);
    const [manualInput, setManualInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email.trim());
    };

    const handleCsvUpload = async (file: File) => {
        setIsLoading(true);
        try {
            const content = await file.text();
            const lines = content.split('\n').filter(line => line.trim());

            if (lines.length === 0) {
                throw new Error('File is empty');
            }

            const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
            const emailColumnIndex = headers.findIndex(h => h.includes('email'));

            if (emailColumnIndex === -1) {
                throw new Error('No email column found');
            }

            const extractedEmails: ImportedEmail[] = [];
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(v => v.trim());
                const email = values[emailColumnIndex]?.trim();

                if (email) {
                    extractedEmails.push({
                        email,
                        isValid: validateEmail(email),
                        source: 'csv'
                    });
                }
            }

            setEmails(extractedEmails);
            toast.success(`Extracted ${extractedEmails.length} emails from CSV`);
        } catch (error) {
            toast.error(error instanceof Error ? error.message : 'Failed to process CSV');
        } finally {
            setIsLoading(false);
        }
    };

    const handleManualAdd = () => {
        if (!manualInput.trim()) return;

        const emailList = manualInput
            .split(/[,;\n\s]+/)
            .map(email => email.trim())
            .filter(email => email.length > 0);

        const newEmails: ImportedEmail[] = emailList.map(email => ({
            email,
            isValid: validateEmail(email),
            source: 'manual'
        }));

        setEmails(prev => [...prev, ...newEmails]);
        setManualInput('');
        toast.success(`Added ${newEmails.length} emails`);
    };

    const handleRemoveEmail = (index: number) => {
        setEmails(prev => prev.filter((_, i) => i !== index));
    };

    const handleSubmit = async () => {
        const validEmails = emails.filter(email => email.isValid);

        if (validEmails.length === 0) {
            toast.error('No valid emails to send invitations');
            return;
        }

        setIsSubmitting(true);

        try {
            await organizationApi.inviteUsers({
                emails: validEmails.map(e => e.email),
                organizationId
            });

            toast.success(`Invitations sent to ${validEmails.length} user${validEmails.length > 1 ? 's' : ''}`);
            onUserAdded();
            handleClose();
        } catch (error: any) {
            console.error('Error sending invitations:', error);
            toast.error(error.response?.data?.message || 'Failed to send invitations. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleClose = () => {
        if (!isSubmitting) {
            setEmails([]);
            setManualInput('');
            setActiveTab('manual');
            onClose();
        }
    };

    const validEmailsCount = emails.filter(email => email.isValid).length;
    const invalidEmailsCount = emails.length - validEmailsCount;

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-4xl p-0 overflow-hidden bg-background border shadow-xl rounded-lg">
                <div className="flex h-[520px] md:h-[620px]">
                    <div className="w-2/5 hidden md:block">
                        <BlobBackground
                            colorScheme="blue-purple"
                            className="h-full rounded-l-lg"
                        >
                            <div className="text-center space-y-6 px-4">
                                <div className="w-16 h-16 mx-auto bg-white/20 rounded-lg backdrop-blur-sm flex items-center justify-center mb-6 animate-bounce-slow">
                                    <UserPlus className="h-8 w-8 text-text" />
                                </div>

                                <div className="space-y-4">
                                    <blockquote className="text-text text-lg font-medium leading-relaxed">
                                        "Great things in business are never done by one person. They're done by a team of people."
                                    </blockquote>
                                    <cite className="text-text/70 text-sm font-normal">
                                        — Steve Jobs
                                    </cite>
                                </div>

                                {/* <div className="grid grid-cols-2 gap-3 mt-8">
                                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                                        <div className="text-xl font-bold text-white">{emails.length}</div>
                                        <div className="text-white/70 text-xs">Added</div>
                                    </div>
                                    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                                        <div className="text-xl font-bold text-white">{validEmailsCount}</div>
                                        <div className="text-white/70 text-xs">Valid</div>
                                    </div>
                                </div> */}
                            </div>
                        </BlobBackground>
                    </div>

                    {/* Right Panel - Interactive Section */}
                    <div className="w-full md:w-3/5 p-6 bg-background">
                        <DialogHeader className="mb-6">
                            <DialogTitle className="text-xl font-bold text-foreground">
                                Invite Team Members
                            </DialogTitle>
                            <p className="text-muted-foreground text-sm">
                                Choose your preferred method to add new members
                            </p>
                        </DialogHeader>

                        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-5">
                            <TabsList className="grid w-full grid-cols-2 bg-muted p-1 rounded-lg">
                                <TabsTrigger
                                    value="manual"
                                    className="data-[state=active]:bg-background data-[state=active]:shadow-md transition-all duration-300 rounded-md font-medium text-sm"
                                >
                                    <Mail className="h-4 w-4 mr-2" />
                                    Manual Entry
                                </TabsTrigger>
                                <TabsTrigger
                                    value="csv"
                                    className="data-[state=active]:bg-background data-[state=active]:shadow-md transition-all duration-300 rounded-md font-medium text-sm"
                                >
                                    <FileText className="h-4 w-4 mr-2" />
                                    CSV Upload
                                </TabsTrigger>
                            </TabsList>

                            <TabsContent value="manual" className="space-y-4">
                                <div className="relative">
                                    <div className="absolute inset-0 bg-muted/30 rounded-lg"></div>
                                    <div className="relative p-4 space-y-3">
                                        <div className="flex items-center gap-2 mb-3">
                                            <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
                                                <Mail className="h-3 w-3 text-primary-foreground" />
                                            </div>
                                            <h3 className="font-medium text-foreground text-sm">Manual Entry</h3>
                                        </div>
                                        <Textarea
                                            placeholder="Paste your email list here...&#10;<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                                            value={manualInput}
                                            onChange={(e) => setManualInput(e.target.value)}
                                            className="min-h-[120px] resize-none text-sm"
                                        />
                                        <Button
                                            onClick={handleManualAdd}
                                            disabled={!manualInput.trim() || isLoading}
                                            className="w-full font-medium py-2 text-sm"
                                        >
                                            <Plus className="h-4 w-4 mr-2" />
                                            Add Emails to List
                                        </Button>
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="csv" className="space-y-4">
                                <div className="relative">
                                    <div className="absolute inset-0 bg-muted/30 rounded-lg"></div>
                                    <div className="relative p-4 space-y-3">
                                        <div className="flex items-center gap-2 mb-3">
                                            <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
                                                <FileText className="h-3 w-3 text-primary-foreground" />
                                            </div>
                                            <h3 className="font-medium text-foreground text-sm">CSV Upload</h3>
                                        </div>

                                        <div
                                            className="relative border-2 border-dashed border-border rounded-lg p-8 text-center hover:border-primary hover:bg-muted/50 transition-all duration-300 cursor-pointer group"
                                            onClick={() => {
                                                const input = document.createElement('input');
                                                input.type = 'file';
                                                input.accept = '.csv';
                                                input.onchange = (e) => {
                                                    const file = (e.target as HTMLInputElement).files?.[0];
                                                    if (file) {
                                                        handleCsvUpload(file);
                                                    }
                                                };
                                                input.click();
                                            }}
                                        >
                                            <div className="relative z-10">
                                                <div className="w-12 h-12 mx-auto mb-4 bg-primary rounded-lg flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
                                                    <Upload className="h-6 w-6 text-primary-foreground" />
                                                </div>
                                                <h4 className="text-lg font-bold text-foreground mb-2">
                                                    {isLoading ? 'Processing Your File...' : 'Drop Your CSV Here'}
                                                </h4>
                                                <p className="text-muted-foreground mb-3 text-sm">
                                                    Or click to browse and select your file
                                                </p>

                                                {isLoading && (
                                                    <div className="mt-4 flex flex-col items-center gap-2">
                                                        <div className="w-6 h-6 border-2 border-muted border-t-primary rounded-full animate-spin" />
                                                        <span className="text-xs font-medium text-primary">Extracting emails...</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>

                            {emails.length > 0 && (
                                <div className="space-y-3 pt-4 border-t border-border">
                                    <div className="flex items-center justify-between">
                                        <h3 className="font-semibold text-foreground flex items-center gap-2 text-sm">
                                            <div className="w-5 h-5 bg-primary rounded flex items-center justify-center">
                                                <Check className="h-3 w-3 text-primary-foreground" />
                                            </div>
                                            Ready to Send ({emails.length})
                                        </h3>
                                        <div className="flex gap-2">
                                            {validEmailsCount > 0 && (
                                                <Badge className="bg-primary text-primary-foreground text-xs">
                                                    {validEmailsCount} valid
                                                </Badge>
                                            )}
                                            {invalidEmailsCount > 0 && (
                                                <Badge variant="destructive" className="text-xs">
                                                    {invalidEmailsCount} invalid
                                                </Badge>
                                            )}
                                        </div>
                                    </div>

                                    <div className="max-h-32 overflow-y-auto space-y-1 bg-muted/30 rounded-lg p-3">
                                        {emails.map((email, index) => (
                                            <div
                                                key={index}
                                                className={`flex items-center justify-between p-2 rounded-lg text-xs transition-all duration-200 ${
                                                    email.isValid
                                                        ? 'bg-background border border-border shadow-sm'
                                                        : 'bg-destructive/10 border border-destructive/20 shadow-sm'
                                                }`}
                                            >
                                                <div className="flex items-center gap-2 flex-1 min-w-0">
                                                    <div className={`w-4 h-4 rounded-full flex items-center justify-center ${
                                                        email.isValid
                                                            ? 'bg-primary'
                                                            : 'bg-destructive'
                                                    }`}>
                                                        {email.isValid ? (
                                                            <Check className="h-2 w-2 text-primary-foreground" />
                                                        ) : (
                                                            <AlertCircle className="h-2 w-2 text-destructive-foreground" />
                                                        )}
                                                    </div>
                                                    <span className="truncate font-medium text-foreground">{email.email}</span>
                                                </div>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleRemoveEmail(index)}
                                                    className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive rounded transition-all duration-200"
                                                >
                                                    <X className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            <div className="flex gap-3 pt-4">
                                <Button
                                    variant="outline"
                                    onClick={handleClose}
                                    disabled={isSubmitting}
                                    className="flex-1 font-medium py-2 text-sm"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleSubmit}
                                    disabled={validEmailsCount === 0 || isSubmitting}
                                    className="flex-1 font-semibold py-2 text-sm"
                                >
                                    {isSubmitting ? (
                                        <>
                                            <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin mr-2" />
                                            Sending...
                                        </>
                                    ) : (
                                        <>
                                            <Mail className="h-4 w-4 mr-2" />
                                            Send {validEmailsCount} Invitation{validEmailsCount !== 1 ? 's' : ''}
                                        </>
                                    )}
                                </Button>
                            </div>
                        </Tabs>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
