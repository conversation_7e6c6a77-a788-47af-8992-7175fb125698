"use client";

import React from 'react';
import AppProviders from '../AppProviders';
import { cn } from '@/lib/utils';
import useChatStore from '@/store/chatStore';
import Sources from '@/components/Sources';

interface ChatLayoutProps {
  children: React.ReactNode;
}

export default function ChatLayout({ children }: ChatLayoutProps) {
  const { currentChat, sourcesViewId } = useChatStore();

  return (
    <AppProviders>
      <div className="flex flex-1 h-full overflow-hidden relative">
        <div className={cn(
          currentChat && currentChat.chat_id && sourcesViewId ? "flex-[3] h-full transition-all duration-500 ease-in-out" : "flex-1 h-full transition-all duration-500 ease-in-out"
        )}>
          {children}
        </div>
        {currentChat && currentChat.chat_id && sourcesViewId && (
          <div className="flex-[1] h-full opacity-100 transition-all duration-500 ease-in-out">
            <Sources />
          </div>
        )}
      </div>
    </AppProviders>
  );
} 