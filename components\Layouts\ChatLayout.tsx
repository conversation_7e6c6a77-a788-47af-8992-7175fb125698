"use client";

import React from 'react';
import AppProviders from '../AppProviders';
import { SourcesDrawer } from '../SourcesDrawer/SourcesDrawer';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useSourcesDrawer } from '@/store/sourcesDrawerStore';
import { cn } from '@/lib/utils';

interface ChatLayoutProps {
  children: React.ReactNode;
}

export default function ChatLayout({ children }: ChatLayoutProps) {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');
  const { isOpen } = useSourcesDrawer();

  return (
    <AppProviders>
      <div className="flex flex-1 h-full overflow-hidden relative">
        <div className={cn(
          "flex-1 h-full transition-all duration-300 ease-in-out",
          isOpen && !isTablet ? "pr-[320px]" : "pr-0"
        )}>
          {children}
        </div>
        <div className={cn(
          "transition-transform duration-300 ease-in-out",
          isTablet 
            ? "fixed inset-x-0 bottom-0 z-50 h-[60vh]"
            : "fixed top-0 right-0 h-full w-[320px] z-50",
          isOpen 
            ? isTablet ? "translate-y-0" : "translate-x-0"
            : isTablet ? "translate-y-full" : "translate-x-full"
        )}>
          <SourcesDrawer isMobile={isMobile} isTablet={isTablet} />
        </div>
      </div>
    </AppProviders>
  );
} 