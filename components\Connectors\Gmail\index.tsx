"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ConnectorProps } from '../types';
import {
    Loader2,
    CheckCircle2,
    MessageSquare,
    Power,
    FileText,
    ArrowRight,
    Shield,
    LogIn,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { queryClient, createMutationFn, useApiMutation, useApiQuery } from "@/lib/apiClient";
import { useRouter } from "next/navigation";

interface GoogleUser {
    email: string
    name: string
    photoUrl: string
}

const LoadingSkeleton = () => (
    <div className="space-y-8 p-6 bg-card rounded-lg border shadow-sm dark:border-border animate-pulse">
        <div className="flex items-center justify-between">
            <div className="space-y-2">
                <div className="h-6 w-48 bg-muted rounded-md" />
                <div className="h-4 w-72 bg-muted rounded-md" />
            </div>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
            {[1, 2].map((i) => (
                <div key={i} className="space-y-6">
                    <div className="w-48 h-48 mx-auto bg-muted rounded-lg" />
                    <div className="space-y-2">
                        <div className="h-5 w-40 mx-auto bg-muted rounded-md" />
                        <div className="h-4 w-56 mx-auto bg-muted rounded-md" />
                    </div>
                </div>
            ))}
        </div>

        <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border flex justify-between items-center">
            <div className="flex items-center gap-4">
                <div className="h-6 w-6 bg-muted rounded-full" />
                <div className="h-4 w-64 bg-muted rounded-md" />
            </div>
            <div className="h-10 w-40 bg-muted rounded-md" />
        </div>
    </div>
)

export default function GmailConnector({ data }: ConnectorProps) {
    const router = useRouter()
    // const { setSelectedConnectors, setShowSelected } = useUI();
    const [state, setState] = useState<"disconnected" | "connecting" | "connected">(
        data?.isConnected ? "connected" : "disconnected"
    )
    const [user, setUser] = useState<GoogleUser | null>(
        data?.config ? {
            email: data.config.email,
            name: data.config.name,
            photoUrl: data.config.picture
        } : null
    )
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [isDisconnecting, setIsDisconnecting] = useState(false)
    const isHandledRef = useRef(false)

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const state = urlParams.get("state");

        if (code && state && !isHandledRef.current) {
            isHandledRef.current = true;
            handleOAuthCallback(code, state);
        }

        return () => {
            // No need for cleanup as the ref persists between re-renders
        };
    }, []);

    const connectGmail = useApiMutation(
        createMutationFn.post('/gmail/callback'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['gmail'] });
                    setUser({
                        email: response.result.data.email,
                        name: response.result.data.name,
                        photoUrl: response.result.data.picture
                    })
                    setState("connected")
                    setError(null)

                    // Clear the URL parameters after successful connection
                    window.history.replaceState({}, document.title, window.location.pathname);
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error setting up the request")
                }
                setState("disconnected")
            }
        }
    );

    const handleOAuthCallback = async (code: string, state: string) => {
        setState("connecting")
        try {
            await connectGmail.mutateAsync({ code, state })
        } finally {
            window.history.replaceState({}, document.title, window.location.pathname)
        }
    }

    const getAuthUrl = useApiMutation(
        createMutationFn.post('/gmail/auth-url'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['gmail-auth-url'] });
                    console.log('Auth URL response:', response.result); // Debug log
                    router.push(response.result)
                    setError(null)
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error setting up the request")
                }
            }
        }
    );

    const handleConnect = async () => {
        setIsLoading(true)
        await getAuthUrl.mutateAsync({})
        if (!getAuthUrl.isPending) {
            setIsLoading(false)
        }
    }

    const disconnectGmail = useApiMutation(
        createMutationFn.post('/gmail/disconnect'),
        {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['gmail'] });
                setState("disconnected")
                setUser(null)
                setError(null)
                setIsDisconnecting(false)
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong while disconnecting."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error disconnecting from Gmail")
                }
                setIsDisconnecting(false)
            }
        }
    );

    const handleDisconnect = async () => {
        setIsDisconnecting(true)
        await disconnectGmail.mutateAsync({})
    }

    const handleStartChatting = () => {
        // setSelectedConnectors((prev: string[]) => [...prev, "Gmail"]);
        // setShowSelected(true);
        router.push('/chat');
    };

    if (state === "connecting") {
        return (
            <div className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Connecting to Gmail...</span>
            </div>
        )
    }

    if (state === "connected" && user) {
        return (
            <div className="space-y-4 p-4 bg-card rounded-lg border shadow-sm dark:border-border">
                <div className="flex items-center gap-4 bg-muted/50 p-4 rounded-lg dark:bg-muted/10">
                    <Image src={user.photoUrl} alt={user.name} width={56} height={56} className="rounded-full ring-2 ring-primary/10 dark:ring-primary/20" />
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg">{user.name}</h3>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                    </div>
                    <div className="flex flex-col items-center gap-1">
                        <CheckCircle2 className="h-6 w-6 text-green-500 dark:text-green-400" />
                        <span className="text-xs text-muted-foreground">Connected</span>
                    </div>
                </div>

                <div className="flex flex-col md:flex-row items-center gap-8 w-full p-6 rounded-xl hover:bg-muted/50 transition-colors border border-primary/10 dark:border-primary/20">
                    <div className="relative w-28 h-28 flex-shrink-0 flex items-center justify-center">
                        <div className="absolute inset-0 bg-primary/5 dark:bg-primary/10 rounded-full blur-xl" />
                        <Image
                            src="/new-logo.svg"
                            alt="AI Logo"
                            width={70}
                            height={70}
                            className="relative"
                        />
                    </div>
                    <div className="flex-grow text-center md:text-left">
                        <h3 className="font-semibold text-lg bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
                            Continue to Chat
                        </h3>
                        <div className="space-y-2 text-sm text-muted-foreground mt-3">
                            <p>✨ Ask questions about your emails</p>
                            <p>🔍 Search across your inbox</p>
                            <p>📊 Get instant email summaries and insights</p>
                        </div>
                    </div>
                    <div
                        onClick={handleStartChatting}
                        className="flex-shrink-0 w-full md:w-auto cursor-pointer"
                    >
                        <Button className="group bg-primary text-primary-foreground hover:bg-primary/90 w-full md:w-auto">
                            Start Chatting
                            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                    </div>
                </div>
                <div className="flex justify-between items-center mt-4">
                    {/* <Button
                        onClick={handleStartChatting}
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                    >
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Start Chatting
                    </Button> */}

                    <Button
                        onClick={handleDisconnect}
                        variant="outline"
                        className="hover:bg-destructive hover:text-destructive-foreground"
                        disabled={isDisconnecting}
                    >
                        {isDisconnecting ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Disconnecting...
                            </>
                        ) : (
                            <>
                                <Power className="w-4 h-4 mr-2" />
                                Disconnect
                            </>
                        )}
                    </Button>
                </div>
            </div>
        )
    }

    if (isLoading) {
        return <LoadingSkeleton />
    }

    return (
        <div className="space-y-8 p-6 bg-card rounded-lg border shadow-sm dark:border-border">
            <div className="flex items-center justify-between">
                <div className="space-y-2">
                    <h3 className="font-semibold text-lg">Connect Gmail</h3>
                    <p className="text-sm text-muted-foreground">Access your emails with AI-powered insights</p>
                </div>
            </div>
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-lg">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                    <span className="font-medium">🔒 Verification Notice:</span>
                    {" "}Our app is currently under Google's verification process. You may see an "unverified app" screen – this is a standard security measure. Our application is completely safe to use and follows all Google's security guidelines. You can proceed with confidence.
                </p>
            </div>
            <div className="grid gap-8 md:grid-cols-2">
                <div className="relative">
                    <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl" />
                    <div className="relative space-y-6">
                        <img
                            src="/email.svg"
                            alt="Search Emails"
                            className="w-48 h-48 mx-auto"
                        />
                        <div className="space-y-2">
                            <h3 className="font-semibold text-center">Smart Email Search</h3>
                            <p className="text-sm text-muted-foreground text-center">
                                Find any information across your emails instantly
                            </p>
                        </div>
                    </div>
                </div>

                <div className="relative">
                    <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl" />
                    <div className="relative space-y-6">
                        <Image
                            src="/new-logo.svg"
                            alt="AI Logo"
                            width={60}
                            height={60}
                            className="mx-auto w-48 h-48"
                        />
                        <div className="space-y-2">
                            <h3 className="font-semibold text-center">AIDE - Email Intelligence</h3>
                            <p className="text-sm text-muted-foreground text-center">
                                Get instant insights and summaries from your emails
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {error && (
                <div className="bg-destructive/10 text-destructive dark:bg-destructive/20 p-3 rounded-md text-sm">
                    {error}
                </div>
            )}

            <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border border-primary/10 dark:border-primary/20 flex justify-between items-center">
                <div className="flex items-center gap-4">
                    <Shield className="h-6 w-6 text-primary" />
                    <p className="text-sm">
                        <span className="font-medium">Secure & Private:</span>
                        {" "}Your data stays protected with real-time processing
                    </p>
                </div>
                <Button
                    onClick={handleConnect}
                    className="bg-primary dark:text-white"
                    disabled={getAuthUrl.isPending}
                >
                    {getAuthUrl.isPending ? (
                        <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Connecting...
                        </>
                    ) : (
                        <>
                            <LogIn className="w-4 h-4 mr-2" />
                            Connect Gmail
                        </>
                    )}
                </Button>
            </div>


        </div>
    )
}

