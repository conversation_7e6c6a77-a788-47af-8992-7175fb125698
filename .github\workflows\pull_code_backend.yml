name: App Deployment

on:
  push:
    branches:
      - test

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up SSH Agent and Add SSH Key
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.EC2_SSH_KEY }}

      - name: Deploy Application on EC2
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_SSH_USER }}@${{ secrets.EC2_SSH_IP }} '
            cd /home/<USER>/web-app || exit

            # Pull the latest code from the main branch
            echo "Pulling the latest code..."
            git pull origin main

            # Install exact versions of the dependencies
            echo "Installing dependencies..."
            npm ci

            # Generate Prisma client
            echo "Running Prisma generate..."
            npx prisma generate

            # Build the application
            echo "Building the application..."
            npm run build

            # Stop existing tmux session if running
            echo "Stopping the existing tmux session if running..."
            if tmux has-session -t web-app-session 2>/dev/null; then
              tmux kill-session -t web-app-session
            fi

            # Start the application inside a new tmux session
            echo "Starting application inside tmux session..."
            tmux new-session -d -s web-app-session 'npm start'
          '
