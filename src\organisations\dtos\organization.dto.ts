import { IsOptional, IsString, IsUrl, Max<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateOrganizationDto {
  @ApiProperty({
    description: 'Organization description',
    example: 'A leading provider of cloud solutions and enterprise software',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Description cannot be longer than 500 characters' })
  description?: string;

  @ApiProperty({
    description: 'Organization logo URL',
    example: 'https://example.com/logo.png',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUrl({}, { message: 'Logo must be a valid URL' })
  logo?: string;
}
