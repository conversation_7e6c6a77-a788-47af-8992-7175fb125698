import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  Request,
  Put,
} from '@nestjs/common';
import { SuperAdminService } from './superadmin.service';
import { AdminLoginDto } from './dtos/login.dto';
import { PaginationDto } from './dtos/pagination.dto';
import { SuperAdminAuthGuard } from '../auth/guards/superadmin-auth.guard';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  CreateOrganizationAdminDto,
  CreateOrganizationDto,
} from './dtos/onboarding.dto';
import { UpdateOrgAdminDto } from './dtos/update-org-admin.dto';

@ApiTags('SuperAdmin')
@Controller('superadmin')
export class SuperAdminController {
  constructor(private readonly superAdminService: SuperAdminService) {}

  @Post('login')
  @ApiOperation({ summary: 'SuperAdmin login' })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    schema: {
      type: 'object',
      properties: {
        accessToken: { type: 'string' },
        refreshToken: { type: 'string' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async login(@Body() loginDto: AdminLoginDto) {
    return this.superAdminService.login(loginDto);
  }

  @Get('users')
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'List all users (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'List of users with pagination',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Not an admin user' })
  async listUsers(@Query() paginationDto: PaginationDto) {
    return this.superAdminService.listUsers(paginationDto);
  }

  @Post('create-organization')
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Onboard a new organization' })
  @ApiBody({
    type: CreateOrganizationDto,
    description: 'Organization and admin details',
    examples: {
      organizationExample: {
        summary: 'Basic organization creation',
        value: {
          name: 'Acme Corporation',
          description: 'A leading provider of cloud solutions',
          logo: 'https://example.com/logo.png',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Organization created successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        organization: { type: 'object' },
      },
    },
  })
  async onboardOrganization(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Request() req,
  ) {
    return this.superAdminService.onboardOrganization(
      createOrganizationDto,
      req.user.id,
    );
  }

  @Get('organizations')
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'List all organizations' })
  @ApiResponse({
    status: 200,
    description: 'List of organizations with pagination',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string' },
              createdAt: { type: 'string' },
              updatedAt: { type: 'string' },
              creator: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  email: { type: 'string' },
                },
              },
              admin: {
                type: 'object',
                nullable: true,
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  email: { type: 'string' },
                  role: { type: 'string' },
                  createdAt: { type: 'string' },
                },
              },
              teams: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    description: { type: 'string' },
                    createdAt: { type: 'string' },
                  },
                },
              },
              KnowledgeBase: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    status: { type: 'string' },
                    createdAt: { type: 'string' },
                  },
                },
              },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async listOrganizations(@Query() paginationDto: PaginationDto) {
    return this.superAdminService.listOrganizations(paginationDto);
  }

  @Post('create-organization-admin')
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new organization admin' })
  @ApiBody({
    type: CreateOrganizationAdminDto,
    description: 'Organization admin details',
  })
  @ApiResponse({
    status: 201,
    description: 'Organization admin created successfully',
  })
  async createOrganizationAdmin(
    @Body() createOrganizationAdminDto: CreateOrganizationAdminDto,
  ) {
    return this.superAdminService.createOrganizationAdmin(
      createOrganizationAdminDto,
    );
  }

  @Get('support-queries')
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'List all support queries' })
  @ApiResponse({
    status: 200,
    description: 'List of support queries with pagination',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              email: { type: 'string' },
              message: { type: 'string' },
              area: { type: 'string' },
              status: { type: 'string' },
              createdAt: { type: 'string' },
              updatedAt: { type: 'string' },
              user: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  email: { type: 'string' },
                },
              },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async listSupportQueries(@Query() paginationDto: PaginationDto) {
    return this.superAdminService.listSupportQueries(paginationDto);
  }

  @Get('feedback-queries')
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'List all feedback queries' })
  @ApiResponse({
    status: 200,
    description: 'List of feedback queries with pagination',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              email: { type: 'string' },
              rating: { type: 'number' },
              area: { type: 'string' },
              message: { type: 'string' },
              createdAt: { type: 'string' },
              updatedAt: { type: 'string' },
              user: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  email: { type: 'string' },
                },
              },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async listFeedbackQueries(@Query() paginationDto: PaginationDto) {
    return this.superAdminService.listFeedbackQueries(paginationDto);
  }

  @Put('update-organization-admin')
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update organization admin' })
  @ApiBody({
    type: UpdateOrgAdminDto,
    description: 'New admin details',
    examples: {
      updateAdminExample: {
        summary: 'Update organization admin',
        value: {
          email: '<EMAIL>',
          name: 'New Admin',
          organizationId: '123e4567-e89b-12d3-a456-426614174000',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Organization admin updated successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        admin: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            email: { type: 'string' },
            role: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  async updateOrganizationAdmin(@Body() updateOrgAdminDto: UpdateOrgAdminDto) {
    return this.superAdminService.updateOrganizationAdmin(updateOrgAdminDto);
  }
}
