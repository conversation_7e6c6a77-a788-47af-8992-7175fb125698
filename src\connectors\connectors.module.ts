import { Module } from '@nestjs/common';
import { ConnectorsService } from './v1/connectors.v1.service';
import { ConnectorsController } from './v1/connectors.v1.controller';
import { PrismaModule } from 'src/prisma/prisma.module';
import { GoogleController } from './providers/google/googledrive/googledrive.v1.controller';
import { GoogleService } from './providers/google/googledrive/googledrive.v1.service';
import { DynamoDBModule } from 'src/dynamodb/dynamodb.module';
import { GmailController } from './providers/google/gmail/gmail.v1.controller';
import { GmailService } from './providers/google/gmail/gmail.v1.service';
import { SlackController } from './providers/slack/slack.v1.controller';
import { SlackService } from './providers/slack/slack.v1.service';
import { GitHubController } from './providers/github/github.v1.controller';
import { GitHubService } from './providers/github/github.v1.service';
import { OneDriveController } from './providers/microsoft/onedrive/onedrive.v1.controller';
import { OneDriveService } from './providers/microsoft/onedrive/onedrive.v1.service';
import { CommonModule } from '../common/common.module';
import { NotionController } from './providers/notion/notion.v1.controller';
import { NotionService } from './providers/notion/notion.v1.service';
import { HubspotController } from './providers/hubspot/hubspot.controller';
import { HubspotService } from './providers/hubspot/hubspot.service';
import { GoogleCalendarController } from './providers/google/calendar/calendar.v1.controller';
import { GoogleCalendarService } from './providers/google/calendar/calendar.v1.service';
import { ZendeskController } from './providers/zendesk/zendesk.v1.controller';
import { ZendeskService } from './providers/zendesk/zendesk.v1.service';
import { EtlModule } from '@/etl/etl.module';

@Module({
  imports: [PrismaModule, DynamoDBModule, CommonModule, EtlModule],
  providers: [
    ConnectorsService,
    GoogleService,
    GmailService,
    SlackService,
    GitHubService,
    OneDriveService,
    NotionService,
    HubspotService,
    GoogleCalendarService,
    ZendeskService,
  ],
  controllers: [
    ConnectorsController,
    GoogleController,
    GmailController,
    SlackController,
    GitHubController,
    OneDriveController,
    NotionController,
    HubspotController,
    GoogleCalendarController,
    ZendeskController,
  ],
  exports: [ConnectorsService, GoogleCalendarService],
})
export class ConnectorsModule {}
