"use client"

import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { toast } from "sonner";
import { passwordResetClient } from '@/lib/passwordResetClient';
import Image from "next/image";
import Link from "next/link";

export default function ResetPassword() {
    const searchParams = useSearchParams();
    const router = useRouter();
    const token = searchParams.get("token");
    const [isTokenValid, setIsTokenValid] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const [formData, setFormData] = useState({
        newPassword: "",
        confirmPassword: "",
    });
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [validationErrors, setValidationErrors] = useState({
        minLength: false,
        uppercase: false,
        lowercase: false,
        special: false,
        number: false,
    });

    useEffect(() => {
        const verifyToken = async () => {
            if (!token) {
                toast.error("Invalid or expired reset link");
                router.push("/");
                return;
            }

            try {
                const response = await passwordResetClient.verifyToken(token);
                if (response.status) {
                    setIsTokenValid(true);
                } else {
                    toast.error("Invalid or expired reset link");
                    router.push("/");
                }
            } catch (error: any) {
                toast.error(error.message || "Invalid or expired reset link");
                router.push("/");
            } finally {
                setIsLoading(false);
            }
        };

        verifyToken();
    }, [token, router]);

    const validatePassword = (password: string) => {
        setValidationErrors({
            minLength: password.length >= 12,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
            number: /[0-9]/.test(password),
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (formData.newPassword !== formData.confirmPassword) {
            toast.error("Passwords do not match");
            return;
        }
        
        if (!token || !isTokenValid) {
            toast.error("Invalid reset token");
            return;
        }

        if (!Object.values(validationErrors).every(Boolean)) {
            toast.error("Please meet all password requirements");
            return;
        }

        setLoading(true);
        try {
            await passwordResetClient.resetPassword(token, formData.newPassword);
            toast.success("Password reset successfully. Please login with your new password.");
            router.push("/");
        } catch (error: any) {
            setLoading(false);
            toast.error(error.message || "Failed to reset password");
        }
    };

    if (isLoading) {
        return (
            <div className="flex min-h-screen items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        );
    }

    if (!isTokenValid) {
        return null;
    }

    return (
        <div className="flex min-h-screen w-full flex-col justify-center px-4 py-8 text-gray-900 dark:text-gray-200 duration-500 animate-in fade-in">
            <div className="mx-auto flex w-full max-w-[420px] flex-col justify-center space-y-6">
                <div className="flex flex-col items-center text-center mb-8">
                    <Link href="/" className="flex items-center gap-2 sm:gap-3">
                        <Image
                            src={"/new-logo.svg"}
                            alt="logo"
                            width={70}
                            height={70}
                            className="w-[60px] h-[60px] sm:w-[70px] sm:h-[70px] md:w-[80px] md:h-[80px]"
                            priority
                        />
                        <span className="text-3xl sm:text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#C95158] to-[#4C68A8] bg-clip-text text-transparent">
                            STEPS AI
                        </span>
                    </Link>
                </div>

                <div className="space-y-6">
                    <div className="space-y-2 text-center">
                        <h1 className="text-2xl font-semibold tracking-tight">
                            Reset Your Password
                        </h1>
                        <p className="text-sm text-muted-foreground">
                            Enter your new password below
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="newPassword">New Password</Label>
                            <div className="relative">
                                <Input
                                    id="newPassword"
                                    type={showPassword ? "text" : "password"}
                                    value={formData.newPassword}
                                    onChange={(e) => {
                                        setFormData(prev => ({
                                            ...prev,
                                            newPassword: e.target.value
                                        }));
                                        validatePassword(e.target.value);
                                    }}
                                    className="pr-10"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute right-3 top-1/2 -translate-y-1/2"
                                >
                                    {showPassword ? (
                                        <EyeOffIcon className="h-4 w-4 text-gray-500" />
                                    ) : (
                                        <EyeIcon className="h-4 w-4 text-gray-500" />
                                    )}
                                </button>
                            </div>
                            
                            <div className="mt-2 space-y-1 text-sm">
                                {Object.entries(validationErrors).map(([key, valid]) => (
                                    <div
                                        key={key}
                                        className={`flex items-center gap-2 ${
                                            valid ? "text-green-500" : "text-gray-500"
                                        }`}
                                    >
                                        {valid ? "✓" : "•"} {key.replace(/([A-Z])/g, " $1").toLowerCase()}
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="confirmPassword">Confirm Password</Label>
                            <div className="relative">
                                <Input
                                    id="confirmPassword"
                                    type={showConfirmPassword ? "text" : "password"}
                                    value={formData.confirmPassword}
                                    onChange={(e) => setFormData(prev => ({
                                        ...prev,
                                        confirmPassword: e.target.value
                                    }))}
                                    className="pr-10"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                    className="absolute right-3 top-1/2 -translate-y-1/2"
                                >
                                    {showConfirmPassword ? (
                                        <EyeOffIcon className="h-4 w-4 text-gray-500" />
                                    ) : (
                                        <EyeIcon className="h-4 w-4 text-gray-500" />
                                    )}
                                </button>
                            </div>
                        </div>

                        <Button
                            type="submit"
                            className="w-full"
                            disabled={loading || !Object.values(validationErrors).every(Boolean) || 
                                     formData.newPassword !== formData.confirmPassword}
                        >
                            {loading ? "Resetting..." : "Reset Password"}
                        </Button>
                    </form>

                    <div className="text-center">
                        <Button
                            variant="ghost"
                            className="text-sm text-muted-foreground hover:text-primary"
                            onClick={() => router.push("/")}
                        >
                            Back to Login
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
} 