export interface Team {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  memberCount: number;
  teamLeader?: {
    id: string;
    name: string;
    email: string;
  };
  members?: TeamMember[];
  createdAt?: string;
  updatedAt?: string;
  organization?: {
    id: string;
    name: string;
    logo?: string;
  };
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'USER' | 'TEAMADMIN';
  profileImageUrl?: string;
  lastActive?: string;
  emailVerified?: boolean;
  hasOnboarded?: boolean;
  onboardingCompleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface OrganizationUser {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'TEAMADMIN' | 'USER';
  teams: string[];
  lastActive?: string;
  profileImageUrl?: string;
  hasOnboarded?: boolean;
}

export interface CreateTeamRequest {
  name: string;
  description?: string;
  organizationId?: string;
}

export interface InviteUsersRequest {
  emails: string[];
  organizationId?: string;
}

export interface UpdateUserRoleRequest {
  userId: string;
  role: 'ADMIN' | 'TEAMADMIN' | 'USER';
}

export interface AddMemberToTeamRequest {
  memberId: string;
}

export interface BulkAddMembersRequest {
  teamId: string;
  emails: string[];
}

export interface BulkAddMembersResult {
  email: string;
  status: 'success' | 'error' | 'pending';
  message: string;
}

export interface AssignTeamLeaderRequest {
  userId: string;
}

export interface Invitation {
  id: string;
  email: string;
  type: 'ORGANIZATION' | 'TEAM';
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'CANCELLED';
  expiresAt: string;
  createdAt: string;
  acceptedAt?: string;
  inviter: {
    id: string;
    name: string;
    email: string;
  };
  organization: {
    id: string;
    name: string;
    logo?: string;
  };
  team?: {
    id: string;
    name: string;
    description?: string;
  };
}

export interface InvitationsResponse {
  status: boolean;
  statusCode: number;
  result: {
    success: boolean;
    message: string;
    invitations: Invitation[];
    summary: {
      total: number;
      pending: number;
      accepted: number;
      expired: number;
      cancelled: number;
    };
  };
  version: string;
}

export interface GetInvitationsParams {
//   organizationId: string; // Required parameter
  status?: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'CANCELLED';
  type?: 'ORGANIZATION' | 'TEAM';
}

export interface CancelInvitationRequest {
  invitationId: string;
}

export interface ResendInvitationRequest {
  invitationId: string;
}

export interface InvitationActionResponse {
  success: boolean;
  message: string;
  invitation: {
    id: string;
    email: string;
    status: string;
    expiresAt?: string;
  };
}

export interface ConnectorConfig {
  name?: string;
  email?: string;
  [key: string]: any;
}

export interface Connector {
  connectorId: string;
  name: string;
  type: string;
  logo: string;
  category: string;
  description: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR';
  connectedAt: string;
  updatedAt: string;
  config: ConnectorConfig;
}

export interface UserConnectors {
  userId: string;
  userName: string;
  userEmail: string;
  userRole?: 'ADMIN' | 'TEAMADMIN' | 'USER';
  organizationId?: string;
  connectors: Connector[];
  totalConnectors: number;
}

export interface OrganizationConnectors {
  organizationId: string;
  organizationName: string;
  users: UserConnectors[];
  totalUsers: number;
  totalConnections: number;
  connectorSummary: Record<string, number>;
}

export interface UpdateOrganizationRequest {
  description?: string;
  logo?: string;
}

export interface OrganizationDetails {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  createdAt: string;
  updatedAt: string;
  slug?: string;
  createdBy?: string;
  _count?: {
    users: number;
    teams: number;
  };
}

export interface OnboardingStatus {
  userId: string;
  hasCreatedChat: boolean;
  hasConnectedCalendar: boolean;
  hasCompletedOnboarding: boolean;
  chatCreatedAt?: string;
  calendarConnectedAt?: string;
  onboardingCompletedAt?: string;
  finalVerdict: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Meeting {
  id: string;
  summary: string;
  start: string;
  end: string;
  location?: string;
  description?: string;
}

export interface OnboardingStatusResponse {
  status: boolean;
  statusCode: number;
  result: {
    success: boolean;
    message: string;
    hasCreatedChat: boolean;
    hasCompletedOnboarding: boolean;
    hasConnectedCalendar: boolean;
    finalVerdict: boolean;
    upcomingMeetings?: Meeting[];
  };
  version: string;
}

export interface OrganizationDetailsResponse {
  success: boolean;
  message: string;
  organization: OrganizationDetails;
}

export interface ActivityFeedItem {
  id: string;
  type: 'USER_JOINED' | 'TEAM_CREATED' | 'CONNECTOR_CONNECTED' | 'MEETING_SCHEDULED' | 'INVITATION_SENT' | 'ONBOARDING_COMPLETED';
  title: string;
  description: string;
  timestamp: string;
  user: {
    id: string;
    name: string;
    email: string;
    profileImageUrl?: string;
  };
  metadata?: {
    teamName?: string;
    connectorType?: string;
    meetingId?: string;
    invitationCount?: number;
    [key: string]: any;
  };
}

export interface ActivityFeedResponse {
  status: boolean;
  statusCode: number;
  result: {
    success: boolean;
    message: string;
    activities: ActivityFeedItem[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      hasMore: boolean;
    };
  };
  version: string;
}

export interface GetActivityFeedParams {
  organizationId?: string;
  page?: number;
  limit?: number;
  type?: ActivityFeedItem['type'];
  startDate?: string;
  endDate?: string;
}

export interface UserMeResponse {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'TEAMADMIN' | 'USER';
  organizationId: string;
  lastActive?: string;
  profileImageUrl?: string;
  hasOnboarded?: boolean;
  onboardingCompleted?: boolean;
  emailVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
  connectors: UserConnector[];
  totalConnectors: number;
  teams?: string[];
  organization?: {
    id: string;
    name: string;
    logo?: string;
  };
}

export interface UserConnector {
  id: string;
  connectorId: string;
  name: string;
  slug: string;
  logo: string;
  status: 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  isConnected: boolean;
  connectedAt?: string;
  lastSyncAt?: string;
  metadata?: any;
}
export interface GetUserMeApiResponse {
  success: boolean;
  message: string;
  user: UserMeResponse;
}
