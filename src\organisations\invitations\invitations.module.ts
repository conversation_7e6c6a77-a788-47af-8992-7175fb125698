import { Modu<PERSON> } from '@nestjs/common';
import { InvitationsController } from './v1/invitations.controller';
import { InvitationsService } from './v1/invitations.service';
import { PrismaModule } from '@/prisma/prisma.module';
import { EmailModule } from 'src/email/email.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [
    PrismaModule,
    EmailModule,
    JwtModule,
    ConfigModule,
  ],
  controllers: [InvitationsController],
  providers: [InvitationsService],
  exports: [InvitationsService],
})
export class InvitationsModule {}
