import { Controller, Post, Body, UseGuards, Get } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { GmailService } from './gmail.v1.service';
import { GmailCallbackDto, GmailRefreshTokenDto } from './dtos/gmail.dto';

@ApiTags('Gmail-V1')
@Controller({ version: '1', path: 'gmail' })
export class GmailController {
  constructor(private gmailService: GmailService) { }

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate Gmail Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated Gmail Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.gmailService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle Gmail Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed Gmail Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: GmailCallbackDto) {
    return this.gmailService.handleCallback(body.code, body.state);
  }

  @Post('refresh-token')
  @ApiOperation({ summary: 'Refresh Gmail Auth Token' })
  @ApiResponse({
    status: 200,
    description: 'Successfully refreshed Gmail Auth Token',
  })
  async refreshToken(
    @Body() body: GmailRefreshTokenDto,
    // @Headers('x-signature') signature: string,
  ) {
    // const isValid = this.webhookService.verifySignature(
    //   JSON.stringify(body),
    //   signature,
    // );

    // if (!isValid) {
    //   throw new UnauthorizedException('Invalid signature');
    // }
    return this.gmailService.refreshToken(body.refreshToken, body.userId);
  }

  @Get('user-configs')
  @ApiOperation({ summary: 'Get Gmail User Configs' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved Gmail User Configs',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async getGmailUserConfigs(@GetCurrentUser() user: JwtPayload) {
    return this.gmailService.getGmailUserConfigs(user.sub);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect Gmail Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected Gmail Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.gmailService.disconnect(user.sub);
  }
}
