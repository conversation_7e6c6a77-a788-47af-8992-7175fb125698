import {
  Controller,
  Get,
  Post,
  Param,
  HttpC<PERSON>,
  HttpStatus,
  UseGuards,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ActivitiesService } from './activities.v1.service';
import {
  GetActivitiesResponseDto,
} from '../dtos/activities.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { ActivityType } from '@prisma/client';

@ApiTags('Activities-V1')
@Controller({ version: '1', path: 'activities' })
export class ActivitiesController {
  constructor(private readonly activitiesService: ActivitiesService) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get organization activity feed',
    description: `
      Retrieve the top 10 recent activity logs for the current user's organization.
      Users can only view activities for their own organization.

      **Supported Activity Types:**
      - USER_JOINED_ORGANIZATION: User joined the organization
      - USER_LEFT_ORGANIZATION: User left the organization
      - USER_ROLE_CHANGED: User role was modified
      - TEAM_CREATED: New team was created
      - TEAM_DELETED: Team was deleted
      - TEAM_MEMBER_ADDED: Member added to team
      - TEAM_MEMBER_REMOVED: Member removed from team
      - TEAM_ADMIN_ASSIGNED: Team admin role assigned
      - CONNECTOR_CONNECTED: Integration/connector was connected
      - CONNECTOR_DISCONNECTED: Integration/connector was disconnected
      - FILE_UPLOADED: File was uploaded
      - KNOWLEDGE_BASE_CREATED: Knowledge base was created
      - KNOWLEDGE_BASE_SHARED: Knowledge base was shared
      - ONBOARDING_STEP_COMPLETED: User completed an onboarding step
      - ONBOARDING_COMPLETED: User completed full onboarding
      - CALENDAR_CONNECTED: Google Calendar was connected
      - CHAT_CREATED: New chat was created
    `,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Activity feed retrieved successfully',
    type: GetActivitiesResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Organization not found or user not found',
  })
  @HttpCode(HttpStatus.OK)
  async getActivities(
    @GetCurrentUser() currentUser: JwtPayload,
  ): Promise<GetActivitiesResponseDto> {
    return this.activitiesService.getActivities(
      currentUser.orgId,
      currentUser.sub,
    );
  }

  @Get('stats')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get activity statistics',
    description: 'Get activity statistics for the current user\'s organization including activity counts by type and recent activity trends.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Activity statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalActivities: {
          type: 'number',
          example: 150,
          description: 'Total number of activities',
        },
        activitiesByType: {
          type: 'object',
          example: {
            USER_JOINED_ORGANIZATION: 25,
            TEAM_CREATED: 5,
            CONNECTOR_CONNECTED: 30,
          },
          description: 'Count of activities by type',
        },
        recentActivityCount: {
          type: 'number',
          example: 12,
          description: 'Number of activities in the last 7 days',
        },
        mostActiveUsers: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              userId: { type: 'string', example: 'uuid-string' },
              userName: { type: 'string', example: 'John Doe' },
              activityCount: { type: 'number', example: 15 },
            },
          },
          description: 'Top 5 most active users',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Organization not found',
  })
  @HttpCode(HttpStatus.OK)
  async getActivityStats(
    @GetCurrentUser() currentUser: JwtPayload,
  ) {
    return {
      message: 'Activity statistics endpoint - implementation pending',
      organizationId: currentUser.orgId,
      requestedBy: currentUser.sub,
    };
  }

  @Post(':organizationId/test-activities')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Create test activities (for testing purposes)',
    description: 'Creates sample activity logs to test the activity feed functionality.',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'Organization ID to create test activities for',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Test activities created successfully',
  })
  @HttpCode(HttpStatus.OK)
  async createTestActivities(
    @Param('organizationId') organizationId: string,
    @GetCurrentUser() currentUser: JwtPayload,
  ) {
    if (organizationId !== currentUser.orgId) {
      throw new ForbiddenException('You can only create test activities for your own organization');
    }

    const user = await this.activitiesService['prisma'].user.findUnique({
      where: { id: currentUser.sub },
      select: { organizationId: true, name: true, email: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Create sample activities
    const sampleActivities = [
      {
        organizationId,
        userId: currentUser.sub,
        activityType: ActivityType.USER_JOINED_ORGANIZATION,
        description: `${user.name} joined the organization`,
        metadata: { email: user.email },
      },
      {
        organizationId,
        userId: currentUser.sub,
        activityType: ActivityType.ONBOARDING_COMPLETED,
        description: `${user.name} completed onboarding`,
        metadata: { completedSteps: ['chat', 'calendar', 'profile'] },
      },
      {
        organizationId,
        userId: currentUser.sub,
        activityType: ActivityType.CONNECTOR_CONNECTED,
        description: `${user.name} connected Google Drive`,
        metadata: { connectorName: 'Google Drive', connectorType: 'google-drive' },
      },
      {
        organizationId,
        userId: currentUser.sub,
        activityType: ActivityType.CALENDAR_CONNECTED,
        description: `${user.name} connected Google Calendar`,
        metadata: { calendarProvider: 'Google Calendar' },
      },
      {
        organizationId,
        userId: currentUser.sub,
        activityType: ActivityType.CHAT_CREATED,
        description: `${user.name} created a new chat`,
        metadata: { chatId: 'sample-chat-id' },
      },
    ];

    // Create activities using the service
    for (const activity of sampleActivities) {
      await this.activitiesService.createActivityLog(activity);
    }

    return {
      message: 'Test activities created successfully',
      count: sampleActivities.length,
      organizationId,
    };
  }
}
