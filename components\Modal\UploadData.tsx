'use client'

import React, { useState, useC<PERSON>back, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    <PERSON><PERSON>Footer,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useRouter } from "next/navigation"
import { toast } from 'sonner';
import {
    Upload,
    Loader2,
    CheckCircle2,
    X,
    File,
    Image as ImageIcon,
    FileText,
    AlertCircle,
    ChevronRight,
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import axios from "axios"
import {
    useApiMutation,
    createMutationFn,
    queryClient
} from "@/lib/apiClient";
import { Separator } from "@/components/ui/separator"
import { Progress } from "@steps-ai/ui"
import { Dialog as UIDialog, DialogContent as UIDialogContent, DialogHeader as UIDialogHeader, DialogTitle as UIDialogTitle, DialogDescription as UIDialogDescription } from "@/components/ui/dialog";
// import { createGoogleDrivePicker } from '@/lib/googleDrivePicker';
// import { createOneDrivePicker } from '@/lib/oneDrivePicker';
// import { useGoogleDrivePicker } from '@/lib/googleDrivePicker';
// import { useOneDrivePicker } from '@/lib/oneDrivePicker';

interface UploadDataProps {
    className?: string
    onUploadComplete?: (files: File[]) => void
    kid: string
}

export default function UploadData({ className, kid }: UploadDataProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isSuccess, setIsSuccess] = useState(false)
    const [isHovered, setIsHovered] = useState(false)
    const [activeTab, setActiveTab] = useState("local")
    const [supportedFiles, setSupportedFiles] = useState<File[]>([]);
    const [unsupportedFiles, setUnsupportedFiles] = useState<File[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isDragging, setIsDragging] = useState(false)
    const [uploadPhase, setUploadPhase] = useState<'uploading' | 'processing' | null>(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [totalSize, setTotalSize] = useState(0);
    const [showUnsupportedModal, setShowUnsupportedModal] = useState(false);

    // Add validation for kid
    useEffect(() => {
        if (!kid) {
            setError("Knowledge base ID is required");
            setIsOpen(false);
        }
    }, [kid]);

    const ALLOWED_FORMATS = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'application/vnd.ms-xpsdocument',
        'application/epub+zip',
        'application/x-mobipocket-ebook',
        'application/x-fictionbook+xml',
        'application/x-cbz',
        'image/svg+xml',
        'text/markdown',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/x-hwp',
        'application/vnd.oasis.opendocument.text',
        'text/x-markdown',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/x-hwp',
        'application/vnd.oasis.opendocument.text',
        'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.openxmlformats-officedocument.presentationml.slide',
        'application/vnd.openxmlformats-officedocument.presentationml.slideLayout',
        'application/vnd.openxmlformats-officedocument.presentationml.slideMaster',
        'application/vnd.openxmlformats-officedocument.presentationml.slideLayout',
    ];
    const MAX_TOTAL_SIZE = 1024 * 1024 * 1024;
    const MAX_FILE_SIZE = 1024 * 1024 * 1024;

    const addFilesToKnowledgebase = useApiMutation(
        createMutationFn.post('/knowledgebase/addfiles'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['files'] });
                    queryClient.invalidateQueries({ queryKey: ['knowledgebasedata'] });
                    setIsSuccess(true);
                    // toast.success('Files uploaded successfully!');
                } else {
                    setError("Unexpected response from server");
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message || "Something went wrong. Please try again.";
                    toast.error("Knowledge Base Error", {
                        description: errorMessage,
                        duration: 5000
                    });
                    setError(errorMessage);
                } else if (error.request) {
                    toast.error("Network Error", {
                        description: "No response received from server",
                        duration: 5000
                    });
                    setError("No response received from server");
                } else {
                    toast.error("Request Error", {
                        description: "Error setting up the request",
                        duration: 5000
                    });
                    setError("Error setting up the request");
                }
            }
        }
    );

    const getFileIcon = (file: File) => {
        if (file.type.startsWith('image/')) return <ImageIcon className="h-6 w-6" />
        if (file.type.includes('pdf')) return <FileText className="h-6 w-6" />
        return <File className="h-6 w-6" />
    }

    const handleDrag = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
    }, [])

    const handleDragIn = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setIsDragging(true)
    }, [])

    const handleDragOut = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setIsDragging(false)
    }, [])

    const validateAndSplitFiles = (newFiles: File[]) => {
        const supported: File[] = [];
        const unsupported: File[] = [];

        newFiles.forEach(file => {
            let type = file.type;
            if (!type && file.name.toLowerCase().endsWith('.md')) {
                type = 'text/markdown';
            }
            if (ALLOWED_FORMATS.includes(type) && file.size <= MAX_FILE_SIZE) {
                supported.push(file);
            } else {
                unsupported.push(file);
            }
        });
        return { supported, unsupported };
    };

    const updateTotalSize = useCallback((fileList: File[]) => {
        const size = fileList.reduce((sum, file) => sum + file.size, 0);
        setTotalSize(size);
    }, []);

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        const droppedFiles = Array.from(e.dataTransfer.files);
        const { supported, unsupported } = validateAndSplitFiles(droppedFiles);

        const newSupported = [...supportedFiles, ...supported];
        const newUnsupported = [...unsupportedFiles, ...unsupported];
        setSupportedFiles(newSupported);
        setUnsupportedFiles(newUnsupported);
        updateTotalSize(newSupported);
        setError(null);
    }, [supportedFiles, unsupportedFiles, updateTotalSize]);



    const removeSupportedFile = useCallback((fileToRemove: File) => {
        setSupportedFiles(prevFiles => {
            const updatedFiles = prevFiles.filter(file => file !== fileToRemove);
            updateTotalSize(updatedFiles);
            return updatedFiles;
        });
    }, [updateTotalSize]);

    const removeUnsupportedFile = useCallback((fileToRemove: File) => {
        setUnsupportedFiles(prevFiles => prevFiles.filter(file => file !== fileToRemove));
    }, []);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            setError(null);
            const newFiles = Array.from(e.target.files);
            const { supported, unsupported } = validateAndSplitFiles(newFiles);
            const updatedSupported = [...supportedFiles, ...supported];
            const updatedUnsupported = [...unsupportedFiles, ...unsupported];
            setSupportedFiles(updatedSupported);
            setUnsupportedFiles(updatedUnsupported);
            updateTotalSize(updatedSupported);
            setError(null);
        }
    }

    // Helper to chunk arrays
    function chunkArray<T>(arr: T[], size: number): T[][] {
        const result: T[][] = [];
        for (let i = 0; i < arr.length; i += size) {
            result.push(arr.slice(i, i + size));
        }
        return result;
    }

    const uploadFiles = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!kid) {
            setError("Knowledge base ID is required");
            return;
        }

        if (supportedFiles.length === 0) {
            setError('Please select supported files to upload');
            return;
        }

        setIsSubmitting(true);
        setError(null);
        setUploadPhase('uploading');
        setUploadProgress(0);

        const BATCH_SIZE = 100;
        const fileChunks = chunkArray(supportedFiles, BATCH_SIZE);
        const totalFiles = supportedFiles.length;
        const cdnUrl = process.env.NEXT_PUBLIC_AWS_CDN_URL;
        let uploadedCount = 0;
        let allFilesForBackend: any[] = [];

        try {
            for (let chunkIdx = 0; chunkIdx < fileChunks.length; chunkIdx++) {
                const chunk = fileChunks[chunkIdx];
                const fileData = chunk.map(file => ({ name: file.name, type: file.type }));
                const presignedRes = await fetch(process.env.NEXT_PUBLIC_PRESIGNED_URL as string, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ files: fileData, folderName: `knowledgebase/${kid}` })
                });
                const req = await presignedRes.json();
                const urls = req.result.urls;

                // Helper for parallel uploads with concurrency limit
                async function uploadWithConcurrencyLimit(files: File[], urls: string[], concurrency: number) {
                    let index = 0;
                    async function uploadNext() {
                        if (index >= files.length) return;
                        const currentIndex = index++;
                        await axios.put(urls[currentIndex], files[currentIndex], {
                            headers: { 'Content-Type': files[currentIndex].type },
                            onUploadProgress: (progressEvent) => {
                                if (typeof progressEvent.total === 'number') {
                                    // Optionally, update per-file progress here
                                }
                            }
                        });
                        uploadedCount++;
                        setUploadProgress(Math.round((uploadedCount / totalFiles) * 100));
                        await uploadNext();
                    }
                    await Promise.all(Array(concurrency).fill(0).map(uploadNext));
                }

                await uploadWithConcurrencyLimit(chunk, urls, 15);

                const filesForBackend = chunk.map((file, idx) => {
                    const urlObj = new URL(urls[idx]);
                    const fileKey = urlObj.pathname.startsWith('/') ? urlObj.pathname.slice(1) : urlObj.pathname;
                    return {
                        name: file.name,
                        url: `${cdnUrl}/${fileKey}`,
                        size: file.size,
                        mimeType: file.type,
                    };
                });
                allFilesForBackend = allFilesForBackend.concat(filesForBackend);
            }

            await addFilesToKnowledgebase.mutateAsync({
                kid,
                files: allFilesForBackend
            });

            setIsSuccess(true);
            setError(null);
            toast.success('Files uploaded successfully!');
            setIsOpen(false); // Close the modal after successful upload
        } catch (err: any) {
            const errorMessage = err.response?.data?.message || 'Upload failed. Please try again.';
            toast.error("Upload Failed", {
                description: errorMessage,
                duration: 5000
            });
            setError(errorMessage);
            console.error(err);
        } finally {
            setSupportedFiles([]);
            setUnsupportedFiles([]);
            setIsSubmitting(false);
            setUploadProgress(0);
            setUploadPhase(null);
        }
    };

    const handleClose = () => {
        setIsOpen(false);
        setError(null);
        setIsSubmitting(false);
    };

    const formatBytes = (bytes: number) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={(open) => {
                if (!open) {
                    setError(null);
                    setIsSubmitting(false);
                    setSupportedFiles([]);
                    setUnsupportedFiles([]);
                    setUploadProgress(0);
                    setUploadPhase(null);
                }
                setIsOpen(open);
            }}>
                <DialogTrigger asChild>
                    <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onHoverStart={() => setIsHovered(true)}
                        onHoverEnd={() => setIsHovered(false)}
                    >
                        <Button
                            className="relative bg-gradient-to-r from-[#2c487c] to-[#3B6BB5] text-white 
                                transition-all duration-200 shadow-lg  pr-6 pl-4"
                        >
                            <motion.div
                                animate={{ rotate: isHovered ? 360 : 0 }}
                                transition={{ duration: 0.5 }}
                                className="mr-2"
                            >
                                <Upload className="h-4 w-4" />
                            </motion.div>
                            Upload Files
                        </Button>
                    </motion.div>
                </DialogTrigger>

                <DialogContent className="sm:max-w-lg min-h-[40vh] flex flex-col bg-background border-border">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2 text-foreground">
                            <Upload className="h-5 w-5 text-primary" />
                            Upload Files
                        </DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            Upload your files from your computer
                        </DialogDescription>
                    </DialogHeader>

                    <form onSubmit={uploadFiles} className="space-y-6">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            transition={{ duration: 0.3 }}
                        >
                            <div
                                className={`border-2 border-dashed rounded-lg p-6 transition-colors 
                      ${isDragging ? "border-primary bg-primary/5" : "border-border"}
                      ${error && supportedFiles.length === 0 ? "border-destructive" : ""}
                      dark:bg-background`}
                                onDragEnter={handleDragIn}
                                onDragLeave={handleDragOut}
                                onDragOver={handleDrag}
                                onDrop={handleDrop}
                            >
                                <div className="text-center">
                                    <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                                    <div className="mt-4">
                                        <label
                                            htmlFor="file-upload"
                                            className="cursor-pointer text-primary hover:text-primary-hover"
                                        >
                                            Click to upload
                                        </label>
                                        <span className="text-muted-foreground">{" or drag and drop"}</span>
                                        <Input
                                            id="file-upload"
                                            type="file"
                                            className="hidden"
                                            onChange={handleFileChange}
                                            multiple
                                        />
                                    </div>
                                </div>
                            </div>
                        </motion.div>

                        <Separator className="my-4" />

                        {supportedFiles.length > 0 && (
                            <div className="mt-4 space-y-3">
                                <div className="flex items-center justify-between text-sm text-muted-foreground">
                                    <span>{supportedFiles.length} file{supportedFiles.length !== 1 ? 's' : ''} selected</span>
                                    <span>{formatBytes(totalSize)}</span>
                                </div>

                                {uploadProgress > 0 && (
                                    <>
                                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                                            <span>Upload Progress</span>
                                            <span>
                                                {uploadProgress}%
                                                {uploadPhase === 'uploading' && ' (Uploading...)'}
                                                {uploadPhase === 'processing' && ' (Processing...)'}
                                            </span>
                                        </div>
                                        <div className="w-full bg-secondary rounded-full h-2.5">
                                            <div
                                                className="bg-primary h-2.5 rounded-full transition-all duration-300"
                                                style={{ width: `${uploadProgress}%` }}
                                            />
                                        </div>
                                    </>
                                )}
                            </div>
                        )}

                        {supportedFiles.length > 0 && (
                            <ScrollArea className="space-y-2 max-h-[35vh] overflow-y-auto">
                                <div className="space-y-2">
                                    {supportedFiles.map((file, index) => (
                                        <div
                                            key={`supported-${file.name}-${index}`}
                                            className="flex items-center justify-between p-2 bg-accent/50 dark:bg-accent/20 rounded-sm hover:bg-accent transition-colors duration-200 animate-fadeIn"
                                        >
                                            <div className="flex items-center gap-2">
                                                {getFileIcon(file)}
                                                <span className="text-sm truncate max-w-[200px] text-foreground">
                                                    {file.name}
                                                </span>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeSupportedFile(file)}
                                                className="opacity-70 hover:opacity-100 transition-opacity duration-200"
                                            >
                                                <X className="h-4 w-4 text-foreground" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </ScrollArea>
                        )}
                        {unsupportedFiles.length > 0 && (
                            <div className="space-y-2 mt-4">
                                <Button
                                    type="button"
                                    variant="outline"
                                    className="w-full border-destructive text-destructive hover:bg-destructive/10"
                                    onClick={() => setShowUnsupportedModal(true)}
                                >
                                    {unsupportedFiles.length} unsupported file{unsupportedFiles.length !== 1 ? 's' : ''} (view)
                                </Button>
                            </div>
                        )}
                        {error && (
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}

                        <DialogFooter className="mt-2 w-full right-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleClose}
                                disabled={isSubmitting}
                                className="border-border text-foreground hover:bg-accent"
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={isSubmitting || supportedFiles.length === 0}
                                className="bg-primary hover:bg-primary-hover text-primary-foreground relative overflow-hidden group"
                            >
                                {isSubmitting ? (
                                    <div
                                        key="submitting"
                                        className="flex items-center gap-2"
                                    >
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        Uploading...
                                    </div>
                                ) : (
                                    <div
                                        key="upload"
                                        className="flex items-center gap-2"
                                    >
                                        <Upload className="h-4 w-4" />
                                        Upload Files
                                    </div>
                                )}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
            <UIDialog open={showUnsupportedModal} onOpenChange={setShowUnsupportedModal}>
                <UIDialogContent className="max-w-lg">
                    <UIDialogHeader>
                        <UIDialogTitle>Unsupported Files</UIDialogTitle>
                        <UIDialogDescription>
                            The following files are not supported and will not be uploaded. You can remove them from the list.
                        </UIDialogDescription>
                    </UIDialogHeader>
                    <div className="max-h-[50vh] overflow-y-auto space-y-2 mt-2">
                        {unsupportedFiles.length === 0 ? (
                            <div className="text-muted-foreground text-center">No unsupported files.</div>
                        ) : (
                            unsupportedFiles.map((file: File, index: number) => (
                                <div
                                    key={`unsupported-modal-${file.name}-${index}`}
                                    className="flex items-center justify-between p-2 bg-destructive/10 rounded-sm animate-fadeIn"
                                >
                                    <div className="flex items-center gap-2">
                                        {getFileIcon(file)}
                                        <span className="text-sm truncate max-w-[200px] text-destructive">
                                            {file.name}
                                        </span>
                                    </div>
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeUnsupportedFile(file)}
                                        className="opacity-70 hover:opacity-100 transition-opacity duration-200"
                                    >
                                        <X className="h-4 w-4 text-destructive" />
                                    </Button>
                                </div>
                            ))
                        )}
                    </div>
                    <div className="flex justify-end mt-4">
                        <Button variant="outline" onClick={() => setShowUnsupportedModal(false)}>
                            Close
                        </Button>
                    </div>
                </UIDialogContent>
            </UIDialog>
        </>
    )
}
