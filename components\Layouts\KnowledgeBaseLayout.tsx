import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { ChevronLeft, ChevronRight, Search } from "lucide-react"

export default function KnowledgeBaseLayout({ children }: { children: React.ReactNode }) {
    return (
        <div className="w-full p-6">
            <div className="max-w-6xl mx-auto space-y-6">
                <div>
                    <h1 className="text-2xl font-semibold">All Knowledge Bases</h1>
                    <p className="text-gray-400">Access all of your knowledgebases here</p>
                </div>

                <div className="p-6 rounded-lg space-y-6">
                    <div className="flex justify-between items-start">
                        <div>
                            <h2 className="text-xl font-semibold">Knowledge bases</h2>
                            <p className="text-gray-400">Add knowledge sources to shape your knowledge base.</p>
                            <a href="#" className="text-purple-400 hover:underline text-sm">Learn how knowledge bases work →</a>
                        </div>
                        <Button >Add new knowledge base</Button>
                    </div>

                    <div className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-800">Your storage</span>
                            <span className="text-sm text-gray-500">3.65 MB / 50 MB </span>
                        </div>
                        <Progress value={7.3} className="h-2 " />
                        <Button variant="link" className="text-purple-400 p-0 h-auto">Upgrade for more</Button>
                    </div>

                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <Input className="pl-10 border-none shadow-md" placeholder="Search for a knowledge base" />
                    </div>
                    {children}
                    
                </div>
            </div>
        </div>
    )
}