import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AdminLoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Admin email address'
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: '123456789adv',
    description: 'Admin password'
  })
  @IsString()
  @IsNotEmpty()
  password: string;
} 