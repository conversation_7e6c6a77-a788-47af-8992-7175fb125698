"use client";

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { But<PERSON> } from "@steps-ai/ui"
import { Database, Boxes, Check, Plus, Loader2, UserCog } from "lucide-react"
import { useApiQuery } from "@/lib/apiClient";
import { useState, useMemo, useEffect } from "react"
import { Connector } from "@/types/sources";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import useChatStore from "@/store/chatStore";
import CreateKnowledgeBase from "@/components/Modal/CreateKnowledgeBase";

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
export function Sources() {
    const router = useRouter();
    const [searchTerm, setSearchTerm] = useState('')
    const { selectedSources, setSelectedSources, savedSources, setSavedSources, auto, setAuto, currentChat } = useChatStore();
    const [kbSearchTerm, setKbSearchTerm] = useState('');

    const {
        data,
        isLoading,
        isError,
    } = useApiQuery<any>(['Sources'], '/user/get-chat-configs');
    const connectorsList = useMemo(() => {
        // Only show DataOps Agent if there is a current chat with an id
        const showDataOps = currentChat && currentChat.chat_id;
        if (!data?.result?.connectors) {
            return showDataOps ? [
                {
                    id: 'dataops',
                    name: 'DataOps Agent',
                    status: 'connected',
                    slug: 'dataops',
                    logo: '', // Will use icon, not image
                    type: 'connector',
                    isDataOps: true,
                }
            ] : [];
        }
        return [
            ...(showDataOps ? [
                {
                    id: 'dataops',
                    name: 'DataOps Agent',
                    status: 'connected',
                    slug: 'dataops',
                    logo: '', // Will use icon, not image
                    type: 'connector',
                    isDataOps: true,
                }
            ] : []),
            ...(data.result.connectors.Connected || []).map((c: Connector) => ({
                ...c,
                status: "connected",
            })),
            ...(data.result.connectors.Available || []).map((c: Connector) => ({
                ...c,
                status: "available",
            })),
        ];
    }, [data, currentChat]);

    const knowledgeBasesList = useMemo(() => {
        if (!data?.result?.knowledgebases) return [];
        return [
            ...(data.result.knowledgebases.Ready || []),
            ...(data.result.knowledgebases.Processing || []),
        ];
    }, [data]);

    const knowledgeBasesWithDuplicateCheck = useMemo(() => {
        // Create a map to count occurrences of each name
        const nameCount = new Map<string, number>();
        knowledgeBasesList.forEach(kb => {
            nameCount.set(kb.name, (nameCount.get(kb.name) || 0) + 1);
        });

        // Return the list with a hasDuplicate flag
        return knowledgeBasesList.map(kb => ({
            ...kb,
            hasDuplicate: (nameCount.get(kb.name) || 0) > 1
        }));
    }, [knowledgeBasesList]);

    const filteredConnectors = useMemo(() => {
        return connectorsList.filter(connector =>
            connector.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [connectorsList, searchTerm]);

    const filteredKnowledgeBases = useMemo(() => {
        return knowledgeBasesWithDuplicateCheck.filter(kb =>
            kb.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [knowledgeBasesWithDuplicateCheck, searchTerm]);

    const filteredKnowledgeBasesSub = useMemo(() => {
        return knowledgeBasesWithDuplicateCheck.filter(kb =>
            kb.name.toLowerCase().includes(kbSearchTerm.toLowerCase())
        );
    }, [knowledgeBasesWithDuplicateCheck, kbSearchTerm]);

    useEffect(() => {
        if (isLoading || isError || !data) return;
        if (!auto) {
            // setSelectedSources(new Map());
            return;
        }
        const allSources = new Map();
        (data.result?.connectors?.Connected || []).forEach((c: Connector) => {
            allSources.set(c.id, {
                id: c.id,
                name: c.name,
                logo: c.logo,
                type: 'connector',
                status: 'connected',
                etlStatus: (c as any).etlStatus || 'none',
                connectorId: (c as any).userConnectorId || 'none',
            });
        });
        (data.result?.knowledgebases?.Ready || []).forEach((kb: any) => {
            if (kb.status === 'SUCCESS') {
                allSources.set(kb.id, {
                    id: kb.id,
                    name: kb.name,
                    type: 'knowledge_base',
                    status: kb.status,
                });
            }
        });
        setSelectedSources(allSources);
    }, [auto, isLoading, isError, data, setSelectedSources]);

    const handleSelect = (source: { id: string; type: "connector" | "knowledge_base"; name: string; status?: string; slug?: string; logo?: string; etl?: string, connectorId?: string }, event?: React.SyntheticEvent) => {
        if (event) event.preventDefault();
        if (source.status === "available") {
            toast.error("Please connect to " + source.slug + " first", {
                description: "Taking you to Connectors where you can integrate your account",
                duration: 2000,
            });
            setTimeout(() => {
                router.push(`/connectors/${source.slug}`);
            }, 2000);
            return;
        }

        const newMap = new Map(selectedSources);
        if (newMap.has(source.id)) {
            newMap.delete(source.id);
        } else {
            newMap.set(source.id, {
                id: source.id,
                name: source.name,
                logo: source.logo,
                type: source.type,
                status: source.status || 'connected',
                etlStatus: source.etl || 'none',
                connectorId: source.connectorId || 'none'
            });
        }
        console.log("newMap", newMap);
        setSelectedSources(newMap);
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" className="border-none" disabled={auto}>
                    <Boxes className="w-2 h-2 md:w-4 md:h-4" />
                    <span className="ml-2 hidden md:block">Data Sources</span>
                    {selectedSources.size > 0 && (
                        <span className="ml-2 md:block hidden bg-primary/20 text-primary rounded-full px-2 py-0.5 text-xs">
                            {selectedSources.size}
                        </span>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-80 p-0" align="start">
                <div className="p-2 pb-0">
                    <input
                        type="text"
                        placeholder="Search sources..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                        disabled={isLoading || isError}
                        className="w-full rounded-md border px-3 py-2 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 bg-transparent"
                    />
                </div>
                <div className="max-h-[250px] overflow-y-auto overflow-x-hidden">
                    {isLoading && (
                        <div className="flex flex-col items-center justify-center py-6 gap-2">
                            <Loader2 className="h-8 w-8 animate-spin text-primary/60" />
                            <p className="text-sm text-muted-foreground">Loading sources...</p>
                        </div>
                    )}
                    {isError && (
                        <div className="p-4">
                            <Alert variant="destructive" className="bg-destructive/10 border-none">
                                <AlertDescription className="text-sm text-destructive">
                                    Failed to load sources. Please try again later or contact support if the issue persists.
                                </AlertDescription>
                            </Alert>
                        </div>
                    )}
                    {!isLoading && !isError && (
                        <>
                            {filteredConnectors.length > 0 && (
                                <>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuLabel>Agents</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuGroup>
                                        {filteredConnectors.map((connector) => (
                                            <DropdownMenuItem
                                                key={connector.id}
                                                onSelect={(event: any) => handleSelect({
                                                    id: connector.id,
                                                    name: connector.name,
                                                    status: connector.status,
                                                    slug: connector.slug,
                                                    logo: connector.logo,
                                                    type: 'connector',
                                                    etl: connector.etlStatus,
                                                    connectorId: connector.userConnectorId
                                                }, event)}
                                                disabled={connector.status !== "connected" && connector.status !== "available"}
                                                className={cn(
                                                    "cursor-pointer flex items-center gap-2",
                                                    connector.status !== "connected" && "opacity-70"
                                                )}
                                            >
                                                <div className={cn(
                                                    "flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                                    selectedSources.has(connector.id) ? "bg-primary text-primary-foreground" : "opacity-50"
                                                )}>
                                                    {selectedSources.has(connector.id) && <Check className="h-3 w-3" />}
                                                </div>
                                                {connector.isDataOps ? (
                                                    <UserCog className="w-5 h-5 text-primary" />
                                                ) : (
                                                    <Image src={connector.logo} alt={connector.name} width={20} height={20} />
                                                )}
                                                <span>{connector.name}</span>
                                                <span className={cn(
                                                    "ml-auto text-xs",
                                                    connector.status === "connected" ? "text-green-500" : "text-yellow-500"
                                                )}>
                                                    {connector.status}
                                                </span>
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuGroup>
                                    <DropdownMenuSeparator />
                                </>
                            )}
                            <DropdownMenuLabel>Data Sources</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuSub>
                                <DropdownMenuSubTrigger className="flex items-center flex-row pr-2 hover:bg-primary/10 py-2">
                                    <div
                                        className={cn(
                                            "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary cursor-pointer",
                                            Array.from(selectedSources.values()).some((item: any) => item.type === 'knowledge_base') ? "bg-primary text-primary-foreground" : "opacity-50"
                                        )}
                                        onClick={e => {
                                            e.stopPropagation();
                                            // Get all selectable knowledge bases (status === 'SUCCESS')
                                            const allKBs = knowledgeBasesWithDuplicateCheck.filter(kb => kb.status === 'SUCCESS');
                                            const allKBIds = allKBs.map(kb => kb.id);
                                            const selectedKBIds = Array.from(selectedSources.values())
                                                .filter((item: any) => item.type === 'knowledge_base')
                                                .map((item: any) => item.id);
                                            const allSelected = allKBs.length > 0 && allKBIds.every(id => selectedKBIds.includes(id));
                                            const newMap = new Map(selectedSources);
                                            if (allSelected) {
                                                // Deselect all knowledge bases
                                                allKBIds.forEach(id => newMap.delete(id));
                                            } else {
                                                // Select all knowledge bases
                                                allKBs.forEach(kb => {
                                                    newMap.set(kb.id, {
                                                        id: kb.id,
                                                        name: kb.name,
                                                        type: 'knowledge_base',
                                                        status: kb.status,
                                                    });
                                                });
                                            }
                                            setSelectedSources(newMap);
                                        }}
                                        title="Select/Deselect all Knowledge Bases"
                                    >
                                        {Array.from(selectedSources.values()).some((item: any) => item.type === 'knowledge_base') && <Check className="h-3 w-3" />}
                                    </div>
                                    <Database className="mr-2 h-4 w-4" />
                                    <span> Knowledge Bases</span>
                                </DropdownMenuSubTrigger>
                                <DropdownMenuSubContent className="w-72">
                                    <Command>
                                        <CommandInput
                                            placeholder="Search knowledge bases..."
                                            value={kbSearchTerm}
                                            onValueChange={setKbSearchTerm}
                                            disabled={isLoading || isError}
                                        />
                                        <CommandList>
                                            {filteredKnowledgeBasesSub.length > 0 && filteredKnowledgeBasesSub.map((kb) => (
                                                <DropdownMenuItem
                                                    key={kb.id}
                                                    disabled={kb.status !== "SUCCESS"}
                                                    onSelect={(event: any) => handleSelect({
                                                        id: kb.id,
                                                        name: kb.name,
                                                        status: kb.status,
                                                        type: 'knowledge_base'
                                                    }, event)}
                                                    className="cursor-pointer"
                                                >
                                                    <div className={cn(
                                                        "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                                        selectedSources.has(kb.id) ? "bg-primary text-primary-foreground" : "opacity-50"
                                                    )}>
                                                        {selectedSources.has(kb.id) && <Check className="h-3 w-3" />}
                                                    </div>
                                                    <Database className="mr-2 h-4 w-4" />
                                                    <span className={kb.hasDuplicate ? "font-medium" : ""}>
                                                        {kb.name}
                                                        {kb.hasDuplicate && <span className="ml-1 text-xs text-amber-500 hidden">(ID: {kb.id.substring(0, 6)})</span>}
                                                    </span>
                                                    <span className={cn(
                                                        "ml-auto text-xs",
                                                        kb.status === "SUCCESS" ? "text-green-500" : "text-yellow-500"
                                                    )}>
                                                        {kb.status === "SUCCESS" ? "Ready" : <span className="text-red-500">Processing</span>}
                                                    </span>
                                                </DropdownMenuItem>
                                            ))}
                                            {filteredKnowledgeBasesSub.length === 0 && (
                                                <p className="text-sm text-muted-foreground text-center">No knowledge bases found</p>
                                            )}
                                        </CommandList>
                                    </Command>
                                </DropdownMenuSubContent>
                            </DropdownMenuSub>
                        </>
                    )}
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
