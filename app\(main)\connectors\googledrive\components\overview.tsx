"use client";
import { formatDate } from '@/lib/utils'
import { ScrollArea } from '@steps-ai/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@steps-ai/ui'
import React, { useState } from 'react'
import Image from 'next/image'
import { Power, Loader2, RefreshCw, Mail, Star, MailOpen, User, TrendingUp } from "lucide-react"
import { queryClient, createMutationFn, useApiMutation } from "@/lib/apiClient"

interface WorkspaceUser {
    id: string;
    name: string;
    avatarUrl: string | null;
    type: 'person' | 'bot';
}

const Overview = ({ config }: { config?: { name: string; email: string; picture: string } }) => {
    // Disconnect logic
    const [isDisconnecting, setIsDisconnecting] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const disconnectGoogleDrive = useApiMutation(
        createMutationFn.post('/google-drive/disconnect'),
        {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['google-drive'] });
                setError(null)
                setIsDisconnecting(false)
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong while disconnecting."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error disconnecting from Google Drive")
                }
                setIsDisconnecting(false)
            }
        }
    )
    const handleDisconnect = async () => {
        setIsDisconnecting(true)
        await disconnectGoogleDrive.mutateAsync({})
    }

    return (
        <div className="grid gap-8 grid-cols-1 md:grid-cols-4 items-start">
            {/* User Info + Actions */}
            {config && (
                <Card className="flex flex-col items-center p-6 rounded-2xl shadow-lg bg-background/70 border-0 ">
                    <CardHeader className="flex flex-col items-center w-full">
                        <div className="relative w-20 h-20 rounded-full overflow-hidden bg-muted mb-4 border-4 border-primary/20">
                            <Image
                                src={config.picture}
                                alt={config.name}
                                fill
                                className="object-cover"
                            />
                        </div>
                        <CardTitle className="text-center text-xl font-bold flex items-center gap-2">
                            {config.name}
                        </CardTitle>
                        <CardDescription className="text-center text-sm text-muted-foreground">{config.email}</CardDescription>
                    </CardHeader>
                    <CardContent className="w-full flex flex-col items-center gap-3 mt-4">
                        <div className="flex gap-2 w-full">
                            <button
                                onClick={handleDisconnect}
                                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg border border-destructive text-destructive hover:bg-destructive/10 transition-colors disabled:opacity-60"
                                disabled={isDisconnecting}
                            >
                                {isDisconnecting ? (
                                    <><Loader2 className="w-4 h-4 animate-spin" />Disconnecting...</>
                                ) : (
                                    <><Power className="w-4 h-4" />Disconnect</>
                                )}
                            </button>
                        </div>
                        {error && <div className="text-xs text-destructive mt-2 w-full text-center">{error}</div>}
                    </CardContent>
                </Card>
            )}
        </div>
    )
}

export default Overview
