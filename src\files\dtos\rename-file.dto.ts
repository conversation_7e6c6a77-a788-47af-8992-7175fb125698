import { <PERSON><PERSON><PERSON>, <PERSON>Not<PERSON><PERSON>y, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RenameFileDto {
  @ApiProperty({
    example: 'New Name',
    description: 'Updated name of the file',
  })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'New name is required' })
  name: string;
}
