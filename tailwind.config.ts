import type { Config } from "tailwindcss"

const config = {
	darkMode: ["class"],
	content: [
		'./pages/**/*.{ts,tsx}',
		'./components/**/*.{ts,tsx}',
		'./app/**/*.{ts,tsx}',
		'./src/**/*.{ts,tsx}',
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				xsss: {
					min: '0',
					max: '160px'
				},
				xss: {
					min: '161',
					max: '320px'
				},
				xs: {
					min: '321',
					max: '640px'
				},
				sm: {
					min: '641px',
					max: '767px'
				},
				md: {
					min: '768px',
					max: '1023px'
				},
				lg: {
					min: '1024px',
					max: '1279px'
				},
				xl: {
					min: '1280px',
					max: '1535px'
				},
				'2xl': {
					min: '1536px'
				},
			}
		},
		extend: {
			fontFamily: {
				montserrat: ['Montserrat', 'sans-serif'],
			},
			colors: {
				boxShadow: {
					'right-lg': '10px 0 15px -3px rgba(0, 0, 0, 0.1), 4px 0 6px -4px rgba(0, 0, 0, 0.1)'
				},
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))',
					hover: 'hsl(var(--primary) / 90%)',
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))',
					hover: 'hsl(var(--accent) / 90%)',
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))',
					hover: 'hsl(var(--sidebar-accent) / 90%)',
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				orbit: {
					'0%': {
						transform: 'rotate(0deg) translateY(calc(var(--radius) * 1px)) rotate(0deg)'
					},
					'100%': {
						transform: 'rotate(360deg) translateY(calc(var(--radius) * 1px)) rotate(-360deg)'
					}
				},
				'logo-cloud': {
					from: {
						transform: 'translateX(0)'
					},
					to: {
						transform: 'translateX(calc(-100% - 4rem))'
					}
				},
				marquee: {
					from: {
						transform: 'translateX(0)'
					},
					to: {
						transform: 'translateX(calc(-100% - var(--gap)))'
					}
				},
				'marquee-vertical': {
					from: {
						transform: 'translateY(0)'
					},
					to: {
						transform: 'translateY(calc(-100% - var(--gap)))'
					}
				},
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				fadeIn: {
					'0%': {
						opacity: '0'
					},
					'100%': {
						opacity: '1'
					}
				},
				shimmer: {
					'0%, 90%, 100%': {
						'background-position': 'calc(-100% - var(--shimmer-width)) 0'
					},
					'30%, 60%': {
						'background-position': 'calc(100% + var(--shimmer-width)) 0'
					}
				},
				gradient: {
					to: {
						backgroundPosition: 'var(--bg-size) 0'
					}
				},

				'fade-in-up': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				shine: {
					"0%": {
						"background-position": "0% 0%",
					},
					"50%": {
						"background-position": "100% 100%",
					},
					to: {
						"background-position": "0% 0%",
					},
				},
			},
			backgroundImage: {
				'custom-gradient': 'linear-gradient(90deg, #f06756 0%, #9760a9 52.82%, #4859f3 100%)'
			},
			animation: {
				gradient: 'gradient 8s linear infinite',
				shimmer: 'shimmer 8s infinite',
				marquee: 'marquee var(--duration) linear infinite',
				orbit: 'orbit calc(var(--duration)*1s) linear infinite',
				'marquee-vertical': 'marquee-vertical var(--duration) linear infinite',
				'accordion-down': 'accordion-down 0.2s ease-out',
				'logo-cloud': 'logo-cloud 30s linear infinite',
				'accordion-up': 'accordion-up 0.2s ease-out',
				fadeIn: 'fadeIn 1.5s ease-in-out forwards',
				'fade-in-up': 'fade-in-up 0.5s ease-out',
				shine: "shine var(--duration) infinite linear",
			},
			typography: {
				DEFAULT: {
					css: {
						maxWidth: 'none',
						code: {
							backgroundColor: '#f3f4f6',
							padding: '2px 4px',
							borderRadius: '4px',
							font: 'inherit',
						},
						'code::before': {
							content: '""',
						},
						'code::after': {
							content: '""',
						},
						table: {
							borderCollapse: 'collapse',
							width: '100%',
						},
						'th,td': {
							border: '1px solid #e5e7eb',
							padding: '0.5rem',
							textAlign: 'left',
						},
						th: {
							backgroundColor: '#f9fafb',
						},
						// maxWidth: '100%',
					},
				},
			},
			zIndex: {
				'100': '100',
				'50': '50',
				'40': '40',
				'30': '30',
				'20': '20',
				'10': '10',
			},
		}
	},
	plugins: [
		require("tailwindcss-animate"),
		require('@tailwindcss/typography'),
	],
} satisfies Config

export default config