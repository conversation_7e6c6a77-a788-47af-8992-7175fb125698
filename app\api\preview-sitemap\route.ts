import sitemap from '../../sitemap'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const sitemapData = await sitemap()
    
    const formattedData = sitemapData.map(entry => ({
      url: entry.url,
      description: entry.description,
      lastModified: entry.lastModified,
      priority: entry.priority,
      changeFrequency: entry.changeFrequency
    }))
    
    return NextResponse.json(formattedData, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    // console.error('Sitemap generation error:', error)
    return NextResponse.json({ error: 'Failed to generate sitemap' }, { status: 500 })
  }
} 