import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { getToken } from "next-auth/jwt";

export const maxDuration = 30;
export const dynamic = "force-dynamic";

const agents = {
    "Gmail": "gmail",
    "Knowledge Base": "kb",
    "Web Search": "web_search"
}

export async function POST(req: NextRequest) {
    const session = await getSession();
    if (!session) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const token = await getToken({
        req: req as any,
        secret: process.env.NEXT_AUTH_SECRET,
        cookieName: "stepsai_secure",
    });

    const data = await req.json();
    const { message, chat_id, sources, webSearch, current_date, advanceSearch, assistant_name } = data;
    const kids = sources.filter((source: any) => source.type === "knowledge_base").map((source: any) => source.id);
    let data_sources: any = sources
        .filter((source: any) => source.type === "connector")
        .map((source: any) => agents[source.name as keyof typeof agents]);
    const etl_ids = sources.filter((source: any) => source.etlStatus !== "none").map((source: any) => source.connectorId);
    let finalKids = [...kids, ...etl_ids];

    finalKids = finalKids.filter((kid: any) => kid != null);

    if (finalKids.length >= 1) {
        data_sources = [...data_sources, "kb"];
    }

    const reqData = JSON.stringify({
        message,
        chat_id,
        model: "gpt-4o-mini",
        tools: [],
        data_sources: [
            ...data_sources,
            ...(webSearch ? ["Web Search"] : []),
            ...(finalKids.length > 0 ? ["Knowledge Base Documents"] : [])
        ],
        assistant_name: assistant_name,
        kids: finalKids,
        current_date: current_date
    })

    const test = JSON.stringify({
        message: message,
        userId: session?.user?.id,
        chatId: chat_id,
        orgId: "stepsai",
        kids: finalKids,
        agents: data_sources
    })
    console.log(test)
    const response = await fetch(`${process.env.NEXT_PUBLIC_CHAT_API_URL}/v1/chat-completion/stream`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token?.accessToken}`
        },
        body: test
    });
    return new Response(response.body, {
        headers: {
            "Content-Type": "text/event-stream",
        },
    });
}

