import { BadRequestException, Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'src/prisma/prisma.service';
import * as qrcode from 'qrcode';
import * as speakeasy from 'speakeasy';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { EmailService } from 'src/email/email.service';
import { randomBytes } from 'crypto';
import { JwtService } from '@nestjs/jwt';
import { AuthService } from 'src/auth/v1/auth.v1.service';
import { ConnectorsStatus, Status } from '@prisma/client';

@Injectable()
export class UserService {
  private saltRounds = 10;
  constructor(
    private prisma: PrismaService,
    private emailSerivice: EmailService,
    private authService: AuthService,
  ) { }

  async getUser(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        name: true,
        email: true,
        id: true,
        profileImageUrl: true,
        emailVerified: true,
        onboardingCompleted: true,
        role: true,
        organizationId: true,
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        plan: {
          select: {
            name: true,
            type: true,
          },
        },
      },
    });

    const userTeams = await this.prisma.team.findMany({
      where: {
        members: {
          some: {
            id: userId,
          },
        },
      },
      select: {
        id: true,
        name: true,
        description: true,
        organizationId: true,
      },
    });

    return {
      ...user,
      // teams: userTeams.length > 0 ? userTeams : null,
      // currentTeam: userTeams.length > 0 ? userTeams[0] : null,
    };
  }

  async updatePassword(userId: string, password: string) {
    const hashedPassword = await bcrypt.hash(password, this.saltRounds);
    await this.prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        password: hashedPassword,
      },
    });
    return 'Password updated successfully';
  }

  async updateUser(userId: string, data: any) {
    await this.prisma.user.update({
      where: {
        id: userId,
      },
      data: data,
    });
    return 'User updated successfully';
  }

  async enableTwoFactorAuth(user: JwtPayload) {
    const secret = speakeasy.generateSecret({
      length: 20,
      name: `AIDE-(${user.name})`,
    });

    await this.prisma.user.update({
      where: {
        id: user.sub,
      },
      data: {
        twoFactorsecret: secret.base32,
      },
    });
    const code = await qrcode.toDataURL(secret.otpauth_url);
    return { code };
  }

  async verifyTwoFactorAuth(user: JwtPayload, code: string) {
    const secret = await this.prisma.user.findUnique({
      where: {
        id: user.sub,
      },
      select: {
        twoFactorsecret: true,
      },
    });
    const isValid = speakeasy.totp.verify({
      secret: secret.twoFactorsecret,
      encoding: 'base32',
      token: code,
      window: 1,
    });
    if (!isValid) {
      return false;
    }
    await this.prisma.user.update({
      where: {
        id: user.sub,
      },
      data: {
        twoFactorEnabled: true,
      },
    });
    return true;
  }

  async disableTwoFactorAuth(user: JwtPayload) {
    await this.prisma.user.update({
      where: {
        id: user.sub,
      },
      data: {
        twoFactorEnabled: false,
        twoFactorsecret: null,
      },
    });
    return true;
  }

  async sendEmailVerification(userId: string) {
    const tokenExpiry = new Date();
    tokenExpiry.setHours(tokenExpiry.getHours() + 24);

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        name: true,
      },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Get JWT token from auth service
    const jwtToken =
      await this.authService.createEmailVerificationToken(userId);

    // Store the verification token from the decoded JWT
    const { verificationToken } =
      await this.authService.verifyEmailToken(jwtToken);

    await this.prisma.user.update({
      where: { id: userId },
      data: {
        emailVerificationToken: verificationToken,
        emailVerificationTokenExpiry: tokenExpiry,
      },
    });

    const details = await this.emailSerivice.sendVerificationEmail(
      user.email,
      user.name,
      jwtToken,
    );

    return 'Email verification sent successfully';
  }

  async verifyEmail(jwtToken: string) {
    try {
      // Verify token using auth service
      const { userId, verificationToken } =
        await this.authService.verifyEmailToken(jwtToken);

      const user = await this.prisma.user.findUnique({
        where: {
          emailVerificationToken: verificationToken,
          id: userId,
        },
      });
      console.log(user);

      if (!user) {
        throw new BadRequestException('Invalid verification token');
      }

      if (
        user.emailVerificationTokenExpiry &&
        user.emailVerificationTokenExpiry < new Date()
      ) {
        throw new BadRequestException('Verification token has expired');
      }

      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerified: true,
        },
      });
      return 'Email verified successfully';
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Invalid or expired token');
    }
  }

  async finishOnboarding(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    await this.prisma.user.update({
      where: { id: userId },
      data: { hasOnboarded: true },
    });
    return 'Onboarding finished successfully';
  }

  async getChatConfigs(userId: string) {
    // Fetch all knowledgebases the user owns or is a member of, without status
    const knowledgebases = await this.prisma.knowledgeBase.findMany({
      where: {
        AND: [
          { OR: [{ ownerId: userId }, { members: { some: { id: userId } } }] },
        ],
      },
      select: { id: true, name: true, description: true },
    });

    // For each knowledgebase, fetch the latest ETL run and set status
    const knowledgebasesWithStatus = await Promise.all(
      knowledgebases.map(async (kb) => {
        const latestEtl = await this.prisma.etl.findFirst({
          where: { knowledgeBaseId: kb.id },
          orderBy: { startDate: 'desc' },
          select: { status: true },
        });
        return {
          ...kb,
          status: latestEtl?.status ?? 'NEW',
        };
      })
    );

    const groupedKnowledgebases = {
      Ready: knowledgebasesWithStatus.filter(
        (kb) => kb.status === Status.SUCCESS || kb.status === Status.NEW,
      ),
      Processing: knowledgebasesWithStatus.filter(
        (kb) => kb.status === Status.PROCESSING,
      ),
    };

    const userConnectors = await this.prisma.userConnectors.findMany({
      where: {
        userId: userId,
        connector: {
          status: ConnectorsStatus.AVAILABLE,
        },
      },
      select: {
        id: true,
        connector: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true,
          },
        },
        etls: {
          select: { id: true, status: true, startDate: true, endDate: true },
        },
      },
    });

    const availableConnectors = await this.prisma.connectors.findMany({
      where: {
        status: ConnectorsStatus.AVAILABLE,
      },
    });

    const groupedConnectors = {
      Connected: userConnectors.map((uc) => ({
        id: uc.connector.id,
        name: uc.connector.name,
        slug: uc.connector.slug,
        logo: uc.connector.logo,
        etlStatus: uc.etls.length > 0 ? uc.etls[0].status : null,
        userConnectorId: uc.id,
      })),
      Available: availableConnectors
        .filter((ac) => !userConnectors.some((uc) => uc.connector.id === ac.id))
        .map((c) => ({
          id: c.id,
          name: c.name,
          slug: c.slug,
          logo: c.logo,
        })),
    };

    return {
      knowledgebases: groupedKnowledgebases,
      connectors: groupedConnectors,
    };
  }

  async completeIntialOnboarding(userId: string, data: any) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });
    if (!user) {
      throw new BadRequestException('User not found');
    }
    await this.prisma.user.update({
      where: { id: userId },
      data: { onboardingCompleted: true, onboardingData: data },
    });
    return 'Onboarding completed successfully';
  }
  async checkInitialOnboardingCompleted(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { onboardingCompleted: true },
    });
    return user.onboardingCompleted;
  }
}
