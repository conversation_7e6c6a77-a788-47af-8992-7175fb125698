export const resetPasswordTemplate = (name: string, url: string) => {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                margin: 0;
                padding: 0;
                background-color: #f6f9fc;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            }

            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 40px 20px;
            }

            .card {
                background-color: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .title {
                color: #1a1a1a;
                font-size: 24px;
                margin-bottom: 24px;
                text-align: center;
            }

            .text {
                color: #4a5568;
                font-size: 16px;
                line-height: 24px;
                margin-bottom: 32px;
            }

            .button-container {
                text-align: center;
            }

            .button {
                display: inline-block;
                background-color: #4f46e5;
                color: white;
                text-decoration: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: 500;
            }

            .small-text {
                color: #718096;
                font-size: 14px;
                line-height: 20px;
                margin-top: 32px;
                text-align: center;
            }

            .footer {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e2e8f0;
                text-align: center;
                color: #718096;
                font-size: 14px;
                line-height: 24px;
            }

            .footer a {
                color: #4f46e5;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="card">
                <h1 class="title">Reset Your Password</h1>
                
                <p class="text">Hi ${name},</p>
                
                <p class="text">We received a request to reset your password. Click the button below to create a new password:</p>
                
                <div class="button-container">
                    <a href="${url}" class="button">Reset Password</a>
                </div>
                
                <p class="small-text">If you didn't request a password reset, you can safely ignore this email.</p>

                <div class="footer">
                    <p>Questions or need guidance? Our support team is ready at
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                    <p>We're excited to be part of your data intelligence journey!</p>
                    <p>Best regards,<br />The StepsAI Team</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;
};