'use client'

import { useEffect } from 'react'
import { signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button' // Assuming you have shadcn/ui or similar

export default function Error({
    error,
    reset,
}: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    useEffect(() => {
        // Log the error to an error reporting service
        console.error(error)
    }, [error])

    return (
        <div className="min-h-screen flex flex-col items-center justify-center p-4">
            <div className="text-center space-y-6">
                <h1 className="text-4xl font-bold text-red-600">Oops! Something went wrong</h1>

                <p className="text-gray-600 max-w-md mx-auto">
                    {error.message || 'An unexpected error occurred. Please try again later.'}
                </p>

                <div className="space-x-4">
                    <Button
                        onClick={reset}
                        variant="outline"
                    >
                        Try again
                    </Button>

                    <Button
                        onClick={() => signOut({ callbackUrl: '/' })}
                        variant="destructive"
                    >
                        Sign Out
                    </Button>
                </div>
            </div>
        </div>
    )
}
