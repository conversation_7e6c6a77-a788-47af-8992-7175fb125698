import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { HubspotService } from './hubspot.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { HubspotCallbackDto, HubspotRefreshTokenDto } from './dtos/hubspot.dto';

@ApiTags('Hubspot-V1')
@Controller({ version: '1', path: 'hubspot' })
export class HubspotController {
  constructor(private hubspotService: HubspotService) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate Notion Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated Notion Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.hubspotService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle Notion Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed Notion Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: HubspotCallbackDto) {
    return this.hubspotService.handleCallback(body.code, body.state);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect Notion Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected Notion Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.hubspotService.disconnect(user.sub);
  }

  @Post('refresh-token')
  @ApiOperation({ summary: 'Refresh Hubspot Auth Token' })
  @ApiResponse({
    status: 200,
    description: 'Successfully refreshed Gmail Auth Token',
  })
  async refreshToken(@Body() body: HubspotRefreshTokenDto) {
    return this.hubspotService.refreshToken(body.userId);
  }
}
