"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeftIcon, RefreshCwIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { motion, Variants } from "framer-motion";
import { useSearchParams } from "next/navigation";
import { Skeleton } from "@steps-ai/ui";
// import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@steps-ai/ui";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Share2, Settings, Database, Activity } from "lucide-react";
import Overview from "./sections/overview";
import { useApiQuery } from "@/lib/apiClient";
import FileManager from "./sections/fileManger";
import ShareKnowledgebase from "./sections/shareKnowledgebse";
import { Separator } from "@steps-ai/ui";
import { toast } from "sonner";

const container: Variants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

const item: Variants = {
  hidden: { y: 20, opacity: 0 },
  show: { y: 0, opacity: 1 },
};

const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'NEW': return 'bg-blue-100 text-blue-800';
      case 'UPLOADED': return 'bg-purple-100 text-purple-800';
      case 'PROCESSING': return 'bg-yellow-100 text-yellow-800';
      case 'SUCCESS': return 'bg-green-100 text-green-800';
      case 'ERROR': return 'bg-red-100 text-red-800';
      case 'RUNNING': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status.toUpperCase()) {
      case 'NEW': return 'New';
      case 'UPLOADED': return 'Uploaded';
      case 'PROCESSING': return 'Processing';
      case 'SUCCESS': return 'Ready';
      case 'ERROR': return 'Error';
      case 'RUNNING': return 'Running';
      default: return 'Unknown';
    }
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
      {getStatusLabel(status)}
    </span>
  );
};

export default function KnowledgeBaseDetailsClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get("id") as string;
  const currentTab = searchParams.get("tab") || "files";
  const [activeTab, setActiveTab] = useState(currentTab);

  const [search, setSearch] = useState("");
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  useEffect(() => {
    if (!id) {
      router.push("/knowledgebase");
      toast.error("Knowledge base ID is required");
    }
  }, [id, router]);

  const handleTabChange = (tab: string) => {
    if (!id) return;
    setActiveTab(tab);
    router.replace(`?id=${id}&tab=${tab}`);
  };

  useEffect(() => {
    setActiveTab(currentTab);
  }, [currentTab]);

  const queryKey = ["knowledgebasedata", id?.toString() ?? "", search, sortBy, sortOrder];
  const { data, isLoading, isError, error, refetch } = useApiQuery<any>(
    queryKey,
    `/knowledgebase/${id ?? ""}`,
    {
      enabled: Boolean(id),
      queryKey,
      placeholderData: (previousData: any) => previousData
    }
  );
  useEffect(() => {
    if (!data || isLoading) return;

    if (data.result.status && (data.result.status.toUpperCase() !== 'SUCCESS' && data.result.status.toUpperCase() !== 'NEW')) {
      const interval = setInterval(() => {
        refetch();
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [data?.result.status, isLoading, refetch]);

  if (!id) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCwIcon className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="px-2 sm:px-6 md:px-20 rounded-lg md:space-y-2 max-w-full overflow-x-hidden">
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6 mb-4">
        <div className="w-full">
          <Button
            variant="ghost"
            onClick={() => router.push("/knowledgebase")}
            className="mb-2 text-text-secondary hover:text-text-primary text-[10px] sm:text-xs transition-colors"
          >
            <ArrowLeftIcon className="h-2 w-2 mr-1" />
            <span className="truncate">Back to all Knowledge Bases</span>
          </Button>

          <div className="flex flex-row justify-between gap-4 w-full">
            <h1 className="text-xl sm:text-3xl md:text-4xl font-bold flex flex-row gap-2 sm:gap-4 text-text-primary items-center">
              <Database className="h-6 w-6 sm:h-10 sm:w-10 text-primary flex-shrink-0" />
              <span className="truncate">
                {isLoading ? <Skeleton className="h-8" /> : data?.result.name}
              </span>
              {!isLoading && data?.result.status && (
                <StatusBadge status={data.result.status} />
              )}
            </h1>

            <Tabs value={activeTab} onValueChange={handleTabChange}>
              <TabsList>
                <TabsTrigger
                  value="files"
                  className="flex-1 sm:flex-initial items-center justify-center px-2 sm:px-4 py-1"
                >
                  <FileText className="h-4 w-4 sm:mr-2" />
                  <span className="text-xs sm:text-sm hidden sm:inline">Files</span>
                </TabsTrigger>
                <TabsTrigger
                  value="share"
                  className="flex-1 sm:flex-initial items-center justify-center px-2 sm:px-4 py-1"
                >
                  <Share2 className="h-4 w-4 sm:mr-2" />
                  <span className="text-xs sm:text-sm hidden sm:inline">Share</span>
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  className="flex-1 sm:flex-initial items-center justify-center px-2 sm:px-4 py-1"
                >
                  <Settings className="h-4 w-4 sm:mr-2" />
                  <span className="text-xs sm:text-sm hidden sm:inline">Settings</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </div>

      <Separator className="my-4 sm:my-6" />

      <div
        className="flex flex-col md:flex-row justify-between items-start gap-4 sm:gap-6 w-full"
      >
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsContent value="settings" className="mt-2">
            <Overview data={data} isLoading={isLoading} />
          </TabsContent>
          <TabsContent value="share" className="mt-0">
            <ShareKnowledgebase data={data} isLoading={isLoading} />
          </TabsContent>
          <TabsContent value="files" className="mt-2">
            <FileManager
              data={data}
              isLoading={isLoading}
              search={search}
              setSearch={setSearch}
              sortBy={sortBy}
              setSortBy={setSortBy}
              sortOrder={sortOrder}
              setSortOrder={setSortOrder}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
