"use client"
import { useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@steps-ai/ui"
import Image from "next/image"
import { User, Link as LinkIcon } from "lucide-react"

import { Form,FormControl,FormDescription,FormField,FormItem,FormLabel,FormMessage } from "@/components/ui/form"
import { Input } from "@steps-ai/ui"
import {
  useApiQuery, useApiMutation,
  createMutationFn,
  queryClient
} from "@/lib/apiClient"
import { Skeleton } from "@steps-ai/ui"
import axios from "axios"
import { toast } from "sonner"
import { useState } from "react"
import { organizationApi, type Connector } from '@/lib/api/organization'

const accountFormSchema = z.object({
  name: z
    .string()
    .min(2, {
      message: "Name must be at least 2 characters.",
    })
    .max(30, {
      message: "Name must not be longer than 30 characters.",
    }),
  email: z
    .string()
    .email({
      message: "Please enter a valid email address.",
    })
    .min(2, {
      message: "Email must be at least 2 characters.",
    })
    .max(50, {
      message: "Email must not be longer than 50 characters.",
    })
})

type AccountFormValues = z.infer<typeof accountFormSchema>

export function AccountForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [profilePhoto, setProfilePhoto] = useState<string | null>()
  const [loading, setLoading] = useState(false)
  const [userConnectors, setUserConnectors] = useState<Connector[]>([])
  const [isLoadingConnectors, setIsLoadingConnectors] = useState(false)

  const { data, isLoading, refetch } = useApiQuery<{
    status: boolean;
    statusCode: number;
    result: {
      name: string;
      email: string;
      id: string;
      profileImageUrl: string | null;
      emailVerified: boolean;
    };
    version: string;
  }>(['User'], '/user/me');


  const updateName = useApiMutation(
    createMutationFn.post('/user/update-user'),
    {
      onSuccess: (response: any) => {
        if (response && response.status) {
          queryClient.invalidateQueries({ queryKey: ['user'] });
          toast.success("Name updated successfully");
        } else {
          toast.error("Something went wrong. Please try again.");
        }
      },
      onError: (error: any) => {
        if (error.response) {
          const errorMessage = error.response.data?.message ||
            "Something went wrong. Please try again.";
          toast.error(errorMessage);
        } else if (error.request) {
          toast.error("No response received from server");
        } else {
          toast.error("Error setting up the request");
        }
      }
    }
  );


  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      name: data?.result?.name || "",
      email: data?.result?.email || "",
    },
  })
  useEffect(() => {
    if (data?.result) {
      form.reset({
        name: data.result.name,
        email: data.result.email,
      })
      setProfilePhoto(data.result.profileImageUrl)
      loadUserConnectors()
    }
  }, [data, form])

  const loadUserConnectors = async () => {
    if (!data?.result?.id) return

    try {
      setIsLoadingConnectors(true)
      const userConnectorData = await organizationApi.getUserConnectors(data.result.id)
      setUserConnectors(userConnectorData.connectors || [])
    } catch (error) {
      console.error('Failed to load user connectors:', error)
      setUserConnectors([])
    } finally {
      setIsLoadingConnectors(false)
    }
  }

  // async function onSubmit(values: AccountFormValues) {
  //   try {
  //     setIsSubmitting(true)
  //     await updateUser.mutateAsync({
  //       name: values.name,
  //     })

  //     await refetch()

  //     toast.success("Profile updated successfully")
  //   } catch (error) {
  //     toast.error("Something went wrong. Please try again.")
  //   } finally {
  //     setIsSubmitting(false)
  //   }
  // }

  async function onSubmit(values: AccountFormValues) {
    setIsSubmitting(true)
    await updateName.mutateAsync({
      name: values.name,
    })
    setIsSubmitting(false)
  }


  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setProfilePhoto(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }
  const updateProfilePicture = useApiMutation(
    createMutationFn.post('/user/update-user'),
    {
      onSuccess: (response: any) => {
        if (response && response.status) {
          queryClient.invalidateQueries({ queryKey: ['user'] });
          toast.success("Profile photo updated successfully");
        } else {
          toast.error("Something went wrong. Please try again.");
        }
      },
      onError: (error: any) => {
        if (error.response) {
          const errorMessage = error.response.data?.message ||
            "Something went wrong. Please try again.";
          toast.error(errorMessage);
        } else if (error.request) {
          toast.error("No response received from server");
        } else {
          toast.error("Error setting up the request");
        }
      }
    }
  );
  const handleSubmit = async () => {
    if (!profilePhoto || !data?.result?.id) return

    const response = await fetch(profilePhoto);
    const blob = await response.blob();
    const file = new File([blob], data?.result?.id, { type: blob.type });

    const formData = new FormData();
    formData.append('files', file);
    formData.append('folderName', `profile`);

    try {
      setLoading(true);
      const response = await axios.post(process.env.NEXT_PUBLIC_UPLOAD_URL as string, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });
      if (!response.data.status) {
        toast.error(response.data.message);
        return;
      }

      const profileData = {
        profileImageUrl: response.data.result.uploadResults[0].url
      }
      updateProfilePicture.mutate(profileData);
      setLoading(false);
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
      setLoading(false);
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-4 w-[250px]" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-4 w-[250px]" />
        </div>
        <Skeleton className="h-10 w-28" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="bg-card border rounded-lg p-4 sm:p-6">
        <h4 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg">Profile Photo</h4>
        <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6">
          <div className="relative w-24 h-24 sm:w-32 sm:h-32 rounded-full overflow-hidden border-2 border-border shadow-lg">
            {profilePhoto ? (
              <img src={profilePhoto} alt="Profile" className="w-full h-full object-cover" />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <User className="h-12 w-12 sm:h-16 sm:w-16 text-muted-foreground" />
              </div>
            )}
          </div>
          <div className="flex flex-col gap-3 items-center sm:items-start">
            <Input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
              id="profile-photo-upload"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById('profile-photo-upload')?.click()}
              className="w-full sm:w-auto"
            >
              Select Photo
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={!profilePhoto || loading}
              className="w-full sm:w-auto"
            >
              {loading ? "Uploading..." : "Upload Photo"}
            </Button>
          </div>
        </div>
      </div>

      {/* Account Information */}
      <div className="bg-card border rounded-lg p-4 sm:p-6">
        <h4 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg">Account Information</h4>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 sm:space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm sm:text-base">Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Your name"
                      {...field}
                      disabled={isSubmitting}
                      aria-disabled={isSubmitting}
                      className="h-10 sm:h-11 mt-2"
                    />
                  </FormControl>
                  <FormDescription className="text-xs sm:text-sm">
                    This is the name that will be displayed on your profile and in emails.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm sm:text-base">Email</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Your email"
                      {...field}
                      disabled
                      aria-disabled="true"
                      className="bg-muted h-10 sm:h-11 mt-2"
                    />
                  </FormControl>
                  <FormDescription className="text-xs sm:text-sm">
                    Your email is used to login to your account.
                    {!data?.result?.emailVerified && (
                      <span className="text-yellow-600 ml-1">
                        (Not verified)
                      </span>
                    )}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-center sm:justify-end pt-4 border-t">
              <Button
                type="submit"
                disabled={isSubmitting || !form.formState.isDirty}
                className="w-full sm:w-auto px-6 sm:px-8"
              >
                {isSubmitting ? "Updating..." : "Update Account"}
              </Button>
            </div>
          </form>
        </Form>
      </div>

      <div className="bg-card border rounded-lg p-4 sm:p-6">
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <h4 className="font-semibold text-base sm:text-lg">Connected Services</h4>
          <span className="text-xs sm:text-sm text-muted-foreground">{userConnectors.length} services</span>
        </div>
        {isLoadingConnectors ? (
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <Skeleton className="h-10 w-10 rounded" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-5 w-16" />
              </div>
            ))}
          </div>
        ) : userConnectors.length > 0 ? (
          <div className="space-y-3">
            {userConnectors.map((connector) => (
              <div key={connector.connectorId} className="flex items-center gap-3 p-3 bg-muted/30 hover:bg-muted/50 transition-colors duration-200 rounded-lg">
                <div className="relative">
                  <img
                    src={connector.logo}
                    alt={connector.name}
                    className="h-10 w-10 rounded object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className="hidden h-10 w-10 bg-primary/10 rounded items-center justify-center">
                    <LinkIcon className="h-5 w-5 text-primary" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <p className="text-sm font-medium truncate">{connector.name}</p>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      connector.status === 'ACTIVE'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        : connector.status === 'ERROR'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                    }`}>
                      {connector.status}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground truncate">{connector.category}</p>
                  <p className="text-xs text-muted-foreground">
                    Connected {new Date(connector.connectedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="flex flex-col items-center gap-3">
              <div className="w-12 h-12 bg-muted/50 rounded-full flex items-center justify-center">
                <LinkIcon className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <p className="text-sm font-medium text-foreground">No services connected</p>
                <p className="text-xs text-muted-foreground mt-1">Connect your favorite tools and services to get started</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}