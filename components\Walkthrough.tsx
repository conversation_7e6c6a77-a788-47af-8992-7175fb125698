'use client';

import { queryClient, useApiQuery } from '@/lib/apiClient';
import { useApiMutation } from '@/lib/apiClient';
import { createMutationFn } from '@/lib/apiClient';
import { useState, useEffect } from 'react';
import Joyride, { STATUS, Step } from 'react-joyride';

const WALKTHROUGH_STORAGE_KEY = 'hasCompletedWalkthrough';

interface AppWalkthroughProps {
    isFirstTime: boolean;
}

const steps: Step[] = [
    {
        target: '#knowledgebase',
        title: 'Welcome to Your Knowledge Hub! 📚',
        content: 'This is your personal knowledge center where you can upload, manage, and organize all your important documents and data. AIDE will use this information to provide more accurate and personalized responses.',
        disableBeacon: true,
        disableOverlayClose: true,
        hideCloseButton: true,
    },
    {
        target: '#connectors',
        title: 'Connect Your Data Sources 🔌',
        content: 'Easily integrate with your favorite tools and platforms! Connect to services like Google Drive, Notion, or your company\'s internal systems to make your AI experience even more powerful.',
        disableBeacon: true,
        disableOverlayClose: true,
        hideCloseButton: true,
    },
    {
        target: '#chat',
        title: 'Start a Conversation 💭',
        content: 'This is where the magic happens! Chat with AIDE, your AI assistant, ask questions, get insights, and collaborate on tasks. AIDE will use your connected knowledge and data sources to provide informed responses.',
        disableBeacon: true,
        disableOverlayClose: true,
        hideCloseButton: true,
    },
    {
        target: '#data-sources',
        title: 'Select Your Data Sources 📊',
        content: 'Select the data sources you want AIDE to use to provide more accurate and personalized responses. You can connect to Google Drive, Notion, do an internet search, or your company\'s internal systems.',
        disableBeacon: true,
        disableOverlayClose: true,
        hideCloseButton: true,
    },
    {
        target: '#settings',
        title: 'Customize Your Experience ⚙️',
        content: 'Personalize your AIDE experience! Adjust your preferences, manage your account settings, and configure the platform to work exactly how you want it.',
        disableBeacon: true,
        disableOverlayClose: true,
        hideCloseButton: true,
    },
    {
        target: '#documentation',
        title: 'Learn More 📖',
        content: 'Need help? Our comprehensive documentation has everything you need to know about using AIDE, from basic conversations to adding multiple data sources and more.',
        disableBeacon: true,
        disableOverlayClose: true,
        hideCloseButton: true,
    },
];

const AppWalkthrough = ({ isFirstTime }: AppWalkthroughProps) => {
    const [isOpen, setIsOpen] = useState(true);
    const [isMobile, setIsMobile] = useState(false);

    // Initialize tour state
    const [runTour, setRunTour] = useState(() => {
        if (typeof window === 'undefined') return false;

        const storedWalkthrough = localStorage.getItem(WALKTHROUGH_STORAGE_KEY);
        if (storedWalkthrough !== null) {
            return storedWalkthrough === 'false';
        }
        return !isFirstTime;
    });

    // Check for mobile screen size
    useEffect(() => {
        if (typeof window === 'undefined') return;

        const checkMobile = () => {
            setIsMobile(window.matchMedia('(max-width: 768px)').matches);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    // Ensure visibility when tour starts on mobile
    useEffect(() => {
        if (isMobile && runTour) {
            setIsOpen(true);
        }
    }, [runTour, isMobile]);

    const onboardUser = useApiMutation(
        createMutationFn.post('/user/finish-onboarding'),
        {
            onSuccess: (response: any) => {
                if (response?.status) {
                    queryClient.invalidateQueries({ queryKey: ['user'] });
                    if (typeof window !== 'undefined') {
                        localStorage.setItem(WALKTHROUGH_STORAGE_KEY, 'true');
                    }
                }
                // console.log("User onboarded");
            },
            onError: (error: any) => {
                console.error('Failed to complete onboarding:', error);
            }
        }
    );

    const handleJoyrideCallback = (data: any) => {
        const { status, action } = data;

        if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
            setRunTour(false);
            onboardUser.mutate({ hasOnboarded: true });

            // Close on mobile after tour
            if (isMobile) {
                setIsOpen(false);
            }
        }
    };

    // Don't render tour if closed on mobile
    if (isMobile && !isOpen) {
        return null;
    }

    return (
        <Joyride
            steps={steps}
            run={runTour}
            continuous={true}
            showProgress={false}
            showSkipButton={false}
            disableOverlayClose={true}
            styles={{
                options: {
                    primaryColor: '#4b5563',
                    backgroundColor: '#ffffff',
                    textColor: '#000000',
                    arrowColor: '#ffffff',
                    overlayColor: 'rgb(0, 0, 0)',
                    spotlightShadow: '0 0 15px rgba(0, 0, 0, 0.5)',
                    zIndex: 10000,
                },
                overlay: {
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                },
                tooltip: {
                    backgroundColor: '#ffffff',
                    color: '#000000',
                },
                buttonNext: {
                    backgroundColor: '#4b5563',
                    color: '#ffffff',
                },
                buttonBack: {
                    color: '#4b5563',
                },
                buttonClose: {
                    color: '#4b5563',
                },
            }}
            callback={handleJoyrideCallback}
        />
    );
};

export default AppWalkthrough;