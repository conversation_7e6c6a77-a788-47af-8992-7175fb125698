'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { Loader2, ShieldCheck, CheckCircle2, Building, User, AtSign, EyeIcon, EyeOffIcon } from 'lucide-react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import axios from 'axios';
import BlobBackground from '@/components/Organization/BlobBackground';

interface AdminData {
  id: string;
  email: string;
  name?: string;
}

interface OrgData {
  id: string;
  name: string;
  logo: string;
}

interface VerificationResponse {
  status: boolean;
  statusCode: number;
  result: {
    success: boolean;
    message: string;
    tokenType: 'org-admin-invite' | 'org-invitation';
    isAdmin: boolean;
    user: AdminData;
    organization: OrgData;
    nextStep: 'update-admin' | 'complete-invitation';
    completionEndpoint: string;
  };
  version: string;
}

export default function OnboardingPage() {
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [verificationStep, setVerificationStep] = useState<'verifying' | 'success' | 'form' | 'error'>('verifying');
  const [adminData, setAdminData] = useState<AdminData | null>(null);
  const [orgData, setOrgData] = useState<OrgData | null>(null);
  const [verificationData, setVerificationData] = useState<VerificationResponse | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    password: '',
    confirmPassword: '',
  });

  const [formErrors, setFormErrors] = useState({
    name: false,
    password: false,
    confirmPassword: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const verifyToken = async () => {
      try {
        const token = searchParams.get('token');

        if (!token) {
          setError('Invalid token');
          setVerificationStep('error');
          setIsLoading(false);
          return;
        }

        const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/users/verify-token`, {
          token
        });
        const data = response.data as VerificationResponse;

        if (data.status && data.result.success) {
          setVerificationData(data);
          setAdminData(data.result.user);
          setOrgData(data.result.organization);
          setVerificationStep('form');

          if (data.result.user?.name) {
            setFormData(prev => ({
              ...prev,
              name: data.result.user?.name || ''
            }));
          }
        } else {
          throw new Error(data.result?.message || 'Verification failed');
        }
      } catch (err: any) {
        let errorMessage = 'An error occurred during verification';

        if (err.response) {
          errorMessage = err.response.data?.message || err.response.data?.result?.message || 'API Error';
        } else if (err.request) {
          errorMessage = 'Network error - please check your connection';
        } else {
          errorMessage = err.message || 'Unknown error occurred';
        }

        setError(errorMessage);
        setVerificationStep('error');
      } finally {
        setIsLoading(false);
      }
    };

    // Only run verification if we're in the verifying step and haven't verified yet
    if (verificationStep === 'verifying' && !verificationData) {
      verifyToken();
    }
  }, [searchParams, verificationStep, verificationData]);

  const validateForm = () => {
    const errors = {
      name: !formData.name.trim(),
      password: formData.password.length < 8,
      confirmPassword: formData.password !== formData.confirmPassword
    };

    setFormErrors(errors);
    return !Object.values(errors).some(Boolean);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (!adminData || !orgData || !verificationData) {
        throw new Error('Missing verification data');
      }

      const token = searchParams.get('token');
      if (!token) {
        throw new Error('Missing token');
      }

      let endpoint = '';
      let payload: any = {};

      if (verificationData.result.isAdmin) {
        endpoint = `${process.env.NEXT_PUBLIC_API_URL}/users/update-admin`;
        payload = {
          id: adminData.id,
          organizationId: orgData.id,
          name: formData.name,
          password: formData.password
        };
      } else {
        endpoint = `${process.env.NEXT_PUBLIC_API_URL}/users/invitation/complete`;
        payload = {
          token: token,
          name: formData.name,
          password: formData.password
        };
      }

      const response = await axios.post(endpoint, payload);

      if (response.data.success || response.data.status) {
        setVerificationStep('success');

        // Sign in the user
        setTimeout(async () => {
          try {
            const result = await signIn('login', {
              email: adminData.email,
              password: formData.password,
              redirect: false
            });

            if (result?.ok) {
              window.location.href = '/organization';
            } else {
              window.location.href = '/?error=login_failed';
            }
          } catch (error) {
            window.location.href = '/?error=signin_error';
          }
        }, 1500);
      } else {
        throw new Error('Failed to complete setup');
      }

    } catch (err) {
      toast.error('Failed to complete setup. Please try again.');
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen w-full bg-background flex items-center justify-center p-4">
      <AnimatePresence mode="wait">
        {verificationStep === 'verifying' && (
          <motion.div
            key="verifying"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col items-center justify-center"
          >
            <div className="relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
              </div>
              <Loader2 className="h-16 w-16 text-primary animate-pulse" />
            </div>
            <p className="text-muted-foreground mt-6 animate-pulse">
              Verifying your access...
            </p>
          </motion.div>
        )}

        {verificationStep === 'form' && adminData && orgData && (
          <motion.div
            key="form"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="w-full max-w-4xl mx-auto"
          >
            <Card className="bg-card border shadow-2xl overflow-hidden">
              <div className="flex flex-col md:flex-row min-h-[600px]">
                {/* Left side - BlobBackground with org details */}
                <div className="w-full md:w-1/2">
                  <BlobBackground className="h-full min-h-[300px] md:min-h-[600px]">
                    <div className="flex flex-col justify-between h-full p-8">
                      {/* Header */}
                      <div>
                        <div className="flex items-center gap-3 mb-8">
                          <Image
                            src="/new-logo.svg"
                            alt="StepsAI"
                            width={40}
                            height={40}
                            className="rounded-xl"
                          />
                          <h1 className="text-2xl font-bold text-white">
                            StepsAI
                          </h1>
                        </div>
                      </div>

                      {/* Organization Info */}
                      <div className="flex-1 flex items-center">
                        <div className="w-full">
                          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                            <div className="flex items-center gap-4 mb-6">
                              {orgData.logo ? (
                                <div className="relative w-16 h-16 rounded-xl overflow-hidden border-2 border-white/20">
                                  <Image
                                    src={orgData.logo}
                                    alt={orgData.name}
                                    fill
                                    className="object-cover"
                                  />
                                </div>
                              ) : (
                                <div className="w-16 h-16 bg-white/10 rounded-xl flex items-center justify-center border-2 border-white/20">
                                  <Building className="h-8 w-8 text-white/60" />
                                </div>
                              )}
                              <div>
                                <h2 className="text-xl font-bold text-white">{orgData.name}</h2>
                                <p className="text-white/60 text-sm">Organization Setup</p>
                              </div>
                            </div>

                            <div className="space-y-4">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center">
                                  <AtSign className="h-4 w-4 text-white/80" />
                                </div>
                                <div>
                                  <p className="text-white/60 text-xs">Admin Email</p>
                                  <p className="text-white font-medium text-sm">{adminData.email}</p>
                                </div>
                              </div>

                              {/* <div className="flex items-center gap-3">
                                <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center">
                                  <User className="h-4 w-4 text-white/80" />
                                </div>
                                <div>
                                  <p className="text-white/60 text-xs">Admin ID</p>
                                  <p className="text-white font-medium text-sm">{adminData.id.substring(0, 12)}...</p>
                                </div>
                              </div> */}
                            </div>

                            <div className="mt-6 pt-4 border-t border-white/10">
                              <p className="text-white/80 italic text-sm">
                                "Complete your account setup to access your organization's dashboard."
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Footer */}
                      <div className="flex mx-auto my-2 items-center gap-2 text-white/60 text-xs">
                        <ShieldCheck className="w-4 h-4" />
                        <span>Secure Enterprise Setup</span>
                      </div>
                    </div>
                  </BlobBackground>
                </div>

                {/* Right side - Form */}
                <div className="w-full md:w-1/2 p-8 flex items-center">
                  <div className="w-full max-w-sm mx-auto">
                    <div className="text-center mb-8">
                      <h2 className="text-2xl font-bold text-foreground mb-2">
                        Complete Your Setup
                      </h2>
                      <p className="text-muted-foreground text-sm">
                        Set up your admin account to get started
                      </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-medium">
                          Full Name
                        </Label>
                        <Input
                          id="name"
                          name="name"
                          placeholder="Enter your full name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className={`h-10 ${formErrors.name ? "border-red-500" : ""}`}
                        />
                        {formErrors.name && (
                          <p className="text-red-500 text-xs">Name is required</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="password" className="text-sm font-medium">
                          Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="password"
                            name="password"
                            type={showPassword ? "text" : "password"}
                            placeholder="Create a secure password"
                            value={formData.password}
                            onChange={handleInputChange}
                            className={`h-10 pr-10 ${formErrors.password ? "border-red-500" : ""}`}
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          >
                            {showPassword ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                        {formErrors.password && (
                          <p className="text-red-500 text-xs">Password must be at least 8 characters</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword" className="text-sm font-medium">
                          Confirm Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="confirmPassword"
                            name="confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            placeholder="Confirm your password"
                            value={formData.confirmPassword}
                            onChange={handleInputChange}
                            className={`h-10 pr-10 ${formErrors.confirmPassword ? "border-red-500" : ""}`}
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          >
                            {showConfirmPassword ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                        {formErrors.confirmPassword && (
                          <p className="text-red-500 text-xs">Passwords do not match</p>
                        )}
                      </div>

                      <div className="pt-4">
                        <Button
                          type="submit"
                          className="w-full h-10 bg-gradient-to-r from-red-500 to-blue-600 hover:from-red-600 hover:to-blue-700 text-white border-0"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Setting up...
                            </>
                          ) : (
                            "Complete Setup"
                          )}
                        </Button>
                      </div>

                      <p className="text-center text-xs text-muted-foreground">
                        By completing setup, you agree to our{" "}
                        <a href="#" className="text-primary hover:underline">Terms</a> and{" "}
                        <a href="#" className="text-primary hover:underline">Privacy Policy</a>.
                      </p>
                    </form>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {verificationStep === 'success' && (
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="flex flex-col items-center justify-center text-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", stiffness: 200, damping: 15 }}
              className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4 border border-green-200 dark:border-green-800"
            >
              <CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
            </motion.div>
            <h2 className="text-2xl font-bold text-foreground mb-2">
              Setup Complete!
            </h2>
            <p className="text-muted-foreground">
              Redirecting you to the dashboard...
            </p>
          </motion.div>
        )}

        {verificationStep === 'error' && (
          <motion.div
            key="error"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="flex flex-col items-center justify-center text-center max-w-md mx-auto"
          >
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4 border border-red-200 dark:border-red-800">
              <ShieldCheck className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-2xl font-bold text-foreground mb-2">
              Verification Failed
            </h2>
            <p className="text-muted-foreground text-sm mb-6">
              {error || 'An error occurred during verification. Please try again or contact support.'}
            </p>
            <Button
              className="bg-gradient-to-r from-red-500 to-blue-600 hover:from-red-600 hover:to-blue-700 text-white border-0"
              onClick={() => window.location.href = '/'}
            >
              Return to Home
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
