import { Module } from '@nestjs/common';
import { CommonService } from './common.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { CommonController } from './common.controller';
import { EtlModule } from '@/etl/etl.module';

@Module({
  imports: [PrismaModule],
  controllers: [CommonController],
  providers: [CommonService],
  exports: [CommonService],
})
export class CommonModule {}
