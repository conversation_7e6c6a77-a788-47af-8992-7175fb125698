import { IsString, IsOptional, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateKnowledgeBaseDto {
  @ApiProperty({ description: 'Name of the knowledge base' })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the knowledge base',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
