import { Controller, Post, Body, UseGuards, Get } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { ZendeskService } from './zendesk.v1.service';
import { CrawlZendeskDto, ZendeskConnectDto } from './dtos/zendesk.dto';

@ApiTags('Zendesk-V1')
@Controller({ version: '1', path: 'zendesk' })
export class ZendeskController {
  constructor(private zendeskService: ZendeskService) {}

  @Post('connect')
  @ApiOperation({ summary: 'Connect to Zendesk' })
  @ApiResponse({
    status: 200,
    description: 'Successfully connected to Zendesk',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async connect(
    @GetCurrentUser() user: JwtPayload,
    @Body() body: ZendeskConnectDto,
  ) {
    return this.zendeskService.connect(
      user.sub,
      body.token,
      body.email,
      body.subdomain,
    );
  }

  @Post('/crawl')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Crawl Zendesk' })
  @ApiResponse({
    status: 200,
    description: 'Crawls a Zendesk account',
  })
  async crawlOneDrive(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: CrawlZendeskDto,
  ) {
    return this.zendeskService.crawlZendesk(user.sub, data);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect Zendesk Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected Zendesk Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.zendeskService.disconnect(user.sub);
  }
}
