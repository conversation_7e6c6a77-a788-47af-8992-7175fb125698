import { Metada<PERSON> } from "next"
import Image from "next/image"

import { Separator } from "@/components/ui/separator"
import { SidebarNav } from "@/components/Navigation/settings-nav"

export const metadata: Metadata = {
    title: "Organization",
}


interface OrganizationLayoutProps {
    children: React.ReactNode
}

export default function OrganizationLayout({ children }: OrganizationLayoutProps) {
    return (
        <div className="space-y-6 px-4 md:px-10">
            {children}
        </div>
    )
}