'use client'

import { createContext, useContext, ReactNode } from 'react'
import { useSession, SessionProvider } from 'next-auth/react'
import { UserRole, Organization, Team } from '@/lib/next-auth'

interface UserContextType {
    user: {
        id: string | null
        name: string | null
        email: string | null
        image?: string | null
        hasOnboarded?: boolean
        role?: UserRole | null
        organizationId?: string | null
        organization?: Organization | null
        teams?: Team[]
        currentTeam?: Team | null
    }
    isLoading: boolean
    isAdmin: boolean
    isTeamAdmin: boolean
    isUser: boolean
    hasOrganization: boolean
    hasTeams: boolean
}

const UserContext = createContext<UserContextType>({
    user: {
        id: null,
        name: null,
        email: null,
        role: null,
        organizationId: null,
        organization: null,
        teams: [],
        currentTeam: null
    },
    isLoading: true,
    isAdmin: false,
    isTeamAdmin: false,
    isUser: false,
    hasOrganization: false,
    hasTeams: false
})

export function UserProvider({ children }: { children: ReactNode }) {
    return (
        <SessionProvider>
            <UserContextWrapper>{children}</UserContextWrapper>
        </SessionProvider>
    )
}

function UserContextWrapper({ children }: { children: ReactNode }) {
    const { data: session, status } = useSession()
    console.log(session)



    const user = {
        id: session?.user?.id || null,
        name: session?.user?.name || null,
        email: session?.user?.email || null,
        image: session?.user?.image || null,
        hasOnboarded: session?.user?.hasOnboarded || false,
        role: session?.user?.role || null,
        organizationId: session?.user?.organizationId || null,
        organization: session?.user?.organization || null,
        teams: session?.user?.teams || [],
        currentTeam: session?.user?.currentTeam || null
    }

    console.log(user)

    const isAdmin = user.role === 'ADMIN' || user.role === 'SUPERADMIN'
    const isTeamAdmin = user.role === 'TEAMADMIN'
    const isUser = user.role === 'USER'
    const hasOrganization = !!user.organizationId
    const hasTeams = (user.teams?.length || 0) > 0

    const value = {
        user,
        isLoading: status === 'loading',
        isAdmin,
        isTeamAdmin,
        isUser,
        hasOrganization,
        hasTeams
    }

    return (
        <UserContext.Provider value={value}>
            {children}
        </UserContext.Provider>
    )
}

export const useUser = () => useContext(UserContext)