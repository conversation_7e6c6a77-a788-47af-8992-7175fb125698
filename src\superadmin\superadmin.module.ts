import { Module } from '@nestjs/common';
import { SuperAdminController } from './superadmin.controller';
import { SuperAdminService } from './superadmin.service';
import { DynamoDBModule } from '../dynamodb/dynamodb.module';
import { AuthModule } from '../auth/auth.module';
import { PrismaModule } from '../prisma/prisma.module';
import { SuperAdminConnectorsController } from './v1/superadmin-connectors.v1.controller';
import { SuperAdminConnectorsService } from './v1/superadmin-connectors.v1.service';
import { EmailModule } from '../email/email.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { MilvusModule } from '@/milvus/milvus.module';

@Module({
  imports: [
    DynamoDBModule,
    AuthModule,
    PrismaModule,
    EmailModule,
    JwtModule,
    ConfigModule,
    MilvusModule,
  ],
  controllers: [SuperAdminController, SuperAdminConnectorsController],
  providers: [SuperAdminService, SuperAdminConnectorsService],
})
export class SuperAdminModule {}
