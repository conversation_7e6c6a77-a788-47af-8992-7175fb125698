import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { google } from 'googleapis';
import { ConfigService } from '@nestjs/config';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { ConnectorsService } from '../../v1/connectors.v1.service';
import { BaseConnectorService } from '../../interfaces/base.interface';

interface SlackConnectionDetails {
  user: {
    id: string;
    name: string;
    email?: string;
    image?: string;
  };
  workspace: {
    id: string;
    name: string;
    domain: string;
    icon?: string;
  };
  channels: {
    total: number;
    channels: Array<{
      id: string;
      name: string;
      topic?: string;
      memberCount: number;
      isPrivate: boolean;
    }>;
  };
}

@Injectable()
export class SlackService extends BaseConnectorService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
  ) {
    super();
  }

  async generateRedirectUrl(userId: string) {
    const scopes = [
      'channels:history',
      'channels:read',
      'search:read',
      'groups:read',
      'groups:history',
      'im:history',
      'users:read',
      'users:read.email',
      'team:read',
      'users.profile:read',
    ];

    const authUrl = `https://slack.com/oauth/v2/authorize?client_id=${this.configService.get(
      'slack.clientId',
    )}&user_scope=${encodeURIComponent(
      scopes.join(','),
    )}&redirect_uri=${encodeURIComponent(
      this.configService.get('slack.redirectUri'),
    )}&state=${userId}`;

    return authUrl;
  }

  async handleCallback(code: string, state: string) {
    try {
      const formData = new FormData();
      formData.append('code', code);
      formData.append('client_id', this.configService.get('slack.clientId'));
      formData.append(
        'client_secret',
        this.configService.get('slack.clientSecret'),
      );

      const tokenResponse = await fetch(
        'https://slack.com/api/oauth.v2.access',
        {
          method: 'POST',
          body: formData,
        },
      );

      const tokenData = await tokenResponse.json();
      if (!tokenData.ok) {
        throw new Error(tokenData.error);
      }

      const { access_token } = tokenData.authed_user;
      console.log(access_token);

      const connectionDetails = await this.getConnectionDetails(access_token);

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Slack',
        },
        select: {
          id: true,
          type: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('Slack connector not found');
      }

      await this.prisma.userConnectors.create({
        data: {
          userId: state,
          connectorId: connector.id,
          config: {
            name: connectionDetails.user.name,
            email: connectionDetails.user.email,
            image: connectionDetails.user.image,
            metadata: JSON.stringify(connectionDetails),
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(access_token);

      const userConnectorDetails = {
        user_id: state,
        connector_id: connector.id,
        connector_type: connector.type,
        name: 'Slack',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        userConnectorDetails,
      );

      return {
        success: true,
        message: 'Slack integration connected successfully',
        data: {
          name: connectionDetails.user.name,
          email: connectionDetails.user.email,
          image: connectionDetails.user.image,
          metadata: JSON.stringify(connectionDetails),
        },
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to process Slack callback: ${error.message}`,
      );
    }
  }

  async disconnect(userId: string) {
    try {
      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Slack',
        },
        select: {
          id: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('Slack connector not found');
      }

      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'Slack',
          },
        },
      );

      const userConnector = userConnectors?.[0];
      if (!userConnector) {
        throw new NotFoundException(
          'No active Slack connection found for this user',
        );
      }

      const decryptedAccessToken = this.connectorsService.decrypt(
        userConnector.credentials.access_token,
        userConnector.credentials.access_token_iv,
      );

      if (decryptedAccessToken) {
        try {
          const formData = new FormData();
          formData.append('token', decryptedAccessToken);
          formData.append(
            'client_id',
            this.configService.get('slack.clientId'),
          );
          formData.append(
            'client_secret',
            this.configService.get('slack.clientSecret'),
          );

          const revokeResponse = await fetch(
            'https://slack.com/api/auth.revoke',
            {
              method: 'POST',
              body: formData,
            },
          );

          const revokeData = await revokeResponse.json();
          if (!revokeData.ok) {
            throw new Error(revokeData.error);
          }
        } catch (error) {
          console.error('Failed to revoke Slack token:', error.message);
        }
      }

      await this.prisma.userConnectors.deleteMany({
        where: {
          userId,
          connectorId: connector.id,
        },
      });

      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'Slack',
      });

      return {
        success: true,
        message: 'Successfully disconnected Slack integration',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to disconnect Slack integration: ${error.message}`,
      );
    }
  }

  private async getConnectionDetails(
    accessToken: string,
  ): Promise<SlackConnectionDetails> {
    try {
      const userResponse = await fetch('https://slack.com/api/auth.test', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      const userData = await userResponse.json();
      if (!userData.ok) throw new Error(userData.error);

      const userProfileResponse = await fetch(
        'https://slack.com/api/users.profile.get',
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );
      const userProfileData = await userProfileResponse.json();
      if (!userProfileData.ok) throw new Error(userProfileData.error);

      const teamResponse = await fetch('https://slack.com/api/team.info', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      const teamData = await teamResponse.json();
      if (!teamData.ok) throw new Error(teamData.error);

      const channelsResponse = await fetch(
        'https://slack.com/api/conversations.list?types=public_channel,private_channel',
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );
      const channelsData = await channelsResponse.json();
      if (!channelsData.ok) throw new Error(channelsData.error);

      return {
        user: {
          id: userData.user_id,
          name: userData.user,
          email: userProfileData.profile.email,
          image: userProfileData.profile.image_72,
        },
        workspace: {
          id: teamData.team.id,
          name: teamData.team.name,
          domain: teamData.team.domain,
          icon: teamData.team.icon.image_102,
        },
        channels: {
          total: channelsData.channels.length,
          channels: channelsData.channels.map((channel) => ({
            id: channel.id,
            name: channel.name,
            topic: channel.topic?.value,
            memberCount: channel.num_members,
            isPrivate: channel.is_private,
          })),
        },
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to fetch Slack connection details: ${error.message}`,
      );
    }
  }

  async getSlackAccessToken(userId: string) {
    const userConnector = await this.dynamoDBService.get(
      this.dynamoDBTableName,
      {
        user_id: userId,
        name: 'Slack',
      },
    );

    if (!userConnector) {
      throw new NotFoundException('Slack connector not found');
    }

    const decryptedAccessToken = this.connectorsService.decrypt(
      userConnector.credentials.access_token,
      userConnector.credentials.access_token_iv,
    );

    return decryptedAccessToken;
  }
}
