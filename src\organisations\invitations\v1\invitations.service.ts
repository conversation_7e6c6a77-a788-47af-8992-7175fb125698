import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/prisma/prisma.service';
import { EmailService } from 'src/email/email.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { InvitationStatus, InvitationType } from '@prisma/client';
import { GetInvitationsDto, CancelInvitationDto, ResendInvitationDto } from '../dtos/invitation.dto';

@Injectable()
export class InvitationsService {
  constructor(
    private prisma: PrismaService,
    private emailService: EmailService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async getInvitations(query: GetInvitationsDto, currentUserId: string) {
    // Get the current user to verify they have access to this organization
    const currentUser = await this.prisma.user.findUnique({
      where: { id: currentUserId },
      select: { organizationId: true, role: true },
    });

    if (!currentUser || !currentUser.organizationId) {
      throw new BadRequestException('User must be part of an organization');
    }

    // Verify user has access to the requested organization
    if (currentUser.organizationId !== query.organizationId) {
      throw new BadRequestException('You can only view invitations from your own organization');
    }

    // Clean up any pending invitations for users who have already onboarded
    await this.cleanupOnboardedUserInvitations(query.organizationId);

    // Build the where clause - organization ID is required
    const whereClause: any = {
      organizationId: query.organizationId,
    };

    // Add optional filters
    if (query.status) {
      whereClause.status = query.status;
    } else {
      // By default, exclude accepted invitations unless specifically requested
      whereClause.status = {
        not: 'ACCEPTED'
      };
    }

    if (query.type) {
      whereClause.type = query.type;
    }

    // Fetch invitations for the organization
    const rawInvitations = await this.prisma.invitation.findMany({
      where: whereClause,
      include: {
        inviter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        team: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Filter out invitations for users who have already onboarded
    // This is an additional safety check to ensure onboarded users don't appear
    const invitationEmails = rawInvitations.map(inv => inv.email);

    const onboardedUsers = await this.prisma.user.findMany({
      where: {
        email: { in: invitationEmails },
        organizationId: query.organizationId,
        onboardingCompleted: true,
      },
      select: { email: true },
    });

    const onboardedEmails = new Set(onboardedUsers.map(user => user.email));

    // Exclude invitations for users who have completed onboarding
    const invitations = rawInvitations.filter(invitation =>
      !onboardedEmails.has(invitation.email)
    );

    // Get summary statistics for the organization
    const summary = await this.prisma.invitation.groupBy({
      by: ['status'],
      where: {
        organizationId: query.organizationId,
      },
      _count: {
        status: true,
      },
    });

    const summaryStats = {
      total: invitations.length,
      pending: summary.find(s => s.status === 'PENDING')?._count.status || 0,
      accepted: summary.find(s => s.status === 'ACCEPTED')?._count.status || 0,
      expired: summary.find(s => s.status === 'EXPIRED')?._count.status || 0,
      cancelled: summary.find(s => s.status === 'CANCELLED')?._count.status || 0,
    };

    return {
      success: true,
      message: 'Invitations fetched successfully',
      invitations,
      summary: summaryStats,
    };
  }

  async cancelInvitation(data: CancelInvitationDto, currentUserId: string) {
    // Get the current user to check permissions
    const currentUser = await this.prisma.user.findUnique({
      where: { id: currentUserId },
      select: { organizationId: true, role: true },
    });

    if (!currentUser || !currentUser.organizationId) {
      throw new BadRequestException('User must be part of an organization');
    }

    // Find the invitation
    const invitation = await this.prisma.invitation.findUnique({
      where: { id: data.invitationId },
      include: {
        organization: true,
        team: true,
      },
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    // Check if user has permission to cancel this invitation
    if (invitation.organizationId !== currentUser.organizationId) {
      throw new BadRequestException('You can only cancel invitations from your organization');
    }

    // Check if invitation can be cancelled
    if (invitation.status !== 'PENDING') {
      throw new BadRequestException('Only pending invitations can be cancelled');
    }

    // Update invitation status
    const updatedInvitation = await this.prisma.invitation.update({
      where: { id: data.invitationId },
      data: { status: 'CANCELLED' },
      include: {
        inviter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        team: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    return {
      success: true,
      message: 'Invitation cancelled successfully',
      invitation: updatedInvitation,
    };
  }

  async resendInvitation(data: ResendInvitationDto, currentUserId: string) {
    // Get the current user to check permissions
    const currentUser = await this.prisma.user.findUnique({
      where: { id: currentUserId },
      select: { organizationId: true, role: true },
    });

    if (!currentUser || !currentUser.organizationId) {
      throw new BadRequestException('User must be part of an organization');
    }

    // Find the invitation
    const invitation = await this.prisma.invitation.findUnique({
      where: { id: data.invitationId },
      include: {
        organization: true,
        team: true,
      },
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    // Check if user has permission to resend this invitation
    if (invitation.organizationId !== currentUser.organizationId) {
      throw new BadRequestException('You can only resend invitations from your organization');
    }

    // Check if invitation can be resent
    if (invitation.status !== 'PENDING' && invitation.status !== 'EXPIRED') {
      throw new BadRequestException('Only pending or expired invitations can be resent');
    }

    // Generate new token and expiry
    const newToken = await this.jwtService.signAsync(
      {
        email: invitation.email,
        organizationId: invitation.organizationId,
        teamId: invitation.teamId,
        type: invitation.type === 'ORGANIZATION' ? 'org-invitation' : 'team-invitation',
        invitationId: invitation.id,
      },
      {
        secret: this.configService.get('auth.accessTokenKey.secret'),
        expiresIn: '7d',
      },
    );

    const newExpiresAt = new Date();
    newExpiresAt.setDate(newExpiresAt.getDate() + 7);

    // Update invitation with new token and expiry
    const updatedInvitation = await this.prisma.invitation.update({
      where: { id: data.invitationId },
      data: {
        token: newToken,
        expiresAt: newExpiresAt,
        status: 'PENDING',
      },
    });

    // Send email based on invitation type
    const invitationUrl = `${process.env.FRONTEND_URL}/onboarding?token=${newToken}`;

    if (invitation.type === 'ORGANIZATION') {
      await this.emailService.sendOrgInvitationEmail(
        invitation.email,
        invitation.organization.name,
        invitationUrl,
      );
    } else if (invitation.type === 'TEAM' && invitation.team) {
      await this.emailService.sendTeamInvitationEmail(
        invitation.email,
        invitation.team.name,
        invitation.organization.name,
        invitationUrl,
      );
    }

    return {
      success: true,
      message: 'Invitation resent successfully',
      invitation: updatedInvitation,
    };
  }

  async markInvitationAsAccepted(invitationId: string) {
    return this.prisma.invitation.update({
      where: { id: invitationId },
      data: {
        status: 'ACCEPTED',
        acceptedAt: new Date(),
      },
    });
  }

  async markExpiredInvitations() {
    const now = new Date();

    return this.prisma.invitation.updateMany({
      where: {
        status: 'PENDING',
        expiresAt: {
          lt: now,
        },
      },
      data: {
        status: 'EXPIRED',
      },
    });
  }

  async cleanupOnboardedUserInvitations(organizationId: string) {
    // Find all pending invitations for users who have completed onboarding
    const pendingInvitations = await this.prisma.invitation.findMany({
      where: {
        organizationId,
        status: 'PENDING',
      },
      select: {
        id: true,
        email: true,
      },
    });

    if (pendingInvitations.length === 0) {
      return { updated: 0 };
    }

    const invitationEmails = pendingInvitations.map(inv => inv.email);

    // Find users who have completed onboarding
    const onboardedUsers = await this.prisma.user.findMany({
      where: {
        email: { in: invitationEmails },
        organizationId,
        onboardingCompleted: true,
      },
      select: { email: true },
    });

    const onboardedEmails = onboardedUsers.map(user => user.email);

    if (onboardedEmails.length === 0) {
      return { updated: 0 };
    }

    // Update invitations for onboarded users to ACCEPTED
    const result = await this.prisma.invitation.updateMany({
      where: {
        organizationId,
        email: { in: onboardedEmails },
        status: 'PENDING',
      },
      data: {
        status: 'ACCEPTED',
        acceptedAt: new Date(),
      },
    });

    return { updated: result.count };
  }
}
