import { FileText, Image as ImageIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { assets } from '@/lib/assets';

interface GDriveSourceProps {
  source: {
    id: string;
    name: string;
    mime_type: string;
    size: number;
    description?: string;
    created_time?: string;
    modified_time?: string;
    web_view_link?: string;
  };
}

export function GDriveSource({ source }: GDriveSourceProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch (e) {
      return dateStr;
    }
  };

  const getFileIcon = (mimeType: string) => {
    switch (mimeType) {
      case 'application/vnd.google-apps.spreadsheet':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-green-600">
            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
          </svg>
        );
      case 'application/vnd.google-apps.document':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-blue-600">
            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
        );
      case 'application/vnd.google-apps.presentation':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-orange-600">
            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 16H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1z"/>
            <path fill="currentColor" d="M8 8h8v2H8zm0 4h8v2H8zm0 4h4v2H8z"/>
          </svg>
        );
      case 'application/pdf':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-red-600">
            <path fill="currentColor" d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2.5 8.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM13 17.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm4.5 4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2z"/>
          </svg>
        );
      default:
        if (mimeType.startsWith('image/')) {
          return <ImageIcon className="w-4 h-4 text-purple-600" />;
        }
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="p-2 border-b border-border last:border-0 hover:bg-accent/50 transition-colors cursor-pointer" onClick={() => source.web_view_link && window.open(source.web_view_link, '_blank', 'noopener noreferrer')}>
      <div className="flex items-start gap-2">
        <div className="mt-1 flex-shrink-0">
          <div className="w-4 h-4 rounded-full overflow-hidden bg-muted flex items-center justify-center">
            {getFileIcon(source.mime_type)}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-foreground truncate">
            {source.name}
          </h4>
          <div className="flex items-center gap-2 mt-0.5">
            <p className="text-xs text-muted-foreground truncate">
              {formatFileSize(source.size)}
            </p>
            {source.modified_time && (
              <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-muted/50 text-muted-foreground/70 whitespace-nowrap">
                {formatDate(source.modified_time)}
              </span>
            )}
          </div>
          {source.description && (
            <p className={cn(
              "text-xs text-muted-foreground mt-1 line-clamp-2",
              !source.name && "mt-0"
            )}>
              {source.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
} 