import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  HttpCode,
  UseGuards,
  HttpStatus,
  Query,
  Req,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiProperty,
  ApiQuery,
} from '@nestjs/swagger';
import { KnowledgebaseService } from './knowledgebase.v1.service';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { CreateKnowledgeBaseDto } from '../dtos/create-knowledgebase.dto';
import { UpdateKnowledgeBaseDto } from '../dtos/update-knowledgebase.dto';
import { ShareKnowledgeBaseDto } from '../dtos/share-knowledgebase.dto';
import { AddFilesDto } from '../dtos/add-files.dto';
import { CrawlKnowledgeBaseDto } from '../dtos/crawl-knowledgebase.dto';
import { UpdateStatusDto } from '../dtos/update-status.dto';
import { Request } from 'express';

@ApiTags('Knowledgebase-V1')
@Controller({ version: '1', path: 'knowledgebase' })
export class KnowledgebaseController {
  constructor(private knowledgebaseService: KnowledgebaseService) { }

  @Post()
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Create a new knowledge base' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Knowledge base created successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async createKnowledgebase(
    @Body() data: CreateKnowledgeBaseDto,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.knowledgebaseService.createKnowledgebase(user.sub, data);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all knowledgebases' })
  @ApiResponse({
    status: 200,
    description: 'All knowledge bases',
  })
  @HttpCode(200)
  async getAllKnowledgebases(@GetCurrentUser() user: JwtPayload) {
    return this.knowledgebaseService.getAllKnowledgebase(user.sub);
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get a knowledgebase with paginated files' })
  @ApiResponse({
    status: 200,
    description: 'Knowledge base details with paginated files',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Number of items per page (default: 10)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for filtering files' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Field to sort by (default: name)' })
  @ApiQuery({ name: 'sortOrder', required: false, type: String, description: 'Sort order (asc or desc, default: asc)' })
  @HttpCode(200)
  async getKnowledgebaseById(
    @GetCurrentUser() user: JwtPayload,
    @Param('id') kid: string,
    @Query() query: any,
    @Req() request: Request,
  ) {
    const queryParams = {
      page: query?.page || '1',
      pageSize: query?.pageSize || '10',
      search: query?.search || '',
      sortBy: query?.sortBy || 'name',
      sortOrder: query?.sortOrder || 'asc',
    };
    return this.knowledgebaseService.getKnowledgebaseById(user.sub, kid, queryParams);
  }

  @Put(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update a knowledgebase' })
  @ApiResponse({
    status: 200,
    description: 'All knowledge bases',
  })
  @HttpCode(200)
  async updateKnowledgebase(
    @GetCurrentUser() user: JwtPayload,
    @Param('id') kid: string,
    @Body() data: UpdateKnowledgeBaseDto,
  ) {
    return this.knowledgebaseService.updateKnowledgebase(user.sub, data, kid);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Delete a knowledgebase' })
  @ApiResponse({
    status: 200,
    description: 'All knowledge bases',
  })
  @HttpCode(200)
  async deleteKnowledgebase(
    @GetCurrentUser() user: JwtPayload,
    @Param('id') kid: string,
  ) {
    return this.knowledgebaseService.deleteKnowledgebase(user.sub, kid);
  }

  @Post('/share')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Share a knowledgebase' })
  @ApiResponse({
    status: 200,
    description: 'All knowledge bases',
  })
  @HttpCode(200)
  async shareKnowledgebase(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: ShareKnowledgeBaseDto,
  ) {
    return this.knowledgebaseService.shareKnowledgebase(user.sub, data);
  }

  @Post('/addfiles')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add files to a knowledge base' })
  @ApiResponse({
    status: 200,
    description: 'Files added successfully',
  })
  @HttpCode(200)
  async addFilesToKnowledgebase(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: AddFilesDto,
  ) {
    return await this.knowledgebaseService.addFilesToKnowledgeBase(
      user.sub,
      data,
    );
  }

  @Get('/crawl/status')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get crawl status' })
  @ApiResponse({
    status: 200,
    description: 'Crawl status',
  })
  @HttpCode(200)
  async getCrawlStatus(
    @GetCurrentUser() user: JwtPayload,
    @Param('id') kid: string,
  ) {
    return this.knowledgebaseService.getCrawlStatus(user.sub, kid);
  }

  @Post('status')
  @ApiOperation({ summary: 'Update status' })
  @ApiResponse({
    status: 200,
    description: 'Status',
  })
  @HttpCode(200)
  async updateStatus(@Body() data: UpdateStatusDto) {
    return this.knowledgebaseService.updateStatus(data);
  }
}
