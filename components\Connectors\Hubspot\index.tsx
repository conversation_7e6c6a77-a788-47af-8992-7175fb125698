"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ConnectorProps } from '../types';
import {
    Loader2,
    CheckCircle2,
    Building2,
    Power,
    ArrowRight,
    Shield,
    LogIn,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { queryClient, createMutationFn, useApiMutation, useApiQuery } from "@/lib/apiClient";
import { useRouter } from "next/navigation";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";

interface HubspotAccount {
    portalId: string
    portalName: string
    accountType: string
}

const LoadingSkeleton = () => (
    <div className="space-y-8 p-6 bg-card rounded-lg border shadow-sm dark:border-border animate-pulse">
        <div className="flex items-center justify-between">
            <div className="space-y-2">
                <div className="h-6 w-48 bg-muted rounded-md" />
                <div className="h-4 w-72 bg-muted rounded-md" />
            </div>
        </div>
        <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border flex justify-between items-center">
            <div className="flex items-center gap-4">
                <div className="h-6 w-6 bg-muted rounded-full" />
                <div className="h-4 w-64 bg-muted rounded-md" />
            </div>
            <div className="h-10 w-40 bg-muted rounded-md" />
        </div>
    </div>
)

export default function HubspotConnector({ data }: ConnectorProps) {
    const router = useRouter()
    const [state, setState] = useState<"disconnected" | "connecting" | "connected">(
        data?.isConnected ? "connected" : "disconnected"
    )
    const [account, setAccount] = useState<HubspotAccount | null>(
        data?.config ? {
            portalId: data.config.portalId,
            portalName: data.config.portalName,
            accountType: data.config.accountType
        } : null
    )
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [isDisconnecting, setIsDisconnecting] = useState(false)
    const isHandledRef = useRef(false)

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const state = urlParams.get("state");

        if (code && state && !isHandledRef.current) {
            isHandledRef.current = true;
            handleOAuthCallback(code, state);
        }
    }, []);

    const connectHubspot = useApiMutation(
        createMutationFn.post('/hubspot/callback'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['hubspot'] });
                    setAccount({
                        portalId: response.result.data.portalId,
                        portalName: response.result.data.portalName,
                        accountType: response.result.data.accountType
                    })
                    setState("connected")
                    setError(null)
                    window.history.replaceState({}, document.title, window.location.pathname);
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                setError(error.response?.data?.message || "Failed to connect to Hubspot")
                setState("disconnected")
            }
        }
    );

    const handleOAuthCallback = async (code: string, state: string) => {
        setState("connecting")
        try {
            await connectHubspot.mutateAsync({ code, state })
        } finally {
            window.history.replaceState({}, document.title, window.location.pathname)
        }
    }

    const getAuthUrl = useApiMutation(
        createMutationFn.post('/hubspot/auth-url'),
        {
            onSuccess: (response: {
                status: boolean;
                statusCode: number;
                result: string;
                version: string;
            }) => {
                console.log('Received response:', response); // Debug log
                
                if (response?.status && typeof response.result === 'string') {
                    queryClient.invalidateQueries({ queryKey: ['hubspot-auth-url'] });
                    try {
                        // Add protocol if missing in redirect_uri
                        let authUrl = response.result;
                        if (authUrl.includes('redirect_uri=localhost')) {
                            authUrl = authUrl.replace(
                                'redirect_uri=localhost',
                                'redirect_uri=http://localhost'
                            );
                        }

                        console.log('Attempting to navigate to:', authUrl);
                        
                        const url = new URL(authUrl);
                        window.location.href = url.toString(); 
                        setError(null);
                    } catch (e: any) {
                        console.error('URL parsing error:', e);
                        setError(`Invalid authorization URL: ${e.message}`);
                    }
                } else {
                    console.error('Invalid response structure:', response);
                    setError("Invalid response structure from server");
                }
            },
            onError: (error: any) => {
                console.error('Auth URL error:', {
                    message: error.message,
                    response: error.response?.data,
                    status: error.response?.status
                });
                setError(error.response?.data?.message || "Failed to get auth URL");
            }
        }
    );

    const handleConnect = async () => {
        setIsLoading(true);
        try {
            console.log('Initiating Hubspot connection...'); // Debug log
            await getAuthUrl.mutateAsync({});
        } catch (error) {
            console.error('Connection error:', error);
            setError('Failed to initiate connection');
        } finally {
            setIsLoading(false);
        }
    };

    const disconnectHubspot = useApiMutation(
        createMutationFn.post('/hubspot/disconnect'),
        {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['hubspot'] });
                setState("disconnected")
                setAccount(null)
                setError(null)
                setIsDisconnecting(false)
            },
            onError: (error: any) => {
                setError(error.response?.data?.message || "Failed to disconnect from Hubspot")
                setIsDisconnecting(false)
            }
        }
    );

    const handleDisconnect = async () => {
        setIsDisconnecting(true)
        await disconnectHubspot.mutateAsync({})
    }

    return (
        <Card className="w-full">
            <CardHeader className="space-y-1">
                <div className="flex items-center space-x-4">
                    <Image
                        src="/hubspot-logo.png" // Make sure to add this image to your public folder
                        alt="Hubspot"
                        width={40}
                        height={40}
                        className="rounded-lg"
                    />
                    <div>
                        <CardTitle className="text-2xl">Hubspot Connection</CardTitle>
                        <CardDescription>
                            Connect your Hubspot account to manage your CRM data
                        </CardDescription>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="space-y-4">
                {state === "connected" && account ? (
                    <div className="bg-secondary/30 p-6 rounded-lg space-y-6">
                        <div className="flex items-center gap-4">
                            <div className="h-12 w-12 rounded-lg bg-background flex items-center justify-center">
                                <Building2 className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg">{account.portalName}</h3>
                                <p className="text-sm text-muted-foreground">
                                    Portal ID: {account.portalId} • {account.accountType}
                                </p>
                            </div>
                        </div>

                        <div className="flex items-center gap-4">
                            <Button
                                variant="destructive"
                                onClick={handleDisconnect}
                                disabled={isDisconnecting}
                                className="relative"
                            >
                                {isDisconnecting ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        <span>Disconnecting...</span>
                                    </>
                                ) : (
                                    <>
                                        <Power className="mr-2 h-4 w-4" />
                                        <span>Disconnect</span>
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                ) : (
                    <div className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="space-y-4 p-6 bg-secondary/30 rounded-lg">
                                <Building2 className="h-8 w-8 text-primary" />
                                <h3 className="font-semibold text-lg">CRM Integration</h3>
                                <p className="text-sm text-muted-foreground">
                                    Access and manage your contacts, companies, and deals
                                </p>
                            </div>
                            <div className="space-y-4 p-6 bg-secondary/30 rounded-lg">
                                <Shield className="h-8 w-8 text-primary" />
                                <h3 className="font-semibold text-lg">Secure Access</h3>
                                <p className="text-sm text-muted-foreground">
                                    Your data is securely accessed through Hubspot's official API
                                </p>
                            </div>
                        </div>

                        <div className="flex justify-center pt-4">
                            <Button
                                size="lg"
                                onClick={handleConnect}
                                disabled={isLoading}
                                className="relative min-w-[200px]"
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                        <span>Connecting...</span>
                                    </>
                                ) : (
                                    <>
                                        <LogIn className="mr-2 h-5 w-5" />
                                        <span>Connect Hubspot</span>
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                )}

                {error && (
                    <div className="bg-destructive/10 text-destructive p-4 rounded-lg mt-4">
                        {error}
                    </div>
                )}
            </CardContent>
        </Card>
    )
} 