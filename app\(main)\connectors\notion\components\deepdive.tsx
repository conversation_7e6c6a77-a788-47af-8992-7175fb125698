"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@steps-ai/ui'
import { Button } from '@steps-ai/ui'
import { useApiMutation, createMutationFn, queryClient } from '@/lib/apiClient';
import React, { useState } from 'react'

const DeepDive = ({ etls, userConnectorId }: { etls: any, userConnectorId: string }) => {
    const [loading, setLoading] = useState(false);
    const enableDeepDive = useApiMutation(createMutationFn.post("/notion/crawl"), {
        onSuccess: (response: any) => {
            if (response?.status && response?.result) {
                queryClient.invalidateQueries({ queryKey: ["notion-crawl"] });
                queryClient.invalidateQueries({ queryKey: ["notion"] });
                try {
                    const url = new URL(response.result);
                } catch (e) {
                    console.error("Invalid auth URL:", e);
                    // setError("Received invalid authorization URL");
                }
            } else {
                console.error("Unexpected response format:", response);
                // setError("Unexpected response from server");
            }
            setLoading(false);
        },
        onError: (error: any) => {
            console.error("Auth URL error:", error);
            // setError(error.response?.data?.message || "Failed to get auth URL");
            setLoading(false);
        },
    });

    console.log(etls)

    // Find the most recent ETL job (assuming etls is sorted by createdAt descending, otherwise sort it)
    const hasEtls = Array.isArray(etls) && etls.length > 0;
    const latestEtl = hasEtls ? etls[0] : null;

    // Helper to format date
    const formatDate = (dateString: string | null) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleString();
    };

    const handleEnableDeepDive = () => {
        setLoading(true);
        enableDeepDive.mutate({
            userConnectorId: userConnectorId,
        });
    }
    return (
        <div>
            <Card className="bg-background/5 w-full">
                <CardHeader>
                    <CardTitle>Deep Dive</CardTitle>
                    <CardDescription>Enable deep dive to get more insights about your workspace.</CardDescription>
                </CardHeader>
                <CardContent>
                    {hasEtls ? (
                        <div className="space-y-2">
                            <div className="font-semibold">Latest Deep Dive Job</div>
                            <div>Status: <span className="font-mono">{latestEtl.status}</span></div>
                            <div>Started: <span className="font-mono">{formatDate(latestEtl.startDate)}</span></div>
                            <div>Ended: <span className="font-mono">{formatDate(latestEtl.endDate)}</span></div>
                            <div className="text-xs text-muted-foreground">Job ID: {latestEtl.id}</div>
                        </div>
                    ) : (
                        <Button className='text-white' onClick={handleEnableDeepDive} disabled={loading}>{loading ? "Starting..." : "Enable Deep Dive"}</Button>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}

export default DeepDive
