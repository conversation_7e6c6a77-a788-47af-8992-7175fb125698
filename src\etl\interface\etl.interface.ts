import { AirflowPublishDto } from '../dtos/airflow.dto';

export enum DAG_ID {
  KNOWLEDGE_BASE = 'knowledge_base',
  CONNECTOR = 'connector',
}

export interface KnowledgeBasePublishArgs {
  s3_uris: string[];
}

export interface EtlProviderStrategy {
  publish(
    user_id: string,
    connector_name: string,
    kid: string,
    organization_id?: string,
    connector_id?: string,
    knowledge_base_data?: KnowledgeBasePublishArgs,
  ): Promise<any>;
  getStatus(dagRunId: string): Promise<any>;
}
