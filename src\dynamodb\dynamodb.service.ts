import {
  CreateTableCommand,
  Dynamo<PERSON><PERSON>lient,
  ScalarAttributeType,
  KeyType,
  ProjectionType,
  BillingMode,
} from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  GetCommand,
  PutCommand,
  DeleteCommand,
  Query<PERSON>ommand,
  UpdateCommand,
  Scan<PERSON>ommand,
} from '@aws-sdk/lib-dynamodb';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DynamoDBService implements OnModuleInit {
  private docClient: DynamoDBDocumentClient;
  private client: DynamoDBClient;

  constructor(private configService: ConfigService) {

    this.client = new DynamoDBClient({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_KEY_SECRET,
      },
    });

    this.docClient = DynamoDBDocumentClient.from(this.client);
  }

  async onModuleInit() {
    await this.createUserConnectorsTable();
    await this.createSyncHistoryTable();
  }

  private async createUserConnectorsTable() {
    const params = {
      TableName: 'UserConnectors',
      KeySchema: [
        { AttributeName: 'user_id', KeyType: KeyType.HASH },
        { AttributeName: 'name', KeyType: KeyType.RANGE },
      ],
      AttributeDefinitions: [
        { AttributeName: 'user_id', AttributeType: ScalarAttributeType.S },
        { AttributeName: 'name', AttributeType: ScalarAttributeType.S },
        {
          AttributeName: 'connector_type',
          AttributeType: ScalarAttributeType.S,
        },
      ],
      GlobalSecondaryIndexes: [
        {
          IndexName: 'ConnectorTypeIndex',
          KeySchema: [
            { AttributeName: 'connector_type', KeyType: KeyType.HASH },
            { AttributeName: 'user_id', KeyType: KeyType.RANGE },
          ],
          Projection: {
            ProjectionType: ProjectionType.ALL,
          },
        },
      ],
      BillingMode: BillingMode.PAY_PER_REQUEST,
    };

    try {
      await this.client.send(new CreateTableCommand(params));
      console.log('UserConnections table created successfully');
    } catch (error) {
      if (error.name === 'ResourceInUseException') {
        console.log('UserConnections table already exists');
      } else {
        throw error;
      }
    }
  }

  private async createSyncHistoryTable() {
    const params = {
      TableName: 'UserSyncHistory',
      KeySchema: [
        { AttributeName: 'user_connector_id', KeyType: KeyType.HASH },
        { AttributeName: 'sync_id', KeyType: KeyType.RANGE },
      ],
      AttributeDefinitions: [
        {
          AttributeName: 'user_connector_id',
          AttributeType: ScalarAttributeType.S,
        },
        { AttributeName: 'sync_id', AttributeType: ScalarAttributeType.S },
        { AttributeName: 'user_id', AttributeType: ScalarAttributeType.S },
        {
          AttributeName: 'connector_name',
          AttributeType: ScalarAttributeType.S,
        },
      ],
      GlobalSecondaryIndexes: [
        {
          IndexName: 'UserConnectorNameIndex',
          KeySchema: [
            { AttributeName: 'user_id', KeyType: KeyType.HASH },
            { AttributeName: 'connector_name', KeyType: KeyType.RANGE },
          ],
          Projection: {
            ProjectionType: ProjectionType.ALL,
          },
        },
      ],
      BillingMode: BillingMode.PAY_PER_REQUEST,
    };

    try {
      await this.client.send(new CreateTableCommand(params));
      console.log('UserSyncHistory table created successfully');
    } catch (error) {
      if (error.name === 'ResourceInUseException') {
        console.log('UserSyncHistory table already exists');
      } else {
        throw error;
      }
    }
  }
  async get(tableName: string, key: Record<string, any>) {
    try {
      const command = new GetCommand({
        TableName: tableName,
        Key: key,
      });
      const response = await this.docClient.send(command);
      return response.Item;
    } catch (error) {
      console.error('DynamoDB GET Error:', error);
      throw error;
    }
  }

  async put(tableName: string, item: Record<string, any>) {
    try {
      const command = new PutCommand({
        TableName: tableName,
        Item: item,
      });
      return await this.docClient.send(command);
    } catch (error) {
      console.error('DynamoDB PUT Error:', error);
      throw error;
    }
  }

  async delete(tableName: string, key: Record<string, any>) {
    try {
      const command = new DeleteCommand({
        TableName: tableName,
        Key: key,
      });
      return await this.docClient.send(command);
    } catch (error) {
      console.error('DynamoDB DELETE Error:', error);
      throw error;
    }
  }

  async query(
    tableName: string,
    params: {
      keyConditionExpression: string;
      expressionAttributeValues: Record<string, any>;
      expressionAttributeNames?: Record<string, string>;
      indexName?: string;
    },
  ) {
    try {
      const command = new QueryCommand({
        TableName: tableName,
        KeyConditionExpression: params.keyConditionExpression,
        ExpressionAttributeValues: params.expressionAttributeValues,
        ExpressionAttributeNames: params.expressionAttributeNames,
        IndexName: params.indexName,
      });
      const response = await this.docClient.send(command);
      return response.Items;
    } catch (error) {
      console.error('DynamoDB QUERY Error:', error);
      throw error;
    }
  }

  async update(
    tableName: string,
    params: {
      key: Record<string, any>;
      updateExpression: string;
      expressionAttributeValues: Record<string, any>;
      expressionAttributeNames?: Record<string, string>;
      conditionExpression?: string;
    },
  ) {
    try {
      const command = new UpdateCommand({
        TableName: tableName,
        Key: params.key,
        UpdateExpression: params.updateExpression,
        ExpressionAttributeValues: params.expressionAttributeValues,
        ExpressionAttributeNames: params.expressionAttributeNames,
        ConditionExpression: params.conditionExpression,
      });
      return await this.docClient.send(command);
    } catch (error) {
      console.error('DynamoDB UPDATE Error:', error);
      throw error;
    }
  }

  async scan(
    tableName: string,
    params?: {
      filterExpression?: string;
      expressionAttributeValues?: Record<string, any>;
      expressionAttributeNames?: Record<string, string>;
      limit?: number;
    },
  ) {
    try {
      const command = new ScanCommand({
        TableName: tableName,
        FilterExpression: params?.filterExpression,
        ExpressionAttributeValues: params?.expressionAttributeValues,
        ExpressionAttributeNames: params?.expressionAttributeNames,
        Limit: params?.limit,
      });
      const response = await this.docClient.send(command);
      return response.Items;
    } catch (error) {
      console.error('DynamoDB SCAN Error:', error);
      throw error;
    }
  }
}
