import React from 'react';
import { Database, ExternalLink, FileText, BookOpen } from 'lucide-react';
import { Button } from '@steps-ai/ui';

interface KnowledgeBaseSourceProps {
  source: {
    type: string;
    text: string;
    content: string;
    title: string;
    file_name: string;
    page_number: string;
    connector_id: string;
    connector_type: string;
    link: string;
    name: string;
    score: number;
    snippet: string;
    metadata: any;
  };
}

export function KnowledgeBaseSource({ source }: KnowledgeBaseSourceProps) {
  const handleClick = () => {
    if (source.link) {
      window.open(source.link, '_blank', 'noopener noreferrer');
    }
  };

  const getFileIcon = () => {
    const fileName = source.file_name || source.name || '';
    const extension = fileName.toLowerCase().split('.').pop();
    
    switch (extension) {
      case 'pdf':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-red-600">
            <path fill="currentColor" d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2.5 8.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM13 17.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm4.5 4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2z"/>
          </svg>
        );
      case 'doc':
      case 'docx':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-blue-600">
            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
        );
      case 'ppt':
      case 'pptx':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-orange-600">
            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14H7v-2h5v2zm3-4H7v-2h8v2zm0-4H7V7h8v2z"/>
          </svg>
        );
      default:
        return <FileText className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatScore = (score: number) => {
    return (score * 100).toFixed(1) + '%';
  };

  const getPreview = (text: string) => {
    if (!text) return 'No content available';
    return text.length > 150 ? text.substring(0, 150) + '...' : text;
  };

  return (
    <div 
      className="p-2 border-b border-border last:border-0 hover:bg-accent/50 transition-colors cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-start gap-2">
        <div className="mt-1 flex-shrink-0">
          <div className="w-4 h-4 rounded-full overflow-hidden bg-muted flex items-center justify-center">
            {getFileIcon()}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-foreground truncate">
            {source.title || source.file_name || 'Knowledge Base Document'}
          </h4>
          <div className="flex items-center gap-2 mt-0.5">
            {source.page_number && (
              <span className="text-xs text-muted-foreground">
                Page {source.page_number}
              </span>
            )}
            <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 whitespace-nowrap">
              {formatScore(source.score)} match
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-1 line-clamp-3">
            {getPreview(source.text || source.content)}
          </p>
          {source.link && (
            <div className="flex items-center gap-1 mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClick();
                }}
                className="flex items-center gap-1 text-xs h-6 px-2"
              >
                <BookOpen className="w-3 h-3" />
                View Document
                <ExternalLink className="w-2.5 h-2.5" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
