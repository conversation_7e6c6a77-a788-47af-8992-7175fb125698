import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GoogleDto {
  @ApiProperty({
    example: 'name',
    description: '<PERSON>',
  })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @ApiProperty({
    example: 'email',
    description: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Name is required' })
  email: string;

  @ApiProperty({
    example: 'profileImageUrl',
    description: 'profile image url',
  })
  @IsString({ message: 'Profile image must be a string' })
  @IsNotEmpty({ message: 'Profile image is required' })
  profileImageUrl: string;
}
