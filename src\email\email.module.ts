import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailService } from './email.service';
import { ResendEmailProvider } from './providers/resend.provider';

export enum EmailProviderType {
  RESEND = 'resend',
  AWS_SES = 'aws_ses',
}

export interface EmailModuleOptions {
  type: EmailProviderType;
  apiKey?: string;
  region?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
}

@Module({})
export class EmailModule {
  static register(options: EmailModuleOptions): DynamicModule {
    return {
      module: EmailModule,
      global: true,
      imports: [ConfigModule],
      providers: [
        {
          provide: 'EMAIL_PROVIDER',

          useFactory: (configService: ConfigService) => {
            switch (options.type) {
              case EmailProviderType.RESEND:
                return new ResendEmailProvider(
                  options.apiKey || configService.get('resend.apiKey'),
                );
              // case EmailProviderType.AWS_SES:
              //   return new AwsSesEmailProvider(
              //     options.region || configService.get('AWS_REGION'),
              //     options.accessKeyId || configService.get('AWS_ACCESS_KEY_ID'),
              //     options.secretAccessKey || configService.get('AWS_SECRET_ACCESS_KEY')
              //   );
              default:
                throw new Error('Invalid email provider');
            }
          },
          inject: [ConfigService],
        },
        EmailService,
      ],
      exports: [EmailService],
    };
  }
}
