import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DynamoDBModule } from '../dynamodb/dynamodb.module';
import { SlackService } from './slack/slack.v1.service';
import { Slack<PERSON>ontroller } from './slack/slack.v1.controller';
import { IntegrationsController } from './v1/integrations.v1.controllers';
import { IntegrationsService } from './v1/integrations.v1.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
    imports: [ConfigModule, DynamoDBModule, PrismaModule],
    controllers: [IntegrationsController, SlackController],
    providers: [IntegrationsService, SlackService]
})
export class IntegrationsModule { }
