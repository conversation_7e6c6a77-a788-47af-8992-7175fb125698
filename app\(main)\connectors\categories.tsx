"use client"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Database, FileText, Globe, Server, Table, Cloud, Mail, Users } from 'lucide-react'

const categories = [
    {
        name: "All Sources",
        icon: Database,
    },
    {
        name: "Databases",
        icon: Table,
    },
    {
        name: "Cloud Storage",
        icon: Cloud,
    },
    {
        name: "Productivity",
        icon: FileText,
    },
    {
        name: "Communication",
        icon: Mail,
    },
    {
        name: "CRM & Support",
        icon: Users,
    },
]

interface CategoriesProps {
    activeCategory: string
    setActiveCategory: (category: string) => void
}

export function Categories({ activeCategory, setActiveCategory }: CategoriesProps) {
    return (
        <ScrollArea className="pb-6">
            <div className="space-y-4">
                <div className="pb-2">
                    <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">Categories</h2>
                    <div className="space-y-1">
                        {categories.map((category) => (
                            <Button
                                key={category.name}
                                variant={activeCategory === category.name ? "secondary" : "ghost"}
                                className="w-full justify-start"
                                onClick={() => setActiveCategory(category.name)}
                            >
                                <category.icon className="mr-2 h-4 w-4" />
                                {category.name}
                            </Button>
                        ))}
                    </div>
                </div>
            </div>
        </ScrollArea>
    )
}

