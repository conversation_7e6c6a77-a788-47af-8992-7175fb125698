import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, IsOptional } from 'class-validator';

export class CreateSupportTicketDto {
    @ApiProperty({
        description: 'Email address of the user',
        example: '<EMAIL>'
    })
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @ApiProperty({
        description: 'Support ticket message',
        example: 'I need help with my account settings.'
    })
    @IsString()
    @IsNotEmpty()
    message: string;

    @ApiProperty({
        description: 'Area of support needed',
        example: 'technical',
        enum: ['technical', 'billing', 'general', 'account']
    })
    @IsString()
    @IsNotEmpty()
    area: string;
}

export class SupportTicketResponseDto {
    @ApiProperty({
        description: 'Unique identifier of the support ticket',
        example: '123e4567-e89b-12d3-a456-************'
    })
    id: string;

    @ApiProperty()
    email: string;

    @ApiProperty()
    message: string;

    @ApiProperty()
    area: string;

    @ApiProperty({
        enum: ['new', 'in_progress', 'resolved', 'closed']
    })
    status: string;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    updatedAt: Date;
}
