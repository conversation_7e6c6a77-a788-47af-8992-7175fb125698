declare interface Window {
    gapi: typeof gapi;
    google: typeof google;
}

declare namespace google {
    namespace accounts {
        namespace oauth2 {
            interface TokenClient {
                requestAccessToken(): void;
            }

            interface TokenResponse {
                access_token: string;
                error?: string;
            }

            function initTokenClient(config: {
                client_id: string;
                scope: string;
                callback: (response: TokenResponse) => void;
            }): TokenClient;
        }
    }

    namespace picker {
        class PickerBuilder {
            addView(view: ViewId): PickerBuilder;
            setOAuthToken(token: string): PickerBuilder;
            setDeveloperKey(key: string): PickerBuilder;
            setCallback(callback: (data: PickerResponse) => void): PickerBuilder;
            build(): Picker;
        }

        class Picker {
            setVisible(visible: boolean): void;
        }

        enum ViewId {
            DOCS = 'docs',
            FOLDERS = 'folders',
            DOCS_IMAGES = 'docs-images',
            DOCS_VIDEOS = 'docs-videos',
            DOCS_UPLOAD = 'docs-upload'
        }
    }
}

declare namespace gapi {
    function load(api: string, callback: () => void): void;
}