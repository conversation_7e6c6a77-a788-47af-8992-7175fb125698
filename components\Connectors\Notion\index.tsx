"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ConnectorProps } from '../types';
import {
    Loader2,
    CheckCircle2,
    FileText,
    Power,
    ArrowRight,
    Shield,
    LogIn,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { queryClient, createMutationFn, useApiMutation, useApiQuery } from "@/lib/apiClient";
import { useRouter } from "next/navigation";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";

interface NotionUser {
    workspaceName: string
    workspaceIcon: string
}

export default function NotionConnector({ data }: ConnectorProps) {
    const router = useRouter()
    const [state, setState] = useState<"disconnected" | "connecting" | "connected">(
        data?.isConnected ? "connected" : "disconnected"
    )
    const [workspace, setWorkspace] = useState<NotionUser | null>(
        data?.config ? {
            workspaceName: data.config.workspaceName,
            workspaceIcon: data.config.workspaceIcon
        } : null
    )
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [isDisconnecting, setIsDisconnecting] = useState(false)
    const isHandledRef = useRef(false)

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const state = urlParams.get("state");

        if (code && state && !isHandledRef.current) {
            isHandledRef.current = true;
            handleOAuthCallback(code, state);
        }
    }, []);

    const connectNotion = useApiMutation(
        createMutationFn.post('/notion/callback'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['notion'] });
                    setWorkspace({
                        workspaceName: response.result.data.workspaceName,
                        workspaceIcon: response.result.data.workspaceIcon
                    })
                    setState("connected")
                    setError(null)
                    window.history.replaceState({}, document.title, window.location.pathname);
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                setError(error.response?.data?.message || "Failed to connect to Notion")
                setState("disconnected")
            }
        }
    );

    const handleOAuthCallback = async (code: string, state: string) => {
        setState("connecting")
        try {
            await connectNotion.mutateAsync({ code, state })
        } finally {
            window.history.replaceState({}, document.title, window.location.pathname)
        }
    }

    const getAuthUrl = useApiMutation(
        createMutationFn.post('/notion/auth-url'),
        {
            onSuccess: (response: any) => {
                if (response?.status && response?.result) {
                    queryClient.invalidateQueries({ queryKey: ['notion-auth-url'] });
                    try {
                        const url = new URL(response.result);
                        router.push(url.toString());
                        setError(null);
                    } catch (e) {
                        console.error('Invalid auth URL:', e);
                        setError("Received invalid authorization URL");
                    }
                } else {
                    console.error('Unexpected response format:', response);
                    setError("Unexpected response from server");
                }
            },
            onError: (error: any) => {
                console.error('Auth URL error:', error);
                setError(error.response?.data?.message || "Failed to get auth URL");
            }
        }
    );

    const handleConnect = async () => {
        setIsLoading(true);
        try {
            await getAuthUrl.mutateAsync({});
        } catch (error) {
            console.error('Connection error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const disconnectNotion = useApiMutation(
        createMutationFn.post('/notion/disconnect'),
        {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['notion'] });
                setState("disconnected")
                setWorkspace(null)
                setError(null)
                setIsDisconnecting(false)
            },
            onError: (error: any) => {
                setError(error.response?.data?.message || "Failed to disconnect from Notion")
                setIsDisconnecting(false)
            }
        }
    );

    const handleDisconnect = async () => {
        setIsDisconnecting(true)
        await disconnectNotion.mutateAsync({})
    }

    return (
        <Card className="w-full">
            <CardHeader className="space-y-1">
                <div className="flex items-center space-x-4">
                    {/* <Image
                        src={data?.logo || '/notion-logo.png'}
                        alt="Notion"
                        width={40}
                        height={40}
                        className="rounded-lg"
                    /> */}
                    <div>
                        <CardTitle className="text-2xl">Notion Connection</CardTitle>
                        <CardDescription>
                            Connect your Notion workspace to access and manage your documents
                        </CardDescription>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="space-y-4">
                {state === "connected" && workspace ? (
                    <div className="bg-secondary/30 p-6 rounded-lg space-y-6">
                        <div className="flex items-center gap-4">
                            {workspace.workspaceIcon && (
                                <div className="h-12 w-12 rounded-lg bg-background flex items-center justify-center">
                                    <Image
                                        src={workspace.workspaceIcon}
                                        alt="Workspace"
                                        width={32}
                                        height={32}
                                        className="rounded"
                                    />
                                </div>
                            )}
                            <div>
                                <h3 className="font-semibold text-lg">{workspace.workspaceName}</h3>
                                <p className="text-sm text-muted-foreground">Connected Workspace</p>
                            </div>
                        </div>

                        <div className="flex items-center gap-4">
                            <Button
                                variant="destructive"
                                onClick={handleDisconnect}
                                disabled={isDisconnecting}
                                className="relative"
                            >
                                {isDisconnecting ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        <span>Disconnecting...</span>
                                    </>
                                ) : (
                                    <>
                                        <Power className="mr-2 h-4 w-4" />
                                        <span>Disconnect</span>
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                ) : (
                    <div className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="space-y-4 p-6 bg-secondary/30 rounded-lg">
                                <FileText className="h-8 w-8 text-primary" />
                                <h3 className="font-semibold text-lg">Access Documents</h3>
                                <p className="text-sm text-muted-foreground">
                                    Connect to access and manage your Notion documents seamlessly
                                </p>
                            </div>
                            <div className="space-y-4 p-6 bg-secondary/30 rounded-lg">
                                <Shield className="h-8 w-8 text-primary" />
                                <h3 className="font-semibold text-lg">Secure Access</h3>
                                <p className="text-sm text-muted-foreground">
                                    Your data is securely accessed through Notion's official API
                                </p>
                            </div>
                        </div>

                        <div className="flex justify-center pt-4">
                            <Button
                                size="lg"
                                onClick={handleConnect}
                                disabled={isLoading}
                                className="relative min-w-[200px]"
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                        <span>Connecting...</span>
                                    </>
                                ) : (
                                    <>
                                        <LogIn className="mr-2 h-5 w-5" />
                                        <span>Connect Notion</span>
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                )}

                {error && (
                    <div className="bg-destructive/10 text-destructive p-4 rounded-lg mt-4">
                        {error}
                    </div>
                )}
            </CardContent>
        </Card>
    );
} 