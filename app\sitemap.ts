
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://stepsai.co'
const SERVICES_URL = 'https://services.stepsai.co'

type BaseSitemapEntry = {
  url: string;
  lastModified?: string | Date;
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

interface SitemapEntry extends BaseSitemapEntry {
  description?: string;
}

export default async function sitemap(): Promise<SitemapEntry[]> {
  const staticRoutes: SitemapEntry[] = [
    {
      url: BASE_URL,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
      description: 'AI-powered enterprise solutions and services platform'
    },
    {
      url: `${BASE_URL}/pricing`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
      description: 'Flexible pricing plans for AI integration and services'
    },
    {
      url: `${BASE_URL}/blog`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
      description: 'Latest insights on AI technology and implementation'
    },
    {
      url: `${BASE_URL}/documentation`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
      description: 'Technical documentation and implementation guides'
    }
  ]

  const servicesRoutes: SitemapEntry[] = [
    {
      url: SERVICES_URL,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
      description: 'Enterprise AI services and solutions hub'
    },
    {
      url: `${SERVICES_URL}/products`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.9,
      description: 'AI products and integration solutions'
    },
    {
      url: `${SERVICES_URL}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
      description: 'Learn about our mission and team'
    },
    {
      url: `${SERVICES_URL}/resources`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
      description: 'AI resources, guides, and best practices'
    },
    {
      url: `${SERVICES_URL}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
      description: 'Get in touch with our AI experts'
    }
  ]

  return [
    ...staticRoutes,
    ...servicesRoutes,
  ]
} 