import { Modu<PERSON> } from '@nestjs/common';
import { KnowledgebaseService } from './v1/knowledgebase.v1.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { CommonModule } from 'src/common/common.module';
import { KnowledgebaseController } from './v1/knowledgebase.v1.controller';
import { EmailModule } from 'src/email/email.module';
import { EtlModule } from '@/etl/etl.module';

@Module({
  imports: [PrismaModule, CommonModule, EmailModule, EtlModule],
  providers: [KnowledgebaseService],
  controllers: [KnowledgebaseController],
})
export class KnowledgebaseModule {}
