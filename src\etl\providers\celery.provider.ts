import { CeleryPublishDto } from '../dtos/celery.dto';
import {
    EtlProviderStrategy,
    KnowledgeBasePublishArgs,
} from '../interface/etl.interface';
import { ConnectorName } from '@/etl/etl.service';

export class CeleryEtlProvider implements EtlProviderStrategy {
    private readonly baseUrl: string;
    private readonly headers: HeadersInit;

    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Content-Type': 'application/json',
            // Authorization: `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`,
        };
    }

    async publish(
        user_id: string,
        connector_name: ConnectorName,
        kid: string,
        connector_id: string,
        organization_id: string,
        knowledge_base_data?: KnowledgeBasePublishArgs,
    ): Promise<CeleryPublishDto> {
        try {
            const reqBody = {
                user_id: user_id,
                connector_name: connector_name,
                connector_id: connector_id,
                org_id: "stepsai",
                files: knowledge_base_data?.s3_uris || [],
            }
            console.log(reqBody)
            const response = await fetch(
                `${this.baseUrl}api/etl`,
                {
                    method: 'POST',
                    headers: {
                        ...this.headers,
                    },
                    body: JSON.stringify(reqBody)
                },
            );

            const data = await response.json();
            const celeryResponse: CeleryPublishDto = {
                taskId: data.task_id,
            };
            return celeryResponse;
        } catch (error) {
            console.error('Failed to publish etl:', error);
            throw error;
        }
    }

    async getStatus(dagRunId: string) {
        return true;
    }
}
