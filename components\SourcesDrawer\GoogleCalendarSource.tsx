import React from 'react';
import { Calendar, Clock, MapPin, Users, Video, ExternalLink } from 'lucide-react';
import { Button } from '@steps-ai/ui';
import { assets } from '@/lib/assets';
import Image from 'next/image';

interface GoogleCalendarSourceProps {
  source: {
    id: string;
    summary: string;
    title: string;
    organizer_email: string;
    start_time: string;
    end_time: string;
    location: string;
    description: string;
    attendees: string[];
    html_link: string;
    meet_link: string;
    content: string;
  };
}

export function GoogleCalendarSource({ source }: GoogleCalendarSourceProps) {
  const formatDateTime = (dateTimeStr: string) => {
    try {
      const date = new Date(dateTimeStr);
      return date.toLocaleString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch (e) {
      return dateTimeStr;
    }
  };

  const formatTime = (dateTimeStr: string) => {
    try {
      const date = new Date(dateTimeStr);
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch (e) {
      return dateTimeStr;
    }
  };

  const getDuration = () => {
    try {
      const start = new Date(source.start_time);
      const end = new Date(source.end_time);
      const diffMs = end.getTime() - start.getTime();
      const diffMins = Math.round(diffMs / (1000 * 60));
      
      if (diffMins < 60) {
        return `${diffMins} min`;
      } else {
        const hours = Math.floor(diffMins / 60);
        const mins = diffMins % 60;
        return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
      }
    } catch (e) {
      return '';
    }
  };

  const handleCalendarClick = () => {
    if (source.html_link) {
      window.open(source.html_link, '_blank');
    }
  };

  const handleMeetClick = () => {
    if (source.meet_link) {
      window.open(source.meet_link, '_blank');
    }
  };

  return (
    <div className="p-2 border-b border-border last:border-0 hover:bg-accent/50 transition-colors">
      <div className="flex items-start gap-2">
        <div className="mt-1 flex-shrink-0">
          <div className="w-4 h-4 rounded-full overflow-hidden bg-muted flex items-center justify-center">
            <Image
              src={assets.Icons["Google Calendar"]}
              alt="Google Calendar"
              width={16}
              height={16}
              className="w-full h-full object-contain"
            />
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-foreground truncate">
            {source.summary}
          </h4>
          <div className="flex items-center gap-2 mt-0.5">
            <p className="text-xs text-muted-foreground truncate">
              {formatDateTime(source.start_time)} - {formatTime(source.end_time)}
            </p>
            <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-muted/50 text-muted-foreground/70 whitespace-nowrap">
              {getDuration()}
            </span>
          </div>
          {source.organizer_email && (
            <p className="text-xs text-muted-foreground mt-1">
              Organized by {source.organizer_email}
            </p>
          )}
          {source.attendees && source.attendees.length > 0 && (
            <div className="flex items-center gap-1 mt-1">
              <Users className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">
                {source.attendees.length} attendee{source.attendees.length !== 1 ? 's' : ''}
              </span>
            </div>
          )}
          {source.location && source.location !== 'Unknown' && (
            <div className="flex items-center gap-1 mt-1">
              <MapPin className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground truncate">
                {source.location}
              </span>
            </div>
          )}
          <div className="flex items-center gap-2 mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCalendarClick}
              className="flex items-center gap-1 text-xs h-6 px-2"
            >
              <Calendar className="w-3 h-3" />
              View
              <ExternalLink className="w-2.5 h-2.5" />
            </Button>
            {source.meet_link && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMeetClick}
                className="flex items-center gap-1 text-xs h-6 px-2"
              >
                <Video className="w-3 h-3" />
                Join
                <ExternalLink className="w-2.5 h-2.5" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
