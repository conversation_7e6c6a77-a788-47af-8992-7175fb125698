{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug NestJS Application", "runtimeExecutable": "node", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register", "-r", "tsconfig-paths/register"], "args": ["${workspaceFolder}/src/main.ts"], "cwd": "${workspaceFolder}", "protocol": "inspector", "console": "integratedTerminal", "env": {"NODE_ENV": "development", "TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json"}, "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"type": "node", "request": "attach", "name": "Attach to NestJS", "port": 9229, "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}]}