"use client";

import { createContext, useContext, useState, ReactNode } from 'react';

interface KnowledgeBaseContextType {
    selectedKBs: string[];
    setSelectedKBs: (kbs: string[]) => void;
    knowledgeBases: { id: string; name: string; }[];
    setKnowledgeBases: (kbs: { id: string; name: string; }[]) => void;
}

const KnowledgeBaseContext = createContext<KnowledgeBaseContextType | undefined>(undefined);

export function KnowledgeBaseProvider({ children }: { children: ReactNode }) {
    const [selectedKBs, setSelectedKBs] = useState<string[]>([]);
    const [knowledgeBases, setKnowledgeBases] = useState<{ id: string; name: string; }[]>([]);

    return (
        <KnowledgeBaseContext.Provider value={{ 
            selectedKBs, 
            setSelectedKBs,
            knowledgeBases,
            setKnowledgeBases
        }}>
            {children}
        </KnowledgeBaseContext.Provider>
    );
}

export function useKnowledgeBase() {
    const context = useContext(KnowledgeBaseContext);
    if (context === undefined) {
        throw new Error('useKnowledgeBase must be used within a KnowledgeBaseProvider');
    }
    return context;
} 