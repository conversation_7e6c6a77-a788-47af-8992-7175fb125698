"use client";

import {
    QueryClient,
    QueryClientProvider,
    useQuery,
    useMutation,
    UseQueryOptions,
    UseMutationOptions
} from '@tanstack/react-query';
import { apiRouter } from './api/apiRouter';

export const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            refetchOnReconnect: false,
            retry: 1,
        },
    },
});

export const fetcherWithRouter = async <T>(
    servicePath: string
): Promise<T> => {
    const { data } = await apiRouter.mainApi.get<T>(servicePath);
    return data;
};

export const useApiQuery = <TQueryFnData = unknown, TError = unknown>(
    key: string[],
    servicePath: string,
    config?: UseQueryOptions<TQueryFnData, TError>
) => {
    return useQuery<TQueryFnData, TError>({
        queryKey: key,
        queryFn: () => fetcherWithRouter<TQueryFnData>(servicePath),
        ...config,
    });
};

export const useApiMutation = <
    TData = unknown,
    TError = unknown,
    TVariables = void,
    TContext = unknown
>(
    mutationFn: (variables: TVariables) => Promise<TData>,
    options?: Omit<UseMutationOptions<TData, TError, TVariables, TContext>, 'mutationFn'>
) => {
    return useMutation<TData, TError, TVariables, TContext>({
        mutationFn,
        ...options,
    });
};

export const createMutationFn = {
    post: <T>(servicePath: string) => async (data: any): Promise<T> => {
        const response = await apiRouter.mainApi.post<T>(servicePath, data);
        return response.data;
    },
    put: <T>(servicePath: string) => async (data: any): Promise<T> => {
        const response = await apiRouter.mainApi.put<T>(servicePath, data);
        return response.data;
    },
    patch: <T>(servicePath: string) => async (data: any): Promise<T> => {
        const response = await apiRouter.mainApi.put<T>(servicePath, data);
        return response.data;
    },
    delete: <T>(servicePath: string) => async (data?: any): Promise<T> => {
        const response = await apiRouter.mainApi.delete<T>(servicePath);
        return response.data;
    },
};

export const useOnboardingCheck = () => {
    return useApiQuery<{ result: boolean }>(
        ['onboarding-status'],
        'user/check-initial-onboarding'
    );
};

export { apiRouter };

export const apiClient = apiRouter.mainApi;