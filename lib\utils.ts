import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
type FileType = 'pdf' | 'doc' | 'sheet' | 'textfile' | 'unknown';

interface FileTypeMapping {
  type: FileType;
  color: string;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const fetcher = (...args: Parameters<typeof fetch>) => fetch(...args).then(res => res.json())
export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();

  const isToday = date.toDateString() === now.toDateString();

  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  const isYesterday = date.toDateString() === yesterday.toDateString();

  if (isToday) {
    return date.toLocaleTimeString('en-IN', { hour: 'numeric', minute: 'numeric', hour12: true });
  } else if (isYesterday) {
    return 'Yesterday';
  } else {
    return date.toLocaleDateString('en-IN', {
      weekday: 'long',
      month: 'short',
      day: 'numeric',
    });
  }
};

export const formatFileSize = (bytes: number) => {
  if (!bytes || isNaN(bytes) || bytes <= 0) return '0 KB';
  return bytes < 1024 * 1024
    ? `${(bytes / 1024).toFixed(2)} KB`
    : `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
};



export const getFileTypeAndColor = (mimeType: string): FileTypeMapping => {
  const fileTypeMapping: Record<string, FileTypeMapping> = {
    'application/pdf': { type: 'pdf', color: 'text-red-500' },
    'application/msword': { type: 'doc', color: 'text-blue-500' },
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { type: 'doc', color: 'text-blue-500' },
    'application/vnd.ms-excel': { type: 'sheet', color: 'text-green-500' },
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { type: 'sheet', color: 'text-green-500' },
    'text/plain': { type: 'textfile', color: 'text-gray-500' },
  };

  return fileTypeMapping[mimeType] || { type: 'unknown', color: 'text-muted-foreground' };
};

export const formatDateForChatHistory = (dateString: string) => {
  try {
      const date = dateString ? new Date(dateString) : new Date('2024-01-01');
      const now = new Date()
      const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

      if (diffInDays === 0) {
          return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true })
      } else if (diffInDays === 1) {
          return 'Yesterday'
      } else if (diffInDays < 7) {
          return date.toLocaleDateString('en-US', { weekday: 'long' })
      } else {
          return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      }
  } catch (error) {
      return 'Jan 1, 2024';
  }
}