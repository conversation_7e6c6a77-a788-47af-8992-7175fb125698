"use client"

import { ChatPanel } from '@/components/Chat/Panel';
import { useUser } from '@/contexts/UserContext';
import WelcomeScreen from '@/components/Chat/WelcomeScreen';
import { useRouter } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';
import useChatStore from '@/store/chatStore';
import { useEffect } from 'react';
import { Message } from '@/types/chat';

const ChatPage = () => {
    const { user } = useUser();
    const router = useRouter();
    const { setInput, setCurrentChat, addMessage } = useChatStore();

    useEffect(() => {
        setInput("");
        setCurrentChat({
            chat_id: null,
            user_id: user.id || ""
        });
    }, []);



    return (
        <div className="flex flex-col h-[92vh] w-full gap-10">
            <WelcomeScreen user={user.name || ""} />
            <ChatPanel />
        </div>
    )
}

export default ChatPage;