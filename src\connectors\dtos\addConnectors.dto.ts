import { ConnectorsStatus, ConnectorsType } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  ValidateNested,
  IsArray,
  ArrayNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';

export class ConnectorItem {
  @ApiProperty({
    description: 'The name of the connector',
    example: 'Slack Connector',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'URL or Base64 string of the connector logo',
    example: 'https://example.com/logo.png',
  })
  @IsString()
  @IsNotEmpty()
  logo: string;

  @ApiProperty({
    description: 'Detailed description of the connector',
    example: 'Integrates with Slack workspace for messaging',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Current status of the connector',
    enum: ConnectorsStatus,
    example: ConnectorsStatus.AVAILABLE,
  })
  @IsEnum(ConnectorsStatus)
  @IsNotEmpty()
  status: ConnectorsStatus;

  @ApiProperty({
    description: 'Type of the connector',
    enum: ConnectorsType,
    example: ConnectorsType.FREE,
  })
  @IsEnum(ConnectorsType)
  @IsNotEmpty()
  type: ConnectorsType;
}

export class CreateConnectorDto {
  @ApiProperty({
    description: 'Array of connectors',
    type: [ConnectorItem],
    isArray: true,
    example: [
      {
        name: 'Slack Connector',
        logo: 'https://example.com/logo.png',
        description: 'Integrates with Slack workspace for messaging',
        status: ConnectorsStatus.AVAILABLE,
        type: ConnectorsType.FREE,
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ConnectorItem)
  connectors: ConnectorItem[];
}
