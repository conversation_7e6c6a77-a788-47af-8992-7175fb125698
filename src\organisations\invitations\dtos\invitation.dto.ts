import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsUUID } from 'class-validator';
import { InvitationStatus, InvitationType } from '@prisma/client';

export class GetInvitationsDto {
  @ApiProperty({
    description: 'Organization ID to get invitations for',
    required: false,
    example: 'b9c2e3a7-43d5-4ed5-89ad-86448e40143f'
  })
  @IsOptional()
  @IsUUID()
  organizationId?: string;

  @ApiProperty({
    description: 'Invitation status to filter (optional)',
    enum: InvitationStatus,
    required: false,
    example: 'PENDING'
  })
  @IsOptional()
  @IsEnum(InvitationStatus)
  status?: InvitationStatus;

  @ApiProperty({
    description: 'Invitation type to filter (optional)',
    enum: InvitationType,
    required: false,
    example: 'ORGANIZATION'
  })
  @IsOptional()
  @IsEnum(InvitationType)
  type?: InvitationType;
}

export class CancelInvitationDto {
  @ApiProperty({
    description: 'Invitation ID to cancel',
    required: true,
  })
  @IsUUID()
  invitationId: string;
}

export class ResendInvitationDto {
  @ApiProperty({
    description: 'Invitation ID to resend',
    required: true,
  })
  @IsUUID()
  invitationId: string;
}
