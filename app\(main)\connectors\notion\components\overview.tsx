"use client";
import { formatDate } from '@/lib/utils'
import { ScrollArea } from '@steps-ai/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@steps-ai/ui'
import React from 'react'
import Image from 'next/image'



interface WorkspaceUser {
    id: string;
    name: string;
    avatarUrl: string | null;
    type: 'person' | 'bot';
}

interface NotionPage {
    id: string;
    title: string;
    url: string;
    createdTime: string;
    lastEditedTime: string;
}


const Overview = ({ data }: { data: any }) => {
    const metadata = JSON.parse(data.metadata);
    const workspaceUsers = metadata.workspaceUsers as WorkspaceUser[];
    const pages = metadata.pages as NotionPage[];
    return (
        <div className="grid gap-4 grid-cols-5">
            {/* Connected Pages */}
            <Card className='col-span-3 bg-background/5'>
                <CardHeader>
                    <CardTitle>Connected Pages</CardTitle>
                    <CardDescription>Pages accessible through this integration</CardDescription>
                </CardHeader>
                <CardContent>
                    <ScrollArea className='h-[400px]'>
                        <div className="grid gap-4">
                            {pages.map((page: any) => (
                                <div key={page.id} className="p-4 rounded-lg border bg-background/5 flex justify-between flex-row">
                                    <a href={page.url} target="_blank" rel="noopener noreferrer"
                                        className="text-primary hover:underline font-medium truncate max-w-[220px] block">
                                        {page.title}
                                    </a>
                                    <div className="mt-2 text-sm text-gray-500">
                                        <p>Created: {formatDate(page.createdTime)}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </ScrollArea>
                </CardContent>
            </Card>
            {/* Workspace Users */}
            <Card className='col-span-2 bg-background/5 h-fit'>
                <CardHeader>
                    <CardTitle>Workspace Members</CardTitle>
                    <CardDescription>People and bots with access to the workspace</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4">
                        {workspaceUsers.map((user: any) => (
                            <div key={user.id} className="flex items-center space-x-4">
                                <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-200">
                                    {user.avatarUrl && (
                                        <Image
                                            src={user.avatarUrl}
                                            alt={user.name}
                                            fill
                                            className="object-cover"
                                        />
                                    )}
                                </div>
                                <div>
                                    <p className="font-medium">{user.name}</p>
                                    <p className="text-sm text-gray-500 capitalize">{user.type}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default Overview
