import Image from 'next/image';
import { assets } from '@/lib/assets';
import { cn } from '@/lib/utils';

interface GmailSourceProps {
  source: {
    id: string;
    thread_id: string;
    subject: string;
    sender: string;
    recipients: string[];
    body: string;
    date: string;
    labels: string[];
    attachments: any;
    snippet: string;
    from: string;
  };
}

export function GmailSource({ source }: GmailSourceProps) {
  const getPreview = (text: string) => {
    return text.split(/\s+/).slice(0, 20).join(' ') + '...';
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (e) {
      return dateStr;
    }
  };

  return (
    <div className="p-2 border-b border-border last:border-0 hover:bg-accent/50 transition-colors cursor-pointer">
      <div className="flex items-start gap-2">
        <div className="mt-1 flex-shrink-0">
          <div className="w-4 h-4 rounded-full overflow-hidden bg-muted flex items-center justify-center">
            <Image 
              src={assets.Icons.Gmail} 
              alt="Gmail" 
              width={16} 
              height={16}
              className="w-full h-full object-contain"
            />
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-foreground truncate">
            {source.subject}
          </h4>
          <div className="flex items-center gap-2 mt-0.5">
            <p className="text-xs text-muted-foreground truncate">
              From: {source.from}
            </p>
            {source.date && (
              <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-muted/50 text-muted-foreground/70 whitespace-nowrap">
                {formatDate(source.date)}
              </span>
            )}
          </div>
          <p className={cn(
            "text-xs text-muted-foreground mt-1 line-clamp-2",
            !source.subject && "mt-0"
          )}>
            {getPreview(source.snippet)}
          </p>
        </div>
      </div>
    </div>
  );
} 