import {
  PrismaClient,
  ConnectorsStatus,
  ConnectorsType,
  PlanType,
  Type,
  Theme,
  Role,
} from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Create a default plan
  const plan = await prisma.plan.upsert({
    where: { name: 'Free Plan' },
    update: {},
    create: {
      type: PlanType.FREE,
      name: 'Free Plan',
      messagesPerDay: 100,
      maxStorage: 1024, // 1GB in MB
      maxKnowledgeBases: 3,
      priceMonthly: 0,
      priceYearly: 0,
    },
  });

  console.log(`Created plan: ${plan.name}`);

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: hashedPassword,
      type: Type.ADMIN,
      role: Role.SUPERADMIN,
      emailVerified: true,
      themePreference: Theme.LIGHT,
      hasOnboarded: true,
      onboardingCompleted: true,
      planId: plan.id,
    },
  });

  console.log(`Created admin user: ${admin.email}`);

  // Create connectors
  const connectorsData = [
    {
      id: '42b80924-1769-4ad5-870b-d037e29a6604',
      name: 'HubSpot',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/hubspot.svg',
      description: 'Integrate with your HubSpot CRM and marketing data',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'CRM & Support',
      slug: 'hubspot',
    },
    {
      id: '************************************',
      name: 'Microsoft Outlook',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/outlook.svg',
      description: 'Integrate your Outlook emails and calendars',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.FREE,
      createdAt: new Date('2025-01-19 14:55:41.664'),
      updatedAt: new Date('2025-01-19 14:55:41.664'),
      category: 'Communication',
      slug: 'outlook',
    },
    {
      id: '9fc8ee90-a901-4d75-9e71-5c8428a90a10',
      name: 'Evernote',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/evernote.svg',
      description: 'Import notes and documents from Evernote',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 14:55:41.664'),
      updatedAt: new Date('2025-01-19 14:55:41.664'),
      category: 'Productivity',
      slug: 'evernote',
    },
    {
      id: 'f91704a9-8045-44e9-927a-28125c95fbb2',
      name: 'Confluence',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/confluence.svg',
      description: 'Access your Confluence workspace and documents',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 14:55:41.664'),
      updatedAt: new Date('2025-01-19 14:55:41.664'),
      category: 'Productivity',
      slug: 'confluence',
    },
    {
      id: 'a427fb31-d199-4146-a232-8763f6eba4ed',
      name: 'Salesforce',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/salesforce.svg',
      description: 'Connect to your Salesforce CRM instance',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.ENTERPRISE,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'CRM & Support',
      slug: 'salesforce',
    },
    {
      id: '8e9d1545-bf18-42d2-9fe2-ad51daff39e4',
      name: 'Notion',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/notion.svg',
      description: 'Access your Notion workspaces and pages',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'Productivity',
      slug: 'notion',
    },
    {
      id: 'b105877c-0b8a-4771-b2ea-098191726c7c',
      name: 'Amazon Redshift',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/redshift.svg',
      description: 'Connect to your Amazon Redshift data warehouse',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'Database',
      slug: 'redshift',
    },
    {
      id: '550cf684-1106-4197-ab90-d2adbc7bcb7d',
      name: 'Google Drive',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/drive.svg',
      description: 'Connect and sync your Google Drive documents',
      status: ConnectorsStatus.AVAILABLE,
      type: ConnectorsType.FREE,
      createdAt: new Date('2025-01-19 14:55:41.664'),
      updatedAt: new Date('2025-01-19 14:55:41.664'),
      category: 'Cloud Storage',
      slug: 'googledrive',
    },
    {
      id: 'b5017107-424d-4554-ab19-c6f5c5a7a971',
      name: 'SharePoint',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/sharepoint.svg',
      description: 'Connect to your SharePoint sites and documents',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.ENTERPRISE,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'Cloud Storage',
      slug: 'sharepoint',
    },
    {
      id: 'a9aa9d6d-38e3-4115-a882-95ca61358b8f',
      name: 'Zendesk',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/zendesk.svg',
      description: 'Access your Zendesk customer support data',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'CRM & Support',
      slug: 'zendesk',
    },
    {
      id: '70c8f3a1-6d80-4658-b12f-fcdfecb2be46',
      name: 'PostgreSQL',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/postgres.svg',
      description: 'Connect and query your PostgreSQL databases',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'Databases',
      slug: 'postgres',
    },
    {
      id: 'ee01f710-8bc4-483b-a856-6e23f1d8e6a4',
      name: 'Amazon RDS',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/rds.svg',
      description: 'Integrate with Amazon RDS managed databases',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'Databases',
      slug: 'rds',
    },
    {
      id: '46f6418e-afaa-4ed7-9a6c-45c5f958f2cc',
      name: 'MongoDB',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/mongodb.svg',
      description: 'Access and query your MongoDB databases',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'Databases',
      slug: 'mongodb',
    },
    {
      id: '43b7210c-9dad-4c69-8949-9af324ae640f',
      name: 'Jira',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/jira.svg',
      description: 'Connect to your Jira projects and issues',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 14:55:41.664'),
      updatedAt: new Date('2025-01-19 14:55:41.664'),
      category: 'Productivity',
      slug: 'jira',
    },
    {
      id: '6661180c-aac3-464d-97a8-0cddf6171896',
      name: 'Gmail',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/gmail.svg',
      description: 'Integrate your Gmail emails and attachments',
      status: ConnectorsStatus.AVAILABLE,
      type: ConnectorsType.FREE,
      createdAt: new Date('2025-01-19 17:03:38.143'),
      updatedAt: new Date('2025-01-19 17:03:38.143'),
      category: 'Communication',
      slug: 'gmail',
    },
    {
      id: 'ec3cc2f1-1a3d-4d5e-9c49-429f770cb183',
      name: 'Slack',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/slack.svg',
      description: 'Import conversations and channels from Slack',
      status: ConnectorsStatus.AVAILABLE,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-01-19 14:55:41.664'),
      updatedAt: new Date('2025-01-19 14:55:41.664'),
      category: 'Communication',
      slug: 'slack',
    },
    {
      id: '76713666-0969-4b50-9f8c-117abd9582b0',
      name: 'GitHub',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/github.svg',
      description:
        'Integrate your github to search through issues and pull requests',
      status: ConnectorsStatus.AVAILABLE,
      type: ConnectorsType.FREE,
      createdAt: new Date('2025-01-28 12:41:01.052'),
      updatedAt: new Date('2025-01-28 12:41:01.052'),
      category: 'Productivity',
      slug: 'github',
    },
    {
      id: 'a842ccaa-5f82-42e6-a0ec-ca3ed8cbcbd5',
      name: 'One Drive',
      logo: 'https://stepsai-assets.s3.us-west-2.amazonaws.com/icons/onedrive.svg',
      description: 'Integrates with Slack workspace for messaging',
      status: ConnectorsStatus.COMING_SOON,
      type: ConnectorsType.PAID,
      createdAt: new Date('2025-02-08 10:18:01.740'),
      updatedAt: new Date('2025-02-08 10:18:01.740'),
      category: 'Cloud Storage',
      slug: 'onedrive',
    },
  ];

  // Insert connectors data
  for (const connector of connectorsData) {
    await prisma.connectors.upsert({
      where: { id: connector.id },
      update: connector,
      create: connector,
    });
  }

  console.log(`Added ${connectorsData.length} connectors`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
