<html>

<head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Welcome to Aide</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.7;
            margin: 0;
            padding: 0;
            background-color: #fafafa;
            color: #2d3748;
        }

        .email-container {
            max-width: 600px;
            width: 100%;
            margin: 40px auto;
            background: #ffffff;
            padding: 48px 32px;
            border-radius: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #1a202c;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            color: #4a5568;
            font-size: 16px;
            margin: 0;
        }

        .welcome-message {
            background-color: #f7fafc;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 32px;
            font-size: 16px;
            color: #4a5568;
        }

        .value-proposition {
            margin-bottom: 32px;
        }

        .value-proposition h2 {
            font-size: 22px;
            color: #1a202c;
            margin-bottom: 16px;
        }

        .value-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .value-list li {
            padding: 12px 0;
            border-bottom: 1px solid #edf2f7;
            color: #4a5568;
        }

        .value-list li:last-child {
            border-bottom: none;
        }

        .cta-button {
            display: inline-block;
            padding: 14px 32px;
            background-color: #4299e1;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .cta-button:hover {
            background-color: #3182ce;
            transform: translateY(-1px);
        }

        .footer {
            margin-top: 40px;
            padding-top: 24px;
            border-top: 1px solid #edf2f7;
            color: #718096;
            font-size: 14px;
            text-align: center;
        }

        .footer a {
            color: #4299e1;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                padding: 24px 16px;
                border-radius: 0;
            }
        }
    </style>
</head>

<body>
    <div class='email-container'>
        <div class='header'>
            <h1>Welcome, ${name}! ✨</h1>
            <p>Your AI Companion</p>
        </div>

        <div class='welcome-message'>
            I'm Aide, your dedicated AI companion from today! I'm here to assist you by seamlessly bringing information
            from various data platforms you use—so you can focus on what truly matters.
        </div>

        <div class='value-proposition'>
            <h2>Let's Start This Journey Together</h2>
            <ul class='value-list'>
                <li>📊 Simplify your data access and analysis</li>
                <li>🔍 Help you find information quickly and efficiently</li>
                <li>💡 Provide insights when you need them</li>
                <li>🎯 Support your decision-making process</li>
            </ul>
        </div>

        <div style="text-align: center;">
            <a href="https://docs.stepsai.co" class="cta-button">Get Started with Aide</a>
        </div>

        <p style="text-align: center; margin-top: 32px;">
            Best regards,<br />
            Aide
        </p>

        <div class='footer'>
            <p>Need help? Reach out anytime at <a href='mailto:<EMAIL>'><EMAIL></a></p>
            <p>Your AI Companion<br />Steps AI</p>
            <p>Looking forward to working with you! 😊</p>
        </div>
    </div>
</body>

</html>