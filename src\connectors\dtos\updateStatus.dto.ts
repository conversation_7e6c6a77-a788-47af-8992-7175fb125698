import { IsS<PERSON>, <PERSON><PERSON>ptional, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EtlStatus } from '@prisma/client';

export class UpdateStatusDto {
    @ApiProperty({ description: 'Task ID' })
    @IsString()
    id: string;

    @ApiProperty({
        description: 'Status of the task',
        required: true,
    })
    @IsEnum(EtlStatus)
    status: EtlStatus;
}