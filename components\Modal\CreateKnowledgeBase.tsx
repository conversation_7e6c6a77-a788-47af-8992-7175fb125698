"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogTrigger,
} from "@steps-ai/ui";
import { Input } from "@steps-ai/ui";
import { Button } from "@steps-ai/ui";
import { Label } from "@steps-ai/ui";
import { Book, Plus, Loader2, CheckCircle2, Sparkles } from "lucide-react";
import {
    useApiMutation,
    createMutationFn,
    queryClient
} from "@/lib/apiClient";
import { useRouter } from "next/navigation";

interface CreateKnowledgeBaseProps {
    onKnowledgeBaseCreate?: (name: string) => void;
    className?: string;
    children?: React.ReactNode;
}

const CreateKnowledgeBase: React.FC<CreateKnowledgeBaseProps> = ({
    onKnowledgeBaseCreate,
    className,
    children,
}) => {
    const router = useRouter();
    const [isOpen, setIsOpen] = useState(false);
    const [name, setName] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [error, setError] = useState("");
    const [isHovered, setIsHovered] = useState(false);

    const createKnowledgebase = useApiMutation(
        createMutationFn.post('/knowledgebase'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['knowledgebases'] });
                    setIsSuccess(true);
                    router.push(`/knowledgebase/configuration?id=${response.result.id}`);
                } else {
                    setError("Unexpected response from server");
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again.";
                    setError(errorMessage);
                } else if (error.request) {
                    setError("No response received from server");
                } else {
                    setError("Error setting up the request");
                }
            }
        }
    );

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!name.trim()) {
            setError("Please enter a name for your knowledge base");
            return;
        }
        // console.log("Creating knowledge base with name:", name);
        // setError("");
        setIsSubmitting(true);

        try {
            createKnowledgebase.mutate({ name });
        } catch (err) {
            console.error(err);
            setError("An unexpected error occurred");
        }
    };

    const handleReset = () => {
        setName("");
        setIsSubmitting(false);
        setIsSuccess(false);
        setError("");
        setIsOpen(false);
    };

    const buttonVariants = {
        initial: { scale: 1 },
        hover: { scale: 1.02 },
        tap: { scale: 0.98 },
    };

    const iconVariants = {
        initial: { rotate: 0 },
        hover: { rotate: 180, transition: { duration: 0.3 } },
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                {children ? (
                    <Button
                        variant="ghost"
                        className={className}
                    >
                        {children}
                    </Button>
                ) : (

                    <Button
                        className="relative bg-gradient-to-r from-[#2c487c] to-[#3B6BB5] text-white 
                            transition-all duration-200 shadow-lg  pr-6 pl-4"
                    >
                        Add new knowledge base

                    </Button>
                )}
            </DialogTrigger>

            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <motion.div
                            initial={{ rotate: -20, opacity: 0 }}
                            animate={{ rotate: 0, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                        >
                            <Book className="h-5 w-5 text-text-primary" />
                        </motion.div>
                        <motion.span
                            initial={{ x: -20, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.2 }}
                        >
                            Create New Knowledge Base
                        </motion.span>
                    </DialogTitle>
                    <DialogDescription
                        className="text-text-secondary"
                    >
                        <motion.div
                            initial={{ y: -10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.3 }}
                        >
                            Give your knowledge base a unique name to get started.
                        </motion.div>
                    </DialogDescription>
                </DialogHeader>

                <motion.form
                    onSubmit={handleSubmit}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="space-y-6"
                >
                    <div className="space-y-2">
                        <Label htmlFor="name" className="text-right">
                            Name
                        </Label>
                        <div className="relative">
                            <Input
                                id="name"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                placeholder="e.g., Product Documentation"
                                className={`pr-8 ${error ? "border-red-500 focus:ring-red-500" : ""
                                    }`}
                                disabled={isSubmitting || isSuccess}
                            />
                            <AnimatePresence>
                                {error && (
                                    <motion.p
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -10 }}
                                        className="text-destructive text-sm mt-1"
                                    >
                                        {error}
                                    </motion.p>
                                )}
                            </AnimatePresence>
                        </div>
                    </div>

                    <DialogFooter className="flex gap-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleReset}
                            disabled={isSubmitting}
                            className="flex-1"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting || isSuccess}
                            className="flex-1 relative bg-gradient-to-r from-[#2c487c] to-[#3B6BB5] text-white"
                        >
                            <AnimatePresence mode="wait">
                                {isSubmitting ? (
                                    <motion.div
                                        key="submitting"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        className="flex items-center gap-2"
                                    >
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        Creating...
                                    </motion.div>
                                ) : isSuccess ? (
                                    <motion.div
                                        key="success"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        className="flex items-center gap-2"
                                    >
                                        <CheckCircle2 className="h-4 w-4" />
                                        Created!
                                    </motion.div>
                                ) : (
                                    <motion.span
                                        key="create"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                    >
                                        Create Knowledge Base
                                    </motion.span>
                                )}
                            </AnimatePresence>
                        </Button>
                    </DialogFooter>
                </motion.form>
            </DialogContent>
        </Dialog >
    );
};

export default CreateKnowledgeBase;
