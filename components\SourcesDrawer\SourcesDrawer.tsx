import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronDown } from 'lucide-react';
import { Button } from '@steps-ai/ui';
import { cn } from '@/lib/utils';
import { useSourcesDrawer, selectStructuredSources } from '@/store/sourcesDrawerStore';
import useChatStore from '@/store/chatStore';
import { GmailSource } from './GmailSource';
import { WebSource } from './WebSource';
import { GoogleDriveSource } from './GoogleDriveSource';
import { GoogleCalendarSource } from './GoogleCalendarSource';
import { KnowledgeBaseSource } from './KnowledgeBaseSource';
import { useStore } from 'zustand';

interface SourcesDrawerProps {
  isMobile?: boolean;
  isTablet?: boolean;
}

interface SourceGroup {
  type: string;
  sources: any[];
}

export function SourcesDrawer({ isMobile = false, isTablet = false }: SourcesDrawerProps) {
  const { isOpen, setIsOpen, currentMessage } = useSourcesDrawer();
  const structuredSources = useStore(useSourcesDrawer, selectStructuredSources);

  const [sourceGroups, setSourceGroups] = useState<SourceGroup[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);

  useEffect(() => {
    if (!structuredSources || !Array.isArray(structuredSources)) {
    //   console.log('No structured sources or invalid format:', structuredSources);
      setSourceGroups([]);
      setExpandedGroups([]);
      return;
    }

    // console.log('Processing structured sources:', structuredSources);

    const groups: { [key: string]: any[] } = {};

    structuredSources.forEach((sourceItem) => {
      const toolName = sourceItem.type;
    //   console.log('Processing source item:', sourceItem, 'with type:', toolName);

      if (toolName) {
        if (!groups[toolName]) {
          groups[toolName] = [];
        }
        groups[toolName].push(sourceItem);
      }
    });

    // console.log('Grouped sources:', groups);

    const formattedGroups: SourceGroup[] = Object.keys(groups).map(toolName => ({
      type: toolName,
      sources: groups[toolName]
    })).filter(group => group.sources.length > 0);

    // console.log('Formatted groups:', formattedGroups);

    setSourceGroups(formattedGroups);

    if (formattedGroups.length > 0) {
      setExpandedGroups(formattedGroups.map(group => group.type));
    } else {
      setExpandedGroups([]);
    }

  }, [structuredSources]);

  const toggleGroup = (type: string) => {
    setExpandedGroups(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const renderSourceGroup = (group: SourceGroup) => {
    const isExpanded = expandedGroups.includes(group.type);
    
    const displayTitle = group.type === 'Web Search' ? 'Web Sources' :
                        group.type === 'Google Drive' ? 'Google Drive Files' :
                        group.type === 'Google Calendar' ? 'Calendar Events' :
                        group.type === 'Knowledge Base' ? 'Knowledge Base' :
                        group.type;

    return (
      <div key={group.type} className="border-b border-border last:border-0">
        <button
          onClick={() => toggleGroup(group.type)}
          className="w-full flex items-center justify-between p-3 hover:bg-accent/50 transition-colors"
        >
          <span className="font-medium text-sm capitalize">
            {displayTitle}
          </span>
          <ChevronDown 
            className={cn(
              "w-4 h-4 transition-transform",
              isExpanded ? "transform rotate-180" : ""
            )}
          />
        </button>
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="p-2 space-y-1">
                {group.sources.map((source, idx) => (
                  group.type === 'Web Search' ? (
                    <WebSource key={source.url || idx} source={source} />
                  ) : group.type === 'Gmail' ? (
                    <GmailSource key={source.id || idx} source={source} />
                  ) : group.type === 'Google Drive' ? (
                    <GoogleDriveSource key={source.id || idx} source={source} />
                  ) : group.type === 'Google Calendar' ? (
                    <GoogleCalendarSource key={source.id || idx} source={source} />
                  ) : group.type === 'Knowledge Base' ? (
                    <KnowledgeBaseSource key={source.connector_id || source.name || idx} source={source} />
                  ) : (
                    <div key={idx} className="text-sm text-muted-foreground">Unknown Source Type: {group.type}</div>
                  )
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  if (isTablet) {
    return (
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: "60vh" }}
            exit={{ height: 0 }}
            className="bg-background border-t border-border rounded-t-xl shadow-lg"
          >
            <div className="flex justify-end p-2">
              <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="overflow-y-auto h-[calc(60vh-40px)]">
              {sourceGroups.map(renderSourceGroup)}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: "320px" }}
          exit={{ width: 0 }}
          className="h-full bg-background border-l border-border"
        >
          <div className="flex justify-end p-2">
            <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="overflow-y-auto h-[calc(100vh-40px)]">
            {sourceGroups.map(renderSourceGroup)}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
} 