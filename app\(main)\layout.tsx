import MainLayout from "@/components/Layouts/MainLayout";
import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import { Toaster } from 'sonner';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from "@/lib/apiClient";
import { UserProvider } from "@/contexts/UserContext";
import AppWalkthrough from "@/components/Walkthrough";
import { ThemeProvider } from '@/components/providers/ThemeProvider'

export default async function AppLayout({
    children,
}: {
    children: React.ReactNode;
}) {

    const session = await getSession();
    if (!session) redirect("/");


    return (
        <ThemeProvider attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange>
            <UserProvider>
                <QueryClientProvider client={queryClient}>
                        <div className="flex flex-row h-screen">
                            <MainLayout>
                                {children}
                                <Toaster richColors expand={true} />
                            </MainLayout>
                        </div>
                </QueryClientProvider>
            </UserProvider>

        </ThemeProvider>
    );
}