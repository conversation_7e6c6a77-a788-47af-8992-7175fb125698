import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsEmail, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class BulkAddMembersDto {
  @ApiProperty({
    description: 'Team ID',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  teamId: string;

  @ApiProperty({
    description: 'List of email addresses to add to the team',
    required: true,
    example: ['<EMAIL>', '<EMAIL>'],
    type: [String],
  })
  @IsArray()
  @IsEmail({}, { each: true })
  @IsNotEmpty()
  emails: string[];
}

export class BulkAddMembersResultDto {
  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Status of the operation',
    example: 'success',
  })
  @IsString()
  status: 'success' | 'error' | 'pending';

  @ApiProperty({
    description: 'Message describing the result',
    example: 'User added to team successfully',
  })
  @IsString()
  message: string;
}
