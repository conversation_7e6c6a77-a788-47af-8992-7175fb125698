import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CommonService } from 'src/common/common.service';
import { RenameFileDto } from '../dtos/rename-file.dto';

@Injectable()
export class FilesService {
  constructor(
    private prisma: PrismaService,
    private commonService: CommonService,
  ) {}

  async renameFile(data: RenameFileDto, userId: string, fileId: string) {
    const file = await this.prisma.file.findUnique({
      where: { id: fileId },
      include: { knowledgeBase: { include: { owner: true } } },
    });

    if (!file || file.knowledgeBase.ownerId !== userId) {
      throw new BadRequestException('File not found or access denied');
    }

    this.prisma.file.update({
      where: { id: fileId },
      data: { name: data.name },
    });
    return 'File renamed successfully';
  }

  async deleteFile(fileId: string, userId: string) {
    const file = await this.prisma.file.findUnique({
      where: { id: fileId },
      include: { knowledgeBase: { include: { owner: true } } },
    });

    if (!file || file.knowledgeBase.ownerId !== userId) {
      throw new BadRequestException('File not found or access denied');
    }

    // Extract the file key from the URL
    const fileUrl = file.url;
    const s3Key = fileUrl.split('.amazonaws.com/')[1];

    // Delete from S3 first
    await this.commonService.deleteFilesFromS3([s3Key]);

    // Then delete from database
    await this.prisma.file.delete({
      where: { id: fileId },
    });

    return 'File deleted successfully';
  }
}
