import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateSupportTicketDto } from '../dtos/support.dto';

@Injectable()
export class SupportV1Service {
    constructor(private prisma: PrismaService) { }

    async createSupportTicket(dto: CreateSupportTicketDto, userId: string   ) {
        return this.prisma.support.create({
            data: {
                email: dto.email,
                message: dto.message,
                area: dto.area,
                userId: userId,
            },
        });
    }

    async getSupportTickets(userId?: string) {
        return this.prisma.support.findMany({
            where: userId ? { userId } : undefined,
            orderBy: { createdAt: 'desc' },
        });
    }

    async getSupportTicketById(id: string) {
        return this.prisma.support.findUnique({
            where: { id },
        });
    }
}
