import { Injectable } from '@nestjs/common';
import { promisify } from 'util';
import { createCipheriv, createDecipheriv, randomBytes, scrypt } from 'crypto';

import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { PrismaService } from '@/prisma/prisma.service';

@Injectable()
export class CommonService {
  private s3Client: S3Client;
  private encryptionKey: Buffer;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    const key = this.configService.get<string>('encryption.key');
    if (!key)
      throw new Error('ENCRYPTION_KEY environment variable is required');
    this.encryptionKey = Buffer.from(key, 'hex');

    if (process.env.AWS_ACCESS_KEY && process.env.AWS_KEY_SECRET) {
      this.s3Client = new S3Client({
        region: process.env.AWS_REGION,
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY,
          secretAccessKey: process.env.AWS_KEY_SECRET,
        },
      });
    }
  }

  async toHash(password: string) {
    const scryptAsync = promisify(scrypt);
    const salt = randomBytes(16).toString('hex');
    const buf = (await scryptAsync(password, salt, 64)) as Buffer;
    return `${buf.toString('hex')}.${salt}`;
  }

  async compare(storedPassword: string, suppliedPassword: string) {
    const scryptAsync = promisify(scrypt);
    const [hashedPassword, salt] = storedPassword.split('.');
    const buf = (await scryptAsync(suppliedPassword, salt, 64)) as Buffer;
    return buf.toString('hex') === hashedPassword;
  }

  async checkUser(email: string) {
    const user = await this.prisma.user.findUnique({
      where: { email },
    });
    if (user) {
      return true;
    }
    return false;
  }

  async uploadFilesToS3(files: Express.Multer.File[], folderName: string) {
    const uploadPromises = files.map(async (file) => {
      const sanitizedFileName = this.sanitizeFileName(file.originalname);
      const uniqueFileName = `${sanitizedFileName}`;

      const fileKey = `${folderName}/${uniqueFileName}`;

      const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: fileKey,
        Body: file.buffer,
        ContentType: file.mimetype,
      };

      const command = new PutObjectCommand(params);
      try {
        await this.s3Client.send(command);
        return {
          name: file.originalname,
          url: `${process.env.AWS_CDN_URL}/${fileKey}`,
          size: file.size,
          mimeType: file.mimetype,
        };
      } catch (error) {
        console.error(`Failed to upload ${file.originalname}:`, error);
        throw new Error(`Failed to upload ${file.originalname}`);
      }
    });

    return Promise.all(uploadPromises);
  }

  private sanitizeFileName(originalName: string): string {
    return originalName
      .normalize('NFC')
      .replace(/[^\p{L}\p{N}._-]/gu, '_')
      .replace(/^\.+/, '')
      .replace(/\.{2,}/g, '.')
      .trim()
      .substring(0, 255);
  }
  async deleteFilesFromS3(files: string[]) {
    const deletePromises = files.map(async (file) => {
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: file,
      };
      try {
        await this.s3Client.send(new DeleteObjectCommand(params));
      } catch (error) {
        console.error(`Failed to delete ${file}:`, error);
        throw new Error(`Failed to delete ${file}`);
      }
    });

    return Promise.all(deletePromises);
  }

  encrypt(text: string): { encryptedData: string; iv: string } {
    const iv = randomBytes(16);
    const cipher = createCipheriv('aes-256-gcm', this.encryptionKey, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      encryptedData: encrypted + ':' + authTag.toString('hex'),
      iv: iv.toString('hex'),
    };
  }

  decrypt(encryptedData: string, iv: string): string {
    const [encrypted, authTag] = encryptedData.split(':');
    const decipher = createDecipheriv(
      'aes-256-gcm',
      this.encryptionKey,
      Buffer.from(iv, 'hex'),
    );

    decipher.setAuthTag(Buffer.from(authTag, 'hex'));

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  async getPresignedUrl(key: string, contentType: string): Promise<string> {
    if (!this.s3Client) {
      throw new Error('S3 client not configured');
    }
    const command = new PutObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: key,
      ContentType: contentType,
    });
    // URL expires in 10 minutes
    return getSignedUrl(this.s3Client, command, { expiresIn: 600 });
  }
}
