import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { google } from 'googleapis';
import { ConfigService } from '@nestjs/config';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { ConnectorsService } from '../../../v1/connectors.v1.service';
import { OAuthConnectorService } from 'src/connectors/interfaces/oauth.interface';
import { CrawlGoogleDriveDto } from './dtos/google.dto';
import { EtlStatus } from '@prisma/client';
import { EtlService } from '@/etl/etl.service';

@Injectable()
export class GoogleService extends OAuthConnectorService {
  private oauth2Client: any;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
    private etlService: EtlService,
  ) {
    super();
    this.oauth2Client = new google.auth.OAuth2(
      this.configService.get<string>('auth.google.clientId'),
      this.configService.get<string>('auth.google.clientSecret'),
      this.configService.get<string>('auth.google.redirectUri') +
        '/googledrive',
    );
  }

  async generateRedirectUrl(userId: string) {
    const scopes = [
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
      state: userId,
    });
  }

  async handleCallback(code: string, state: string) {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      const { access_token, refresh_token, expiry_date } = tokens;
      const userId = state;

      this.oauth2Client.setCredentials(tokens);
      const oauth2 = google.oauth2('v2');
      const userInfo = await oauth2.userinfo.get({ auth: this.oauth2Client });

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Google Drive',
        },
        select: {
          id: true,
          type: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('Google Drive connector not found');
      }

      await this.prisma.userConnectors.create({
        data: {
          userId,
          connectorId: connector.id,
          config: {
            email: userInfo.data.email,
            name: userInfo.data.name,
            picture: userInfo.data.picture,
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(access_token);
      const { encryptedData: refreshTokenEncrypted, iv: refreshTokenIv } =
        this.connectorsService.encrypt(refresh_token);

      const userConnectorDetails = {
        user_id: userId,
        connector_id: connector.id,
        connector_type: connector.type,
        name: 'Google Drive',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
          refresh_token: refreshTokenEncrypted,
          refresh_token_iv: refreshTokenIv,
          expiry_date: expiry_date,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        userConnectorDetails,
      );

      return {
        success: true,
        message: 'Google Drive integration connected successfully',
        data: {
          email: userInfo.data.email,
          name: userInfo.data.name,
          picture: userInfo.data.picture,
        },
      };
    } catch (error) {
      throw new BadRequestException('Failed to process Google callback');
    }
  }

  async refreshToken(refreshToken: string, userId: string) {
    try {
      this.oauth2Client.setCredentials({
        refresh_token: refreshToken,
      });

      const { credentials } = await this.oauth2Client.refreshAccessToken();
      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'Google Drive',
          },
        },
      );

      if (!userConnectors?.[0]) {
        throw new NotFoundException('User connector not found');
      }

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(credentials.access_token);

      const updatedUserConnector = {
        ...userConnectors[0],
        updated_at: new Date().toISOString(),
        credentials: {
          ...userConnectors[0].credentials,
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
          expiry_date: credentials.expiry_date,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        updatedUserConnector,
      );

      return {
        accessToken: credentials.access_token,
        expiresAt: credentials.expiry_date,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      if (error.response?.data?.error === 'invalid_grant') {
        throw new BadRequestException(
          'Refresh token is invalid or has expired. User needs to re-authenticate.',
        );
      }

      throw new BadRequestException(
        `Failed to refresh access token: ${error.message}`,
      );
    }
  }

  async disconnect(userId: string) {
    try {
      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Google Drive',
        },
        select: {
          id: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('Google Drive connector not found');
      }

      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'Google Drive',
          },
        },
      );

      const userConnector = userConnectors?.[0];
      if (!userConnector) {
        throw new NotFoundException(
          'No active Google connection found for this user',
        );
      }

      const decryptedAccessToken = this.connectorsService.decrypt(
        userConnector.credentials.access_token,
        userConnector.credentials.access_token_iv,
      );

      if (decryptedAccessToken) {
        try {
          this.oauth2Client.setCredentials({
            access_token: decryptedAccessToken,
          });
          await this.oauth2Client.revokeToken(decryptedAccessToken);
        } catch (error) {
          const errorMessage = error.response?.data?.error || error.message;
        }
      }

      await this.prisma.userConnectors.deleteMany({
        where: {
          userId,
          connectorId: connector.id,
        },
      });

      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'Google Drive',
      });

      return {
        success: true,
        message: 'Successfully disconnected Google integration',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to disconnect Google integration: ${error.message}`,
      );
    }
  }

  async crawlGoogleDrive(userId: string, data: CrawlGoogleDriveDto) {
    const { userConnectorId } = data;
    const etlRun = await this.prisma.etl.findFirst({
      where: {
        userConnectorId: userConnectorId,
      },
    });

    if (etlRun && etlRun.status !== EtlStatus.FAILED) {
      throw new BadRequestException('ETL in progress!');
    }

    if (etlRun && etlRun.status === EtlStatus.FAILED) {
      await this.prisma.etl.delete({
        where: {
          id: etlRun.id,
        },
      });
    }

    // const etlResponse = await this.etlService.publish(
    //   userId,
    //   ConnectorName.GOOGLE_DRIVE,
    //   userConnectorId,
    // );

    // await this.prisma.etl.create({
    //   data: {
    //     dagId: etlResponse.dagId,
    //     dagRunId: etlResponse.dagRunId,
    //     status: etlResponse.state,
    //     startDate: etlResponse.executionDate,
    //     userConnectorId: userConnectorId,
    //   },
    // });

    return {
      success: true,
      message: 'Google Drive crawl started successfully',
    };
  }
}
