import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { AdminLoginDto } from './dtos/login.dto';
import { PaginationDto } from './dtos/pagination.dto';
import { AuthService } from '../auth/v1/auth.v1.service';
import { PrismaService } from '../prisma/prisma.service';
import {
  CreateOrganizationAdminDto,
  CreateOrganizationDto,
} from './dtos/onboarding.dto';
import { Role } from '@prisma/client';
import { EmailService } from '../email/email.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UpdateOrgAdminDto } from './dtos/update-org-admin.dto';
import { MilvusService } from '@/milvus/milvus.service';

@Injectable()
export class SuperAdminService {
  constructor(
    private readonly authService: AuthService,
    private readonly prismaService: PrismaService,
    private readonly emailService: EmailService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly milvusService: MilvusService,
  ) {}

  async login(loginDto: AdminLoginDto) {
    const authResult = await this.authService.loginWithCredentials({
      email: loginDto.email,
      password: loginDto.password,
    });

    const user = await this.prismaService.user.findUnique({
      where: { email: loginDto.email },
      select: { role: true },
    });

    if (!user || user.role.toString() !== 'SUPERADMIN') {
      throw new UnauthorizedException('SuperAdmin access required');
    }

    // Generate tokens
    const tokens = await this.authService.generateTokens(authResult);
    return {
      ...tokens,
      user: authResult,
    };
  }

  async listUsers(paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;

    const [users, total] = await Promise.all([
      this.prismaService.user.findMany({
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          type: true,
          emailVerified: true,
          hasOnboarded: true,
          createdAt: true,
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prismaService.user.count(),
    ]);

    return {
      data: users,
      metadata: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async onboardOrganization(
    createOrganizationDto: CreateOrganizationDto,
    superadminId: string,
  ) {
    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    const safeOrgName = createOrganizationDto.name
      .toLowerCase()
      .replace(/[^a-z0-9_]+/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_+|_+$/g, '');
    const slug = `${safeOrgName}_${timestamp}`;

    const collectionCreated = await this.milvusService.createCollection({
      collectionName: slug,
    });

    if (!collectionCreated) {
      throw new BadRequestException('Collection already exists');
    }

    const organization = await this.prismaService.organization.create({
      data: {
        name: createOrganizationDto.name,
        description: createOrganizationDto.description,
        slug: slug,
        logo: createOrganizationDto.logo,
        createdBy: superadminId,
      },
    });

    return {
      message: 'Organization created successfully',
      organization,
    };
  }

  async createOrganizationAdmin(
    createOrganizationAdminDto: CreateOrganizationAdminDto,
  ) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: createOrganizationAdminDto.organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const admin = await this.prismaService.user.create({
      data: {
        email: createOrganizationAdminDto.email,
        role: Role.ADMIN,
        organizationId: createOrganizationAdminDto.organizationId,
      },
    });

    const accessToken = await this.jwtService.signAsync(
      {
        email: admin.email,
        organizationId: organization.id,
        type: 'org-admin-invite',
      },
      {
        secret: this.configService.get('auth.accessTokenKey.secret'),
        expiresIn: '5m',
      },
    );

    await this.emailService.magicLinkEmailToOrgAdmin(
      admin.email,
      organization.name,
      `${process.env.FRONTEND_URL}/onboarding?token=${accessToken}`,
    );

    return {
      message: 'Organization admin created successfully',
      admin,
    };
  }

  async listOrganizations(paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;

    const [organizations, total] = await Promise.all([
      this.prismaService.organization.findMany({
        select: {
          id: true,
          name: true,
          description: true,
          slug: true,
          logo: true,
          createdAt: true,
          updatedAt: true,
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          users: {
            where: {
              role: Role.ADMIN,
            },
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              createdAt: true,
            },
            take: 1,
          },
          teams: {
            select: {
              id: true,
              name: true,
              description: true,
              createdAt: true,
            },
          },
          KnowledgeBase: {
            select: {
              id: true,
              name: true,
              status: true,
              createdAt: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prismaService.organization.count(),
    ]);

    const transformedData = organizations.map((org) => ({
      ...org,
      admin: org.users[0] || null,
      users: undefined, 
    }));

    return {
      data: transformedData,
      metadata: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async listSupportQueries(paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;

    const [supportQueries, total] = await Promise.all([
      this.prismaService.support.findMany({
        select: {
          id: true,
          email: true,
          message: true,
          area: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prismaService.support.count(),
    ]);

    return {
      data: supportQueries,
      metadata: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async listFeedbackQueries(paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;

    const [feedbackQueries, total] = await Promise.all([
      this.prismaService.feedback.findMany({
        select: {
          id: true,
          email: true,
          rating: true,
          area: true,
          message: true,
          createdAt: true,
          updatedAt: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prismaService.feedback.count(),
    ]);

    return {
      data: feedbackQueries,
      metadata: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async updateOrganizationAdmin(updateOrgAdminDto: UpdateOrgAdminDto) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: updateOrgAdminDto.organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const currentAdmin = await this.prismaService.user.findFirst({
      where: { 
        organizationId: updateOrgAdminDto.organizationId,
        role: Role.ADMIN 
      },
    });

    const existingUser = await this.prismaService.user.findUnique({
      where: { email: updateOrgAdminDto.email },
    });

    let admin = await this.prismaService.$transaction(async (prisma) => {
      if (currentAdmin && currentAdmin.email !== updateOrgAdminDto.email) {
        await prisma.user.update({
          where: { id: currentAdmin.id },
          data: { role: Role.USER },
        });
      }

      if (existingUser) {
        return prisma.user.update({
          where: { email: updateOrgAdminDto.email },
          data: {
            role: Role.ADMIN,
            organizationId: updateOrgAdminDto.organizationId,
          },
        });
      } else {
        return prisma.user.create({
          data: {
            email: updateOrgAdminDto.email,
            role: Role.ADMIN,
            organizationId: updateOrgAdminDto.organizationId,
          },
        });
      }
    });

    const accessToken = await this.jwtService.signAsync(
      {
        email: admin.email,
        organizationId: organization.id,
        type: 'org-admin-invite',
      },
      {
        secret: this.configService.get('auth.accessTokenKey.secret'),
        expiresIn: '5m',
      },
    );

    // Send invitation email
    await this.emailService.magicLinkEmailToOrgAdmin(
      admin.email,
      organization.name,
      `${process.env.FRONTEND_URL}/onboarding?token=${accessToken}`,
    );

    return {
      message: 'Organization admin updated successfully',
      admin,
    };
  }
}
