import React from 'react'
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON> } from 'recharts'

const data = [
    { day: 'Mon', users: 120 },
    { day: 'Tue', users: 98 },
    { day: 'Wed', users: 150 },
    { day: 'Thu', users: 80 },
    { day: 'Fri', users: 170 },
    { day: 'Sat', users: 200 },
    { day: 'Sun', users: 90 },
]

const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
        return (
            <div className="bg-white/90 border border-gray-200 rounded-lg px-4 py-2 shadow-lg">
                <p className="text-sm font-semibold text-gray-800">{label}</p>
                <p className="text-xs text-blue-600">Users: <span className="font-bold">{payload[0].value}</span></p>
            </div>
        )
    }
    return null
}

const analytics = () => {
    return (
        <div className="relative w-full mx-auto my-8">
            <div className="filter blur-sm pointer-events-none select-none">
                <ResponsiveContainer width="100%" height={320}>
                    <LineChart data={data} margin={{ top: 30, right: 30, left: 0, bottom: 10 }}>
                        <defs>
                            <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="0%" stopColor="#6366f1" stopOpacity={0.8} />
                                <stop offset="100%" stopColor="#a5b4fc" stopOpacity={0.2} />
                            </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="4 4" stroke="#e5e7eb" />
                        <XAxis dataKey="day" className="text-xs" tick={{ fill: '#64748b', fontWeight: 500 }} />
                        <YAxis className="text-xs" tick={{ fill: '#64748b', fontWeight: 500 }} />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend verticalAlign="top" height={36} iconType="circle" wrapperStyle={{ color: '#6366f1', fontWeight: 600, fontSize: '14px' }} />
                        <Line
                            type="monotone"
                            dataKey="users"
                            stroke="url(#colorUsers)"
                            strokeWidth={4}
                            dot={{ r: 8, fill: '#6366f1', stroke: '#fff', strokeWidth: 3, filter: 'drop-shadow(0 2px 6px #6366f155)' }}
                            activeDot={{ r: 12, fill: '#6366f1', stroke: '#fff', strokeWidth: 4 }}
                            name="Active Users"
                        />
                    </LineChart>
                </ResponsiveContainer>
            </div>
            <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                <div className="px-8 py-6 rounded-2xl shadow-lg  backdrop-blur-md text-center">
                    <span className="block text-2xl md:text-3xl font-bold text-gray-200 mb-2 font-sans">
                        Analytics Coming Soon
                    </span>
                    <span className="block text-base text-gray-500 font-normal font-sans">
                        Track daily engagement and usage trends here.
                    </span>
                </div>
            </div>
        </div>
    )
}

export default analytics
