'use client'

import { createContext, useContext, ReactNode } from 'react'
import { useSession, SessionProvider } from 'next-auth/react'

interface UserContextType {
    user: {
        id: string | null
        name: string | null
        email: string | null
        image?: string | null
        hasOnboarded?: boolean
    }
    isLoading: boolean
}

const UserContext = createContext<UserContextType>({
    user: { id: null, name: null, email: null },
    isLoading: true
})

export function UserProvider({ children }: { children: ReactNode }) {
    return (
        <SessionProvider>
            <UserContextWrapper>{children}</UserContextWrapper>
        </SessionProvider>
    )
}

function UserContextWrapper({ children }: { children: ReactNode }) {
    const { data: session, status } = useSession()

    const value = {
        user: {
            id: session?.user?.id || null,
            name: session?.user?.name || null,
            email: session?.user?.email || null,
            image: session?.user?.image || null,
            hasOnboarded: session?.user?.hasOnboarded || false
        },
        isLoading: status === 'loading'
    }

    return (
        <UserContext.Provider value={value}>
            {children}
        </UserContext.Provider>
    )
}

export const useUser = () => useContext(UserContext) 