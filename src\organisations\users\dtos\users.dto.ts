import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString } from 'class-validator';

export class AddUserDto {
  @ApiProperty({ description: 'User email' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'User password' })
  @IsString()
  password: string;

  @ApiProperty({ description: 'User name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'User organization id' })
  @IsString()
  organizationId: string;
}

export class VerifyAdminDto {
  @ApiProperty({
    description: 'JWT token for admin verification',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  token: string;
}

export class UpdateAdminDto {
  @ApiProperty({ description: 'Admin ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Organization ID' })
  @IsString()
  organizationId: string;

  @ApiProperty({ description: 'Admin Name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Admin password' })
  @IsString()
  password: string;
}
