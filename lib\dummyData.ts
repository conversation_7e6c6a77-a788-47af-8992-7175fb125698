import { ChatRequest, ChatResponse, UserMessage, AssistantMessage } from "@/types/chat";

const userRequest: UserMessage = {
    role: "user",
    message: "Tell me about knowledge bases",
    chat_id: "123",
    timestamp: new Date().toISOString(),
    sources: {
        allowed_kids: [
            "Sales Documentation",
            "Product Manual",
            "Technical Specs",
            "User Guide 2023",
            "API Documentation",
            "Release Notes",
            "Training Materials",
            "Customer FAQs",
            "Internal Policies",
            "Security Guidelines",
            "Best Practices",
            "Troubleshooting Guide",
            "Implementation Guide",
            "System Architecture",
            "Database Schema",
            "Code Documentation",
            "Testing Procedures",
            "Deployment Guide",
            "Maintenance Manual",
            "Integration Guide",
            "User Stories",
            "Requirements Doc",
            "Design Specs",
            "QA Guidelines",
            "Support Docs",
            "Workflow Guide",
            "Configuration Guide",
            "Performance Docs",
            "Backup Procedures",
            "Recovery Guide"
        ],
        allowed_tools: [
            "GoogleDrive",
            "OneDrive",
            "Dropbox",
            "SharePoint"
        ],
        allowed_data_sources: [
            "Knowledge Base Tables",
            "Knowledge Base Documents"
        ]
    },
    user_id: "user123"
};

const assistantResponse: AssistantMessage = {
    role: "assistant",
    message: "Here's what I found about knowledge bases...",
    chat_id: "123",
    timestamp: new Date().toISOString(),
    sources: [
        {
            source: {
                tool_name: "Knowledge Base Documents",
                sources: [
                    {
                        name: "Technical_Documentation.pdf",
                        type: "Knowledge Base Document",
                        dataset_uid: "doc123",
                        content: "Comprehensive technical documentation about system architecture",
                        score: 0.95
                    },
                    {
                        name: "User_Guide.pdf",
                        type: "Knowledge Base Document",
                        dataset_uid: "doc456",
                        content: "Step-by-step guide for users",
                        score: 0.85
                    },
                    {
                        name: "API_Reference.pdf",
                        type: "Knowledge Base Document",
                        dataset_uid: "doc789",
                        content: "Complete API documentation with examples",
                        score: 0.92
                    }
                ],
                type: "Retrieval"
            },
            timestamp: new Date().toISOString()
        },
        // {
        //     source: {
        //         tool_name: "Knowledge Base Tables",
        //         sources: [
        //             {
        //                 name: "System_Metrics",
        //                 type: "Knowledge Base Table",
        //                 dataset_uid: "table123",
        //                 content: "Performance metrics and statistics",
        //                 score: 0.88
        //             },
        //             {
        //                 name: "User_Analytics",
        //                 type: "Knowledge Base Table",
        //                 dataset_uid: "table456",
        //                 content: "User behavior analytics data",
        //                 score: 0.82
        //             }
        //         ],
        //         type: "Retrieval"
        //     },
        //     timestamp: new Date().toISOString()
        // }
    ]
};

export const dummyMessage = {
    ...userRequest,
    ...assistantResponse
}; 