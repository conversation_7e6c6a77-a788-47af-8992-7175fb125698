"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@steps-ai/ui'
import { Button } from '@steps-ai/ui'
import { useApiMutation, createMutationFn, queryClient } from '@/lib/apiClient';
import React, { useState } from 'react'

const DeepDive = ({ etls, userConnectorId }: { etls: any, userConnectorId: string }) => {

    return (
        <div>
            <Card className="bg-background/5 w-full">
                <CardHeader>
                    <CardTitle>Deep Dive</CardTitle>
                    <CardDescription>This section is coming soon. Stay tuned for updates!</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="text-center text-muted-foreground py-4">Deep Dive will be available in a future release.</div>
                </CardContent>
            </Card>
        </div>
    )
}

export default DeepDive
