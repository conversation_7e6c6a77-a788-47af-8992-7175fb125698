import { redirect } from "next/navigation";
import { getSession } from "@/lib/auth";
import AuthClient from "@/app/auth/authClient";
import type { Metadata } from "next";

export const metadata: Metadata = {
    title: "Get Started",
};

export default async function AuthenticationPage() {
    const session = await getSession();
    if (session) {
        if (session.user.organizationId && session.user.organization) {
            redirect("/organization");
        } else {
            redirect("/chat");
        }
    }

    return (<AuthClient />);
}