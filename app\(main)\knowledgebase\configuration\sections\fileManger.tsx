"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
    Search,
    Filter,
    TextSearch,
    MoreVertical,
    FileText,
    FileSpreadsheet,
    File,
    ChevronLeft,
    ChevronRight,
} from "lucide-react";
import { Button } from "@steps-ai/ui";
import { motion, Variants } from "framer-motion";
import { Input } from "@steps-ai/ui";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { formatDate, formatFileSize, getFileTypeAndColor } from "@/lib/utils";
import { ScrollArea } from "@steps-ai/ui";
import { useSearchParams, useRouter } from "next/navigation";
import UploadData from "@/components/Modal/UploadData";
import { Skeleton } from "@steps-ai/ui";
import {
    useApiMutation,
    createMutationFn,
    queryClient
} from "@/lib/apiClient";
import { toast } from "sonner"
// import PDFViewer from "@/components/PDFViewer/PDFViewer";
import Image from "next/image";
import debounce from 'lodash/debounce';


export interface FileItem {
    id: string;
    name: string;
    size: string;
    type: string;
    uploadedBy: {
        name: string;
        email: string;
        avatar: string;
    };
    lastModified: string;
}

export type FileType = "all" | "documents" | "spreadsheets" | "pdfs" | "images";
const container: Variants = {
    hidden: { opacity: 0 },
    show: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.3,
        },
    },
};

const item: Variants = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 },
};

export default function FileManager({
    data,
    isLoading,
    search,
    setSearch,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
}: {
    data?: any;
    isLoading?: boolean;
    search: string;
    setSearch: (search: string) => void;
    sortBy: string;
    setSortBy: (sortBy: string) => void;
    sortOrder: "asc" | "desc";
    setSortOrder: (sortOrder: "asc" | "desc") => void;
}) {
    const searchParams = useSearchParams();
    const id = searchParams.get("id");
    const [recentFiles, setRecentFiles] = useState([]);
    const [files, setFiles] = useState([]);
    const [filteredFiles, setFilteredFiles] = useState([]);
    const [loading, setLoading] = useState(false)
    const [selectedFile, setSelectedFile] = useState<string | null>(null);
    const router = useRouter();
    const debouncedSearch = useCallback(
        debounce((value: string) => {
            setSearch(value);
        }, 500),
        []
    );

    useEffect(() => {
        return () => {
            debouncedSearch.cancel();
        };
    }, [debouncedSearch]);

    useEffect(() => {
        if (!id) {
            router.push("/knowledgebase");
            toast.error("Knowledge base ID is required");
        }
    }, [id, router]);

    const deleteFile = useApiMutation(
        createMutationFn.delete('/files'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['files'] });
                    queryClient.invalidateQueries({ queryKey: ['knowledgebasedata'] });
                    toast.success("File deleted successfully");
                    setLoading(false)
                } else {
                    setLoading(false)
                    toast.error("Something went wrong. Please try again.");
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again.";
                    toast.error(errorMessage);
                    setLoading(false)
                } else if (error.request) {
                    toast.error("No response received from server");
                    setLoading(false)
                } else {
                    toast.error("Error setting up the request");
                    setLoading(false)
                }
            }
        }
    );

    useEffect(() => {
        if (data?.result?.files) {
            const allFiles = data.result.files;
            setRecentFiles(allFiles.slice(0, 3));
            setFiles(allFiles);
            setFilteredFiles(allFiles);
        }
    }, [data]);

    const handleDelete = (fileId: string, fileUrl: string) => {
        toast('Are you sure?', {
            action: {
                label: 'yes',
                onClick: () => {
                    setLoading(true)
                    deleteFile.mutate({ id: fileId, url: fileUrl });
                }
            },
            closeButton: true
        })
    };

    const handleFileClick = (fileUrl: string, fileName: string) => {
        window.open(fileUrl, '_blank');
    };

    const handleSort = (field: string) => {
        if (sortBy === field) {
            setSortOrder(sortOrder === "asc" ? "desc" : "asc");
        } else {
            setSortBy(field);
            setSortOrder("asc");
        }
    };

    const handleSearch = (value: string) => {
        const input = document.querySelector('input[placeholder="Search files..."]') as HTMLInputElement;
        if (input) {
            input.value = value;
        }
        debouncedSearch(value);
    };

    const RecentFilesSkeleton = () => (
        <div className="grid gap-4 grid-cols-3 mb-6">
            {[1, 2, 3].map((i) => (
                <div
                    key={i}
                    className="flex items-start space-x-4 rounded-lg border p-3"
                >
                    <Skeleton className="h-6 w-6 rounded bg-gray-200 dark:bg-gray-700" />
                    <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700" />
                        <Skeleton className="h-3 w-1/2 bg-gray-200 dark:bg-gray-700" />
                    </div>
                </div>
            ))}
        </div>
    );

    const FileListSkeleton = () => (
        <div className="border rounded-lg">
            <div className="grid grid-cols-12 gap-4 p-4 text-sm font-medium text-muted-foreground border-b">
                <div className="col-span-6">File name</div>
                <div className="col-span-3">Uploaded by</div>
                <div className="col-span-2">Last modified</div>
                <div className="col-span-1"></div>
            </div>
            <ScrollArea className="max-h-[45vh] overflow-y-auto scroll-smooth">
                {[1, 2, 3, 4, 5].map((i) => (
                    <div
                        key={i}
                        className="grid grid-cols-12 gap-4 p-2 items-center hover:bg-muted/50"
                    >
                        <div className="col-span-6 flex items-center space-x-4">
                            <Skeleton className="h-4 w-4 rounded bg-gray-200 dark:bg-gray-700" />
                            <div className="space-y-2 flex-1">
                                <Skeleton className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700" />
                                <Skeleton className="h-3 w-1/2 bg-gray-200 dark:bg-gray-700" />
                            </div>
                        </div>
                        <div className="col-span-3 flex items-center space-x-2">
                            <Skeleton className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700" />
                            <div className="space-y-2 flex-1">
                                <Skeleton className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700" />
                                <Skeleton className="h-3 w-1/2 bg-gray-200 dark:bg-gray-700" />
                            </div>
                        </div>
                        <div className="col-span-2">
                            <Skeleton className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700" />
                        </div>
                        <div className="col-span-1">
                            <Skeleton className="h-8 w-8 rounded bg-gray-200 dark:bg-gray-700" />
                        </div>
                    </div>
                ))}
            </ScrollArea>
        </div>
    );

    return (
        <div className="space-y-8">
            <div>
                <div className="w-full flex flex-col gap-2">
                    <h2 className="text-gray-500 font-bold">Recently added</h2>
                    {isLoading ? (
                        <RecentFilesSkeleton />
                    ) : (
                        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 mb-6">
                            {recentFiles.length > 0 ? (
                                recentFiles.map((file: any) => {
                                    const { color, type } = getFileTypeAndColor(file.mimeType);
                                    return (
                                        <div
                                            key={file.id}
                                            className="flex items-start space-x-4 rounded-lg border p-3 hover:shadow-md transition-all duration-200"
                                        >
                                            <FileText className={`h-6 w-6 ${color} flex-shrink-0`} />
                                            <div
                                                onClick={() => handleFileClick(file.url, file.name)}
                                                className="space-y-1 cursor-pointer overflow-hidden"
                                            >
                                                <h3 className="font-medium leading-none truncate cursor-pointer">{file.name}</h3>
                                                <p className="text-sm text-muted-foreground">
                                                    {formatFileSize(file.size)} • .{type}
                                                </p>
                                            </div>
                                        </div>
                                    );
                                })
                            ) : (
                                <span className="text-sm text-muted-foreground">{`No recent files found :)`}</span>
                            )}
                        </div>
                    )}

                    <h2 className="text-gray-500 font-bold text-md">Files</h2>
                    <div className="flex items-center gap-2 mb-2">
                        <div className="relative flex-1">
                            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Search files..."
                                className="pl-8 w-full"
                                defaultValue={search}
                                onChange={(e) => handleSearch(e.target.value)}
                            />
                        </div>
                        <div className="flex-shrink-0">
                            {id && <UploadData kid={id} />}
                        </div>
                    </div>

                    {isLoading ? (
                        <FileListSkeleton />
                    ) : (
                        <ScrollArea className="max-h-[45vh] overflow-y-auto scroll-smooth">
                            <div className="border rounded-lg overflow-hidden">
                                <div className="overflow-x-auto">
                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-muted/50">
                                                <TableHead
                                                    className="cursor-pointer hover:bg-muted/80 transition-colors"
                                                    onClick={() => handleSort("name")}
                                                >
                                                    <div className="flex items-center space-x-2">
                                                        <span>File name</span>
                                                        {sortBy === "name" && (
                                                            <span className="text-primary">{sortOrder === "asc" ? "↑" : "↓"}</span>
                                                        )}
                                                    </div>
                                                </TableHead>
                                                <TableHead className="hidden sm:table-cell">Uploaded by</TableHead>
                                                <TableHead
                                                    className="cursor-pointer hover:bg-muted/80 transition-colors hidden sm:table-cell"
                                                    onClick={() => handleSort("updatedAt")}
                                                >
                                                    <div className="flex items-center space-x-2">
                                                        <span>Last modified</span>
                                                        {sortBy === "updatedAt" && (
                                                            <span className="text-primary">{sortOrder === "asc" ? "↑" : "↓"}</span>
                                                        )}
                                                    </div>
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {filteredFiles.length > 0 ? (
                                                filteredFiles.map((file: any) => {
                                                    const { type, color } = getFileTypeAndColor(file.mimeType);
                                                    return (
                                                        <TableRow
                                                            key={file.id}
                                                            className="cursor-pointer hover:bg-muted/50 transition-colors"
                                                            onClick={() => handleFileClick(file.url, file.name)}
                                                        >
                                                            <TableCell className="py-3">
                                                                <div className="flex items-center space-x-4">
                                                                    <FileText className={`h-5 w-5 ${color} flex-shrink-0`} />
                                                                    <div>
                                                                        <div className="font-medium">
                                                                            {file.name}
                                                                        </div>
                                                                        <div className="text-xs text-muted-foreground">
                                                                            {formatFileSize(file.size)} • .{type}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </TableCell>
                                                            <TableCell className="hidden sm:table-cell py-3">
                                                                <div className="flex items-center space-x-3">
                                                                    {file.user?.profileImageUrl ? (
                                                                        <Image
                                                                            src={file.user.profileImageUrl}
                                                                            alt={`${file.user.name}'s avatar`}
                                                                            width={20}
                                                                            height={20}
                                                                            className="rounded-full object-cover flex-shrink-0"
                                                                            onError={(e) => {
                                                                                const target = e.target as HTMLImageElement;
                                                                                target.src = '/ai-logo.svg';
                                                                            }}
                                                                        />
                                                                    ) : (
                                                                        <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center">
                                                                            <span className="text-xs text-primary">
                                                                                {file.user?.name?.charAt(0) || '?'}
                                                                            </span>
                                                                        </div>
                                                                    )}
                                                                    <div className="overflow-hidden">
                                                                        <div className="text-sm font-medium truncate">
                                                                            {file.user?.name || 'Unknown User'}
                                                                        </div>
                                                                        <div className="text-xs text-muted-foreground truncate">
                                                                            {file.user?.email || 'No email'}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </TableCell>
                                                            <TableCell className="hidden sm:table-cell py-3">
                                                                <div className="text-sm text-muted-foreground">
                                                                    {formatDate(file.updatedAt)}
                                                                </div>
                                                            </TableCell>
                                                        </TableRow>
                                                    );
                                                })
                                            ) : files.length === 0 ? (
                                                <TableRow>
                                                    <TableCell colSpan={3}>
                                                        <div className="p-4 text-center mx-auto max-w-md mt-4">
                                                            <div className="flex justify-center mb-6">
                                                                <div className="bg-blue-100 p-4 rounded-full">
                                                                    <FileText className="w-12 h-12 text-blue-600" />
                                                                </div>
                                                            </div>
                                                            <h2 className="text-2xl font-bold text-text-primary mb-4">
                                                                Your knowledgebase is Empty
                                                            </h2>
                                                            <p className="text-text-secondary mb-6">
                                                                Start by uploading files to your knowledgebase. Files are the source of
                                                                truth for your team.
                                                            </p>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                <TableRow>
                                                    <TableCell colSpan={3}>
                                                        <div className="p-4 text-center mx-auto max-w-md mt-4">
                                                            <div className="flex justify-center mb-6">
                                                                <div className="bg-orange-100 p-4 rounded-full">
                                                                    <TextSearch className="w-12 h-12 text-orange-600" />
                                                                </div>
                                                            </div>
                                                            <h2 className="text-2xl font-bold text-text-primary mb-4">
                                                                Oops !! No files found
                                                            </h2>
                                                            <p className="text-text-secondary mb-6">
                                                                Either the file is deleted or you have entered the wrong file name.
                                                            </p>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>
                        </ScrollArea>
                    )}
                </div>
            </div>
            {/* {selectedFile && (
                <PDFViewer
                    fileUrl={selectedFile}
                    onClose={() => setSelectedFile(null)}
                />
            )} */}
        </div>
    );
}