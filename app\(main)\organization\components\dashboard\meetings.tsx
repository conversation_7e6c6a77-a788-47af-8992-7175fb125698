'use client'

import { useState, useEffect } from "react"
import { Calendar, ExternalLink, ChevronRight, CheckCircle, MessageSquare, CalendarDays, UserCheck, <PERSON>rkles } from "lucide-react"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { ModernTimeline, type TimelineStep } from "@/components/ui/modern-timeline"
import { assets } from "@/lib/assets"
import Image from "next/image"
import { useUser } from "@/contexts/UserContext"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { organizationApi, type OnboardingStatusResponse, type Meeting } from "@/lib/api/organization"

export default function Meetings() {
  const { user } = useUser();
  const router = useRouter();
  const [onboardingData, setOnboardingData] = useState<OnboardingStatusResponse['result'] | null>(null);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      fetchOnboardingStatus();
    }
  }, [user?.id]);
  const fetchOnboardingStatus = async () => {
    try {
      setIsLoading(true);
      const data = await organizationApi.getOnboardingStatus(user.id!);
      console.log('Onboarding Data:', data);
      setOnboardingData(data.result);
      
      if (data.result.finalVerdict && data.result.upcomingMeetings) {
        const sortedMeetings = data.result.upcomingMeetings.sort((a, b) => 
          new Date(a.start).getTime() - new Date(b.start).getTime()
        );
        setMeetings(sortedMeetings);
      }
    } catch (error) {
      console.error('Error fetching onboarding status:', error);
      toast.error('Failed to load onboarding status');
    } finally {
      setIsLoading(false);
    }
  };
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const isToday = date.toDateString() === today.toDateString();
    const isTomorrow = date.toDateString() === tomorrow.toDateString();

    let dateLabel;
    if (isToday) {
      dateLabel = "Today";
    } else if (isTomorrow) {
      dateLabel = "Tomorrow";
    } else {
      dateLabel = date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
    }

    const timeLabel = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    return { dateLabel, timeLabel };
  };

  const getPlatformInfo = (meetingLink?: string) => {
    if (!meetingLink) return { platform: "Meeting", color: "bg-gray-500" };

    if (meetingLink.includes('zoom.us')) {
      return { platform: "Zoom", color: "bg-blue-500" };
    } else if (meetingLink.includes('meet.google.com')) {
      return { platform: "Google Meet", color: "bg-green-500" };
    } else if (meetingLink.includes('teams.microsoft.com')) {
      return { platform: "Teams", color: "bg-purple-500" };
    } else {
      return { platform: "Meeting", color: "bg-gray-500" };
    }  };  const OnboardingChecklist = () => {
    const onboardingSteps: TimelineStep[] = [
      {
        id: 1,
        title: "Complete Onboarding",
        description: "Finish the setup process to unlock all features",
        completed: onboardingData?.hasCompletedOnboarding || false,
        icon: UserCheck,
        route: "/onboarding",
        clickable: true,
        active: !onboardingData?.hasCompletedOnboarding,
      },
      {
        id: 2,
        title: "Create Your First Chat",
        description: "Start a conversation to begin using the platform",
        completed: onboardingData?.hasCreatedChat || false,
        icon: MessageSquare,
        route: "/chat",
        clickable: onboardingData?.hasCompletedOnboarding || false,
        active: onboardingData?.hasCompletedOnboarding && !onboardingData?.hasCreatedChat,
      },
      {
        id: 3,
        title: "Connect Your Calendar",
        description: "Link your Google Calendar to see upcoming meetings",
        completed: onboardingData?.hasConnectedCalendar || false,
        icon: CalendarDays,
        route: "/connector/calendar",
        clickable: onboardingData?.hasCreatedChat || false,
        active: onboardingData?.hasCreatedChat && !onboardingData?.hasConnectedCalendar,
      },
    ];const completedSteps = onboardingSteps.filter(step => step.completed).length;
    const progressPercentage = (completedSteps / onboardingSteps.length) * 100;    const handleStepClick = (step: TimelineStep) => {
      if (step.clickable && step.route) {
        router.push(step.route);
      }
    };return (
      <div className="py-4">
        {/* Enhanced Progress Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="space-y-1">
              <h3 className="text-base font-semibold text-foreground">Setup Progress</h3>
              <p className="text-sm text-muted-foreground">
                Get started with your workspace
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium text-primary">
                {Math.round(progressPercentage)}%
              </span>
            </div>
          </div>
            <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-muted-foreground">
              {completedSteps}/{onboardingSteps.length} Steps Complete
            </span>
            {completedSteps === onboardingSteps.length && (
              <div className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3 text-primary" />
                <span className="text-xs font-medium text-primary">All Done!</span>
              </div>
            )}
          </div>
          <div className="h-2 bg-muted/50 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-primary/80 to-primary rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        <ModernTimeline 
          steps={onboardingSteps}
          onStepClick={handleStepClick}
        />

        {/* Completion Celebration */}
        {completedSteps === onboardingSteps.length && (
          <div className="mt-6 p-4 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/20 rounded-full">
                <Sparkles className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h4 className="font-semibold text-foreground">All set! 🎉</h4>
                <p className="text-sm text-muted-foreground">
                  You've completed all onboarding steps. Enjoy using the platform!
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-md mx-auto border-none p-0">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-8 w-8 rounded" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between p-3 rounded-lg">
              <div className="flex items-start space-x-4">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-3 w-12" />
                </div>
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto border-none p-0">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">        <h2 className="text-lg font-semibold">
          {onboardingData?.finalVerdict ? 'My meetings' : 'Getting Started'}
        </h2>
        <Image src={assets.Icons["Google Calendar"]} alt="calendar" width={30} height={30} />
      </CardHeader>
      <CardContent className="space-y-4">        {onboardingData?.finalVerdict ? (
          // Show meetings if onboarding is complete
          <>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg">
                    <div className="flex items-start space-x-4">
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-16" />
                        <Skeleton className="h-3 w-12" />
                      </div>
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-20" />
                      </div>
                    </div>
                    <Skeleton className="h-4 w-4" />
                  </div>
                ))}
              </div>
            ) : meetings.length > 0 ? (
              <>                {meetings.map((meeting) => {
                  const { dateLabel, timeLabel } = formatDateTime(meeting.start);
                  const { platform, color } = getPlatformInfo();

                  return (
                    <div
                      key={meeting.id}
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer group"
                    >
                      <div className="flex items-start space-x-4">
                        <div className="text-sm text-muted-foreground min-w-0">
                          <div className="font-medium">{dateLabel}</div>
                          <div>{timeLabel}</div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-sm mb-1">{meeting.summary}</div>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${color}`} />
                            <span className="text-xs text-muted-foreground">{platform}</span>
                          </div>
                          {meeting.location && (
                            <div className="text-xs text-muted-foreground mt-1">
                              📍 {meeting.location}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                  );
                })}
                <Button
                  variant="ghost"
                  className="w-full justify-between text-sm text-muted-foreground hover:text-foreground mt-4"
                >
                  See all meetings
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <div className="text-center py-8">
                <div className="flex flex-col items-center gap-3">
                  <div className="p-3 bg-muted/50 rounded-full">
                    <Calendar className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="font-medium text-muted-foreground">No upcoming meetings</p>
                    <p className="text-sm text-muted-foreground">
                      You have no meetings scheduled for today and tomorrow.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <OnboardingChecklist />
        )}
      </CardContent>
    </Card>
  )
}
