// import { AirflowPublishDto } from '../dtos/airflow.dto';
// import {
//   EtlProviderStrategy,
//   KnowledgeBasePublishArgs,
// } from '../interface/etl.interface';
// import { EtlStatus, ConnectorName } from '@prisma/client';

// export class AirflowEtlProvider implements EtlProviderStrategy {
//   private readonly baseUrl: string;
//   private readonly headers: HeadersInit;

//   constructor(username: string, password: string, baseUrl: string) {
//     this.baseUrl = baseUrl;
//     this.headers = {
//       'Content-Type': 'application/json',
//       Authorization: `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`,
//     };
//   }

//   async publish(
//     user_id: string,
//     connector_name: ConnectorName,
//     kid: string,
//     knowledge_base_data?: KnowledgeBasePublishArgs,
//   ): Promise<AirflowPublishDto> {
//     const response = await fetch(
//       `${this.baseUrl}/dags/generic_user_connector_etl_jobs/dagRuns`,
//       {
//         method: 'POST',
//         headers: {
//           ...this.headers,
//         },
//         body: JSON.stringify({
//           conf: {
//             user_id: user_id,
//             kid: kid,
//             connector_name: connector_name,
//             ...(knowledge_base_data && {
//               knowledge_base_data: JSON.stringify(knowledge_base_data),
//             }),
//           },
//         }),
//       },
//     );

//     const data = await response.json();
//     const airflowResponse: AirflowPublishDto = {
//       dagId: connector_name,
//       dagRunId: data.dag_run_id,
//       state: EtlStatus.QUEUED,
//       executionDate: data.execution_date,
//       endDate: data.end_date,
//     };
//     return airflowResponse;
//   }

//   async getStatus(dagRunId: string) {
//     const response = await fetch(
//       `${this.baseUrl}/dags/generic_user_connector_etl/dagRuns/${dagRunId}`,
//       {
//         method: 'GET',
//         headers: {
//           ...this.headers,
//         },
//       },
//     );
//     const data = await response.json();
//     return data.state;
//   }
// }
