import {
  Controller,
  Post,
  Body,
  Get,
  HttpCode,
  HttpStatus,
  Param,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthService } from './auth.v1.service';
import { SignUpDto } from '../dtos/signup.dto';
import { LoginDto } from '../dtos/login.dto';
import { GoogleDto } from '../dtos/social.dto';
import { RefreshDto } from '../dtos/refresh.dto';
import { RequestPasswordResetDto, VerifyResetTokenDto, ResetPasswordDto } from '../dtos/reset-password.dto';

@ApiTags('Auth-V1')
@Controller({ version: '1', path: 'auth' })
export class AuthController {
  constructor(private authService: AuthService) { }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() credentials: LoginDto) {
    const user = await this.authService.loginWithCredentials(credentials);
    const token = await this.authService.generateTokens(user);
    return { user, token };
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async register(@Body() registrationData: SignUpDto) {
    const user =
      await this.authService.registerWithCredentials(registrationData);
    const token = await this.authService.generateTokens(user);
    return { user, token };
  }

  @Post('google')
  @HttpCode(HttpStatus.OK)
  async googleLogin(@Body() body: GoogleDto) {
    const user = await this.authService.googleAuthenticate(body);
    const token = await this.authService.generateTokens(user);
    return { user, token };
  }

  @Get('checktwofa/:email')
  @HttpCode(HttpStatus.OK)
  async checkMfa(@Param('email') email: string) {
    const result = await this.authService.checkMfa(email);
    return result;
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refresh(@Body() data: RefreshDto) {
    const result = await this.authService.refreshTokens(data);
    return result;
  }

  @Post('password-reset/request')
  @HttpCode(HttpStatus.OK)
  async requestPasswordReset(@Body() data: RequestPasswordResetDto) {
    await this.authService.sendResetPasswordEmail(data.email);
    return { message: 'If the email exists, a reset link has been sent' };
  }

  @Post('password-reset/verify-token')
  @HttpCode(HttpStatus.OK)
  async verifyResetToken(@Body() data: VerifyResetTokenDto) {
    const result = await this.authService.verifyResetPasswordToken(data.token);
    return result;
  }

  @Post('password-reset/reset')
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() data: ResetPasswordDto) {
    const { userId } = await this.authService.verifyResetPasswordToken(data.token);
    await this.authService.resetPassword(userId, data.newPassword);
    return { message: 'Password has been reset successfully' };
  }
  
}
