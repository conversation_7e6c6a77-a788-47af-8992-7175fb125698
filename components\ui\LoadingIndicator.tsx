import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const loadingStates = [
  {
    title: "Initializing AI Assistant",
    description: "Setting up your personalized experience"
  },
  {
    title: "Processing Request",
    description: "Analyzing your query for optimal results"
  },
  {
    title: "Gathering Knowledge",
    description: "Accessing selected data sources"
  },
  {
    title: "Almost Ready",
    description: "Finalizing response generation"
  }
];

const LoadingIndicator = ({ isVisible = true }) => {
  const [currentState, setCurrentState] = useState(0);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (isVisible) {
      const stateInterval = setInterval(() => {
        setCurrentState((prev) => (prev + 1) % loadingStates.length);
      }, 3000);

      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) return 0;
          return prev + 1;
        });
      }, 50);

      return () => {
        clearInterval(stateInterval);
        clearInterval(progressInterval);
      };
    }
  }, [isVisible]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className="fixed inset-0 flex items-center justify-center z-50 bg-background/80 backdrop-blur-sm"
        >
          <div className="w-full max-w-md px-6">
            <motion.div 
              className="space-y-8 text-center"
              animate={{
                scale: [1, 1.002, 1],
                transition: { duration: 2, repeat: Infinity }
              }}
            >
              <motion.div
                key={currentState}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-2"
              >
                <h3 className="text-2xl font-semibold text-foreground">
                  {loadingStates[currentState].title}
                </h3>
                <p className="text-base text-muted-foreground">
                  {loadingStates[currentState].description}
                </p>
              </motion.div>

              <div className="space-y-3">
                {/* Main Progress Bar */}
                <div className="h-1 w-full bg-blue-100 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-blue-500"
                    style={{ width: `${progress}%` }}
                    animate={{
                      opacity: [0.8, 1, 0.8],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                    }}
                  />
                </div>

                {/* Indeterminate Progress Bar */}
                <div className="h-0.5 w-full bg-blue-50 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-blue-400"
                    animate={{
                      x: ['-100%', '100%'],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: 'linear',
                    }}
                    style={{ width: '50%' }}
                  />
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadingIndicator;