"use server";

import { redis } from "./redisClient";

interface QuotaData {
    quota: number;
    lastMessage: number;
    remaining: number;
}

async function getUserQuota(): Promise<{ quota: number }> {
    return {
        quota: 10,
    };
}

export async function getQuota(userId: string): Promise<QuotaData | null> {
    try {
        console.log("Getting quota for userId", userId);
        if (!userId) return null;

        const key = `chat:${userId}`;
        let data;
        try {
            data = await redis.get(key);
        } catch (error) {
            console.error("Redis get error:", error);
            // Return a default quota if Redis fails
            const defaultQuota = await getUserQuota();
            return {
                quota: defaultQuota.quota,
                lastMessage: Date.now(),
                remaining: defaultQuota.quota
            };
        }

        if (!data) {
            const quotaInfo = await getUserQuota();
            console.log("New quota info:", quotaInfo);
            const newData: QuotaData = {
                quota: quotaInfo.quota,
                lastMessage: Date.now(),
                remaining: quotaInfo.quota
            };
            try {
                await redis.setex(key, 172800, JSON.stringify(newData));
            } catch (error) {
                console.error("Redis set error:", error);
                // Still return the data even if we couldn't save it
                return newData;
            }
            return newData;
        }

        const quotaData: QuotaData = JSON.parse(data);
        const lastMessageDate = new Date(quotaData.lastMessage);
        const now = new Date();

        if (now.getFullYear() !== lastMessageDate.getFullYear() ||
            now.getMonth() !== lastMessageDate.getMonth() ||
            now.getDate() !== lastMessageDate.getDate()) {
            const newQuota = await getUserQuota();
            const resetData: QuotaData = {
                quota: newQuota.quota,
                lastMessage: Date.now(),
                remaining: newQuota.quota
            };
            try {
                await redis.setex(key, 172800, JSON.stringify(resetData));
            } catch (error) {
                console.error("Redis set error on reset:", error);
                // Still return the reset data even if we couldn't save it
                return resetData;
            }
            return resetData;
        }

        return quotaData;
    } catch (error) {
        console.error("Quota check error:", error);
        // Return a default quota in case of any errors
        const defaultQuota = await getUserQuota();
        return {
            quota: defaultQuota.quota,
            lastMessage: Date.now(),
            remaining: defaultQuota.quota
        };
    }
}

export async function increment(userId: string): Promise<number | null> {
    try {
        if (!userId) return null;

        const key = `chat:${userId}`;
        let data;
        try {
            data = await redis.get(key);
        } catch (error) {
            console.error("Redis get error in increment:", error);
            return null;
        }
        if (!data) return null;

        const quotaData: QuotaData = JSON.parse(data);
        if (quotaData.remaining <= 0) return null;

        const updatedData: QuotaData = {
            ...quotaData,
            lastMessage: Date.now(),
            remaining: quotaData.remaining - 1
        };

        try {
            await redis.setex(key, 172800, JSON.stringify(updatedData));
        } catch (error) {
            console.error("Redis set error in increment:", error);
            // Still return the decremented value even if we couldn't save it
            return updatedData.remaining;
        }
        return updatedData.remaining;
    } catch (error) {
        console.error("Increment error:", error);
        return null;
    }
}
