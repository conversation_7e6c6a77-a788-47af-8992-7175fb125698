'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import AddUserModal from '../components/members/addUserModal'
import { useUser } from '@/contexts/UserContext'
import PendingInvites from '../components/members/pendingInvites'
import UserCards from '../components/members/userCards'
import UserProfile from '../components/members/userProfile'

export default function OrganizationMembersPage() {
    const [showInviteUser, setShowInviteUser] = useState(false);
    const { user, isAdmin } = useUser();
    return (
        <div className='flex flex-col gap-4 mt-10'>
            <div className="flex flex-row justify-between items-center gap-2 w-full">
                <div>
                    <h1 className="text-xl sm:text-2xl md:text-4xl font-semibold">
                        {isAdmin ? 'Organization Members' : 'Your Profile'}
                    </h1>
                    <p className="text-muted-foreground mt-1">
                        {isAdmin
                            ? "Manage your organization members and invitations."
                            : "View your profile and organization information."
                        }
                    </p>
                </div>
                {isAdmin && (
                    <Button
                        variant="outline"
                        className="flex items-center gap-2 px-4 py-2 rounded-full border-0 dark:bg-gray-800 dark:text-white"
                        aria-label="Add Member"
                        onClick={() => setShowInviteUser(true)}
                    >
                        <span className="font-bold">Add Member</span>
                    </Button>
                )}
            </div>

            {isAdmin && (
                <AddUserModal
                    isOpen={showInviteUser}
                    onClose={() => setShowInviteUser(false)}
                    organizationId={user.organizationId || ''}
                    onUserAdded={() => { }}
                />
            )}
            {isAdmin ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div className="col-span-1">
                        <UserCards organizationId={user.organizationId || ''} />
                    </div>
                    <div className="col-span-1">
                        <Separator className="lg:hidden mb-6" />
                        <PendingInvites organizationId={user.organizationId || ''} />
                    </div>                </div>
            ) : (
                <div className="max-w-4xl mx-auto">
                    <UserProfile />
                </div>
            )}
        </div>
    )
}




