const DYNAMO_TABLE_NAME = 'UserConnectors';

export interface IBaseCallbackResponse {
  success: boolean;
  message: string;
  data: any;
}

export interface IBasicAuthResponse {
  success: boolean;
  message: string;
  data: any;
}

export abstract class BaseConnectorService {
  protected readonly dynamoDBTableName = DYNAMO_TABLE_NAME;
  abstract generateRedirectUrl(
    userId: string,
  ): Promise<string | { url: string }>;
  abstract handleCallback(
    code: string,
    state: string,
  ): Promise<IBaseCallbackResponse>;
  abstract disconnect(
    userId: string,
  ): Promise<{ success: boolean; message: string }>;
}

export abstract class BasicAuthConnectorService {
  protected readonly dynamoDBTableName = DYNAMO_TABLE_NAME;
  abstract connect(
    userId: string,
    token: string,
    email: string,
    subdomain: string,
  ): Promise<IBasicAuthResponse>;
  abstract disconnect(
    userId: string,
  ): Promise<{ success: boolean; message: string }>;
}
