/*
  Warnings:

  - A unique constraint covering the columns `[userConnectorId]` on the table `Etl` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[teamConnectorId]` on the table `Etl` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[organizationConnectorId]` on the table `Etl` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[knowledgeBaseId]` on the table `Etl` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "EtlStatus" ADD VALUE 'NEW';
ALTER TYPE "EtlStatus" ADD VALUE 'UPLOADED';
ALTER TYPE "EtlStatus" ADD VALUE 'PROCESSING';
ALTER TYPE "EtlStatus" ADD VALUE 'ERROR';

-- CreateIndex
CREATE UNIQUE INDEX "Etl_userConnectorId_key" ON "Etl"("userConnectorId");

-- CreateIndex
CREATE UNIQUE INDEX "Etl_teamConnectorId_key" ON "Etl"("teamConnectorId");

-- CreateIndex
CREATE UNIQUE INDEX "Etl_organizationConnectorId_key" ON "Etl"("organizationConnectorId");

-- CreateIndex
CREATE UNIQUE INDEX "Etl_knowledgeBaseId_key" ON "Etl"("knowledgeBaseId");
