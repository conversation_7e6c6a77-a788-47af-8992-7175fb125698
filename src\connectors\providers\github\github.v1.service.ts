import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { ConnectorsService } from '../../v1/connectors.v1.service';
import { BaseConnectorService } from '../../interfaces/base.interface';

interface GitHubConnectionDetails {
  user: {
    id: string;
    login: string;
    name: string;
    email?: string;
    avatar_url?: string;
  };
  repositories: {
    total: number;
    repositories: Array<{
      id: number;
      name: string;
      full_name: string;
      private: boolean;
      description?: string;
      language?: string;
    }>;
  };
}

@Injectable()
export class GitHubService extends BaseConnectorService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
  ) {
    super();
  }

  async generateRedirectUrl(userId: string) {
    const scopes = [
      'repo',
      'read:user',
      'read:org',
      'repo:status',
      'issues',
      'pull_requests',
    ];

    const state = userId;

    const authUrl = `https://github.com/login/oauth/authorize?client_id=${this.configService.get(
      'github.clientId',
    )}&scope=${encodeURIComponent(
      scopes.join(' '),
    )}&redirect_uri=${encodeURIComponent(
      this.configService.get('github.redirectUri'),
    )}&state=${state}`;

    return authUrl;
  }

  async handleCallback(code: string, state: string) {
    try {
      // Exchange code for access token
      const tokenResponse = await fetch(
        'https://github.com/login/oauth/access_token',
        {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            client_id: this.configService.get('github.clientId'),
            client_secret: this.configService.get('github.clientSecret'),
            code,
            redirect_uri: this.configService.get('github.redirectUri'),
          }),
        },
      );

      const tokenData = await tokenResponse.json();
      if (tokenData.error) {
        throw new Error(tokenData.error_description);
      }

      const { access_token } = tokenData;
      console.log(access_token);
      const connectionDetails = await this.getConnectionDetails(access_token);

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'GitHub',
        },
        select: {
          id: true,
          type: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('GitHub connector not found');
      }

      await this.prisma.userConnectors.create({
        data: {
          userId: state,
          connectorId: connector.id,
          config: {
            name: connectionDetails.user.name,
            email: connectionDetails.user.email,
            image: connectionDetails.user.avatar_url,
            metadata: JSON.stringify(connectionDetails),
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(access_token);

      const userConnectorDetails = {
        user_id: state,
        connector_id: connector.id,
        connector_type: connector.type,
        name: 'GitHub',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        userConnectorDetails,
      );

      return {
        success: true,
        message: 'GitHub integration connected successfully',
        data: {
          name: connectionDetails.user.name,
          email: connectionDetails.user.email,
          image: connectionDetails.user.avatar_url,
          metadata: JSON.stringify(connectionDetails),
        },
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to process GitHub callback: ${error.message}`,
      );
    }
  }

  async disconnect(userId: string) {
    try {
      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'GitHub',
        },
        select: {
          id: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('GitHub connector not found');
      }

      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'GitHub',
          },
        },
      );

      const userConnector = userConnectors?.[0];
      if (!userConnector) {
        throw new NotFoundException(
          'No active GitHub connection found for this user',
        );
      }

      await this.prisma.userConnectors.deleteMany({
        where: {
          userId,
          connectorId: connector.id,
        },
      });

      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'GitHub',
      });

      return {
        success: true,
        message: 'Successfully disconnected GitHub integration',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to disconnect GitHub integration: ${error.message}`,
      );
    }
  }

  private async getConnectionDetails(
    accessToken: string,
  ): Promise<GitHubConnectionDetails> {
    try {
      // Fetch user details
      const userResponse = await fetch('https://api.github.com/user', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/vnd.github.v3+json',
        },
      });
      const userData = await userResponse.json();
      if (userData.message) throw new Error(userData.message);

      // Fetch repositories
      const reposResponse = await fetch(
        'https://api.github.com/user/repos?per_page=100',
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/vnd.github.v3+json',
          },
        },
      );
      const reposData = await reposResponse.json();
      if (reposData.message) throw new Error(reposData.message);

      return {
        user: {
          id: userData.id.toString(),
          login: userData.login,
          name: userData.name || userData.login,
          email: userData.email,
          avatar_url: userData.avatar_url,
        },
        repositories: {
          total: reposData.length,
          repositories: reposData.map((repo) => ({
            id: repo.id,
            name: repo.name,
            full_name: repo.full_name,
            private: repo.private,
            description: repo.description,
            language: repo.language,
          })),
        },
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to fetch GitHub connection details: ${error.message}`,
      );
    }
  }
}
