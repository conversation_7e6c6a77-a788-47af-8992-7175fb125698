"use client"

import React, { useState, useEffect } from 'react'
import {
    <PERSON><PERSON><PERSON><PERSON>b,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
} from "@/components/ui/breadcrumb"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Database, Moon, Star, Sun } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from "@/lib/utils"
import { usePathname, useParams } from 'next/navigation'
import { chatClient } from '@/lib/chatClient'
import { toast } from 'sonner'
import { Message, ChatSessionResponse } from '@/types/chat'
import useChatStore from '@/store/chatStore';
import { useTheme } from "next-themes";
import Link from 'next/link';

const shimmerAnimation = "animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:200%_100%] bg-clip-text";

const Header = () => {
    const pathname = usePathname();
    const isClientPage = pathname.includes('/chat/') && !pathname.endsWith('/chat');
    const isOrganizationPage = pathname.includes('/organization');
    const params = useParams();
    const { theme, setTheme } = useTheme();
    const { currentChat } = useChatStore();
    const [isStarred, setIsStarred] = useState(false);
    const [chatTitle, setChatTitle] = useState("");

    useEffect(() => {
        if (params.id) {
            setChatTitle(currentChat?.name || "");
            const starredState = localStorage.getItem(`chat-${params.id}-starred`);
            setIsStarred(starredState === 'true');
        }
    }, [params.id, currentChat]);

    const handleStar = async () => {
        try {
            if (params.id) {
                const newStarredState = !isStarred;

                const response = await chatClient.updateChat({
                    chat_id: params.id as string,
                    name: chatTitle,
                    starred: newStarredState
                });

                if (response.starred !== undefined) {
                    localStorage.setItem(`chat-${params.id}-starred`, response.starred.toString());
                    setIsStarred(response.starred);

                    toast.success(response.starred ? "Chat added to starred" : "Chat removed from starred");
                }
            }
        } catch (error) {
            console.error('Error starring chat:', error);
            toast.error("Failed to update star status");
        }
    };

    return (
        <header className="flex min-h-12 shrink-0 items-center justify-between px-4 ">


            {isOrganizationPage ?
                <div className="flex items-center justify-between w-full px-4 py-2">
                    <div className="flex items-center gap-2">
                        <SidebarTrigger className="-ml-1" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <h1 className="text-2xl font-bold tracking-tight">
                            Company
                        </h1>
                    </div>
                    <div className="space-y-4">
                        <div className="inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground">
                            {[
                                { label: 'Dashboard', href: '/organization' },
                                { label: 'People', href: '/organization/members' },
                                // { label: 'Teams', href: '/organization/teams' },
                                // { label: 'Connectors', href: '/organization/connectors' },
                                { label: 'Settings', href: '/organization/settings' },
                            ].map(tab => (
                                <Link key={tab.label} href={tab.href} passHref legacyBehavior>
                                    <a
                                        className={cn(
                                            "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                                            pathname === tab.href || (tab.href === '/organization' && pathname === '/organization')
                                                ? 'bg-background text-foreground shadow'
                                                : 'text-muted-foreground hover:text-foreground'
                                        )}
                                    >
                                        {tab.label}
                                    </a>
                                </Link>
                            ))}
                        </div>
                    </div>
                </div> : <>
                    <div className="flex items-center gap-2">
                        <SidebarTrigger className="-ml-1" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <Breadcrumb>
                            <BreadcrumbList>
                                <BreadcrumbItem className="hidden md:block">
                                    <BreadcrumbLink href="#">{chatTitle}</BreadcrumbLink>
                                </BreadcrumbItem>
                            </BreadcrumbList>
                        </Breadcrumb>
                    </div>
                    <div className="flex gap-2">
                        {isClientPage && (
                            <div className="flex gap-2">
                                <TooltipProvider delayDuration={100}>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={handleStar}
                                                className={cn(
                                                    "transition-all duration-200 relative overflow-hidden group",
                                                    isStarred
                                                        ? "text-yellow-400 hover:text-yellow-500"
                                                        : "text-muted-foreground hover:text-foreground"
                                                )}
                                            >
                                                <Star className={cn(
                                                    "h-5 w-5 relative z-10",
                                                    isStarred && "fill-current",
                                                    shimmerAnimation
                                                )} />
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent
                                            side="left"
                                            className="animate-in zoom-in-50 duration-100"
                                            sideOffset={5}
                                        >
                                            <p>{isStarred ? 'Remove from starred' : 'Add to starred chats'}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>

                            </div>
                        )}
                        <Button variant="ghost" size="icon" onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
                            {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
                        </Button>
                    </div   >
                </>}


        </header>
    )
}

export default Header