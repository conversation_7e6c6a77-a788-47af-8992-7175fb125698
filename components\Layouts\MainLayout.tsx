import { AppSidebar } from "@/components/Navigation/app-sidebar"
import {
    SidebarInset,
    SidebarProvider,
} from "@/components/ui/sidebar"
import Header from "../Navigation/header"
import { getSession } from "@/lib/auth"
import { redirect } from "next/navigation"

export default async function MainLayout({ children }: { children: React.ReactNode }) {
    const session = await getSession();
    if (!session) redirect("/");
    return (
        <>
            <SidebarProvider className="h-fit">
                <AppSidebar user={session.user}/>
                <SidebarInset>
                    <Header />
                    {children}
                </SidebarInset>
            </SidebarProvider>
        </>
    )
}