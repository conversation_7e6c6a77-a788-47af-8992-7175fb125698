name: Build and Push to ECR

on:
  push:
    branches:
      - main

env:
  REPO_APP_NAME: nestbackend

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and tag Docker image
        run: |
          docker build -t ${{ env.REPO_APP_NAME }}:${{ github.sha }} .

      - name: Tag and Push Docker Image to ECR
        run: |
          REPO_URI=$(aws ecr describe-repositories --repository-names ${{ env.REPO_APP_NAME }} --query 'repositories[0].repositoryUri' --output text)
          docker tag ${{ env.REPO_APP_NAME }}:${{ github.sha }} $REPO_URI:${{ github.sha }}
          docker push $REPO_URI:${{ github.sha }}

      - name: Update Kubernetes Manifest
        env:
          GH_TOKEN: ${{ secrets.GH_PAT }}
          AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
          AWS_REGION: ${{ secrets.AWS_REGION }}
          GITHUB_SHA: ${{ github.sha }}
        run: | 
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
          git clone https://${GH_TOKEN}@github.com/Steps-AI/k8s.git
          cd k8s
          sed -i "s|image: .*|image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/nestbackend:${GITHUB_SHA}|g" nest-backend/stg/deployment.yaml
          git commit -am "[GHA] UserBackend Staging --> ${GITHUB_SHA}"
          git push origin main

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@0.28.0
        with:
          image-ref: '${{ env.REPO_APP_NAME }}:${{ github.sha }}'
          format: 'table'
          exit-code: '0'
          ignore-unfixed: true
          vuln-type: 'os,library'
          severity: 'UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL'
