"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ConnectorProps } from '../types';
import {
    Loader2,
    CheckCircle2,
    MessageSquare,
    Power,
    FileText,
    ArrowRight,
    Shield,
    LogIn,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { queryClient, createMutationFn, useApiMutation, useApiQuery } from "@/lib/apiClient";
import { useRouter } from "next/navigation";
// import { useUI } from "@/contexts/UIContext";

interface GithubUser {
    email: string
    name: string
    photoUrl: string
}



const LoadingSkeleton = () => (
    <div className="space-y-8 p-6 bg-card rounded-lg border shadow-sm dark:border-border animate-pulse">
        <div className="flex items-center justify-between">
            <div className="space-y-2">
                <div className="h-6 w-48 bg-muted rounded-md" />
                <div className="h-4 w-72 bg-muted rounded-md" />
            </div>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
            {[1, 2].map((i) => (
                <div key={i} className="space-y-6">
                    <div className="w-48 h-48 mx-auto bg-muted rounded-lg" />
                    <div className="space-y-2">
                        <div className="h-5 w-40 mx-auto bg-muted rounded-md" />
                        <div className="h-4 w-56 mx-auto bg-muted rounded-md" />
                    </div>
                </div>
            ))}
        </div>

        <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border flex justify-between items-center">
            <div className="flex items-center gap-4">
                <div className="h-6 w-6 bg-muted rounded-full" />
                <div className="h-4 w-64 bg-muted rounded-md" />
            </div>
            <div className="h-10 w-40 bg-muted rounded-md" />
        </div>
    </div>
)

export default function GithubConnector({ data }: ConnectorProps) {
    console.log("Initial data:", data); 

    const router = useRouter()
    // const { setSelectedConnectors, setShowSelected } = useUI();
    const [state, setState] = useState<"disconnected" | "connecting" | "connected">(
        data?.isConnected ? "connected" : "disconnected"
    )
    console.log("Current state:", state); 

    const [user, setUser] = useState<GithubUser | null>(
        data?.config ? {
            email: data.config.email,
            name: data.config.name,
            photoUrl: data.config.avatar_url
        } : null
    )
    console.log("Current user:", user); 

    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [isDisconnecting, setIsDisconnecting] = useState(false)
    const isHandledRef = useRef(false)

    const getAuthUrl = useApiMutation(
        createMutationFn.post('/github/auth-url'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['github-auth-url'] });
                    router.push(response.result)
                    console.log(response)
                    setError(null)
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error setting up the request")
                }
            }
        }
    );

    const handleConnect = async () => {
        console.log("Initiating connection..."); 
        setIsLoading(true)
        try {
            await getAuthUrl.mutateAsync({})
            
        } catch (error) {
            console.error("Auth URL error:", error); 
            setIsLoading(false)
        }
    }

    const disconnectGithub = useApiMutation(
        createMutationFn.post('/github/disconnect'),
        {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['github'] });
                setState("disconnected")
                setUser(null)
                setError(null)
                setIsDisconnecting(false)
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong while disconnecting."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error disconnecting from GitHub")
                }
                setIsDisconnecting(false)
            }
        }
    );

    const handleDisconnect = async () => {
        setIsDisconnecting(true)
        await disconnectGithub.mutateAsync({})
    }

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const state = urlParams.get("state");

        console.log("URL params - code:", code, "state:", state); 

        if (code && state && !isHandledRef.current) {
            console.log("Initiating GitHub connection..."); 
            isHandledRef.current = true;
            setState("connecting")
            connectGithub.mutateAsync({ code, state })
        }
    }, []);

    const connectGithub = useApiMutation(
        createMutationFn.post('/github/callback'),
        {
            onSuccess: (response: any) => {
                console.log("Connection success response:", response); 
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['github'] });
                    setUser({
                        email: response.result.data.email,
                        name: response.result.data.name,
                        photoUrl: response.result.data.avatar_url
                    })
                    setState("connected")
                    setError(null)
                    window.history.replaceState({}, document.title, window.location.pathname);
                } else {
                    console.error("Unexpected response structure:", response); 
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                console.error("Connection error:", error);
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error setting up the request")
                }
                setState("disconnected")
            },
            onSettled: () => {
                setIsLoading(false);
            }
        }
    );

    const handleStartChatting = () => {
        // setSelectedConnectors(prev => ["GitHub"]);
        // setShowSelected(true);
        router.push('/chat');
    };

    useEffect(() => {
        // console.log("Data changed:", data); 
        if (data?.isConnected) {
            setState("connected");
            setUser(data.config ? {
                email: data.config.email,
                name: data.config.name,
                photoUrl: data.config.avatar_url
            } : null);
        }
    }, [data]);

    if (state === "connecting") {
        return (
            <div className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Connecting to GitHub...</span>
            </div>
        )
    }

    if (state === "connected" && user) {
        const metadata = data?.config?.metadata ? JSON.parse(data.config.metadata) : null;
        const githubUser = metadata?.user;
        const repositories = metadata?.repositories;

        return (
            <div className="space-y-4 p-4 bg-card rounded-lg border shadow-sm dark:border-border">
                <div className="flex items-center gap-4 bg-muted/50 p-4 rounded-lg dark:bg-muted/10">
                    {githubUser?.avatar_url && (
                        <Image 
                            src={githubUser.avatar_url} 
                            alt={githubUser.name || 'GitHub User'} 
                            width={56} 
                            height={56} 
                            className="rounded-full ring-2 ring-primary/10 dark:ring-primary/20"
                            unoptimized
                        />
                    )}
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg">{githubUser?.name || 'GitHub User'}</h3>
                        <p className="text-sm text-muted-foreground">{githubUser?.login}</p>
                    </div>
                    <div className="flex flex-col items-center gap-1">
                        <CheckCircle2 className="h-6 w-6 text-green-500 dark:text-green-400" />
                        <span className="text-xs text-muted-foreground">Connected</span>
                    </div>
                </div>

                {repositories && (
                    <div className="p-4 bg-muted/30 dark:bg-muted/10 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium">Repository Overview</h4>
                            <span className="text-sm text-muted-foreground">
                                Total Repositories: {repositories.total}
                            </span>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="p-3 bg-background rounded-lg border">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Private Repos</span>
                                    <span className="text-sm">
                                        {repositories.repositories?.filter((repo: any) => repo.private).length}
                                    </span>
                                </div>
                            </div>
                            <div className="p-3 bg-background rounded-lg border">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Public Repos</span>
                                    <span className="text-sm">
                                        {repositories.repositories?.filter((repo: any) => !repo.private).length}
                                    </span>
                                </div>
                            </div>
                            <div className="col-span-2 p-3 bg-background rounded-lg border">
                                <div className="space-y-2">
                                    <span className="text-sm font-medium">Top Languages</span>
                                    <div className="flex flex-wrap gap-2">
                                        {Array.from(new Set(repositories.repositories
                                            ?.map((repo: any) => repo.language)
                                            .filter(Boolean)))
                                            .slice(0, 5)
                                            .map((language: unknown) => (
                                                <span key={language as string} className="text-xs px-2 py-1 bg-muted rounded-full">
                                                    {language as string}
                                                </span>
                                            ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <div className="flex flex-col md:flex-row items-center gap-8 w-full p-6 rounded-xl hover:bg-muted/50 transition-colors border border-primary/10 dark:border-primary/20">
                    <div className="relative w-28 h-28 flex-shrink-0 flex items-center justify-center">
                        <div className="absolute inset-0 bg-primary/5 dark:bg-primary/10 rounded-full blur-xl" />
                        <Image
                            src="/new-logo.svg"
                            alt="AI Logo"
                            width={70}
                            height={70}
                            className="relative"
                        />
                    </div>
                    <div className="flex-grow text-center md:text-left">
                        <h3 className="font-semibold text-lg bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
                            Continue to Chat
                        </h3>
                        <div className="space-y-2 text-sm text-muted-foreground mt-3">
                            <p>✨ Ask questions about your repositories</p>
                            <p>🔍 Search across your code and issues</p>
                            <p>📊 Get instant repository insights</p>
                        </div>
                    </div>
                    <div
                        onClick={handleStartChatting}
                        className="flex-shrink-0 w-full md:w-auto cursor-pointer"
                    >
                        <Button className="group bg-primary text-primary-foreground hover:bg-primary/90 w-full md:w-auto">
                            Start Chatting
                            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                    </div>
                </div>

                {/* Action buttons */}
                <div className="flex justify-between items-center mt-4">
                    {/* <Button
                        onClick={handleStartChatting}
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                    >
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Start Chatting
                    </Button> */}

                    <Button
                        onClick={handleDisconnect}
                        variant="outline"
                        className="hover:bg-destructive hover:text-destructive-foreground"
                        disabled={isDisconnecting}
                    >
                        {isDisconnecting ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Disconnecting...
                            </>
                        ) : (
                            <>
                                <Power className="w-4 h-4 mr-2" />
                                Disconnect
                            </>
                        )}
                    </Button>
                </div>
            </div>
        )
    }

    if (isLoading) {
        return <LoadingSkeleton />
    }

    return (
        <div className="space-y-8 p-6 bg-card rounded-lg border shadow-sm dark:border-border">
            <div className="flex items-center justify-between">
                <div className="space-y-2">
                    <h3 className="font-semibold text-lg">Connect GitHub</h3>
                    <p className="text-sm text-muted-foreground">Access your repositories with AI-powered insights</p>
                </div>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
                <div className="relative">
                    <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl" />
                    <div className="relative space-y-6">
                        <img
                            src="/github.svg"
                            alt="Search Repositories"
                            className="w-48 h-48 mx-auto"
                        />
                        <div className="space-y-2">
                            <h3 className="font-semibold text-center">Smart Code Search</h3>
                            <p className="text-sm text-muted-foreground text-center">
                                Find any information across your repositories instantly
                            </p>
                        </div>
                    </div>
                </div>

                <div className="relative">
                    <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl" />
                    <div className="relative space-y-6">
                        <Image
                            src="/new-logo.svg"
                            alt="AI Logo"
                            width={60}
                            height={60}
                            className="mx-auto w-48 h-48"
                        />
                        <div className="space-y-2">
                            <h3 className="font-semibold text-center">AIDE - Repository Intelligence</h3>
                            <p className="text-sm text-muted-foreground text-center">
                                Get instant insights and analysis from your repositories
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {error && (
                <div className="bg-destructive/10 text-destructive dark:bg-destructive/20 p-3 rounded-md text-sm">
                    {error}
                </div>
            )}

            <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border border-primary/10 dark:border-primary/20 flex justify-between items-center">
                <div className="flex items-center gap-4">
                    <Shield className="h-6 w-6 text-primary" />
                    <p className="text-sm">
                        <span className="font-medium">Secure & Private:</span>
                        {" "}Your data stays protected with real-time processing
                    </p>
                </div>
                <Button
                    onClick={handleConnect}
                    className="bg-primary dark:text-white"
                    disabled={getAuthUrl.isPending}
                >
                    {getAuthUrl.isPending ? (
                        <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Connecting...
                        </>
                    ) : (
                        <>
                            <LogIn className="w-4 h-4 mr-2" />
                            Connect GitHub
                        </>
                    )}
                </Button>
            </div>
        </div>
    )
}
