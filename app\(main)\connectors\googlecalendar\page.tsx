"use client";
import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@steps-ai/ui";
import DeepDive from "./components/deepdive";
import Settings from "./components/settings";
import Link from "next/link";
import { ChevronLeft, Loader2 } from "lucide-react";
import {
    queryClient,
    createMutationFn,
    useApiMutation,
    useApiQuery,
} from "@/lib/apiClient";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import Overview from "./components/overview";
import Agent from "./components/agent";

const NotionPage = () => {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const isHandledRef = useRef(false);
    const {
        data,
        isLoading: isLoadingData,
        isError,
    } = useApiQuery<any>([`google-calendar`], `/google-calendar/user-configs`);

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const state = urlParams.get("state");

        if (code && state && !isHandledRef.current) {
            isHandledRef.current = true;
            handleOAuthCallback(code, state);
        } else {
        }
    }, []);

    const connectNotion = useApiMutation(
        createMutationFn.post("/google-calendar/callback"),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ["google-calendar"] });
                    console.log(response);
                    setError(null);
                    window.history.replaceState(
                        {},
                        document.title,
                        window.location.pathname
                    );
                } else {
                    setError("Unexpected response from server");
                }
                setIsLoading(false);
            },
            onError: (error: any) => {
                setError(
                    error.response?.data?.message || "Failed to connect to Google Calendar"
                );
                setIsLoading(false);
            },
        }
    );

    const handleOAuthCallback = async (code: string, state: string) => {
        setIsLoading(true);
        try {
            await connectNotion.mutateAsync({ code, state });
        } finally {
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    };

    const getAuthUrl = useApiMutation(createMutationFn.post("/google-calendar/auth-url"), {
        onSuccess: (response: any) => {
            if (response?.status && response?.result) {
                queryClient.invalidateQueries({ queryKey: ["google-calendar-auth-url"] });
                try {
                    const url = new URL(response.result);
                    router.push(url.toString());
                    setError(null);
                } catch (e) {
                    console.error("Invalid auth URL:", e);
                    setError("Received invalid authorization URL");
                }
            } else {
                console.error("Unexpected response format:", response);
                setError("Unexpected response from server");
            }
        },
        onError: (error: any) => {
            console.error("Auth URL error:", error);
            setError(error.response?.data?.message || "Failed to get auth URL");
        },
    });
    const handleConnect = async () => {
        setIsLoading(true);
        try {
            await getAuthUrl.mutateAsync({});
        } catch (error) {
            console.error("Connection error:", error);
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <div className="w-full">
            {isLoadingData ? (
                <div className="space-y-2 animate-pulse">
                    <div className="flex items-center space-x-2">
                        <Skeleton className="h-4 w-4 rounded" />
                        <Skeleton className="h-4 w-[120px] rounded" />
                    </div>
                    <div className="flex flex-row items-center gap-2">
                        <Skeleton className="h-12 w-12 rounded" />
                        <Skeleton className="h-10 w-[200px] rounded" />
                    </div>
                    <Skeleton className="h-4 w-full max-w-[500px] rounded" />
                    <div className="border rounded-lg p-6 mt-5 dark:border-neutral-800">
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-[100px] rounded" />
                                    <Skeleton className="h-4 w-[80px] rounded" />
                                </div>
                                <Skeleton className="h-6 w-[100px] rounded-full" />
                            </div>
                        </div>
                    </div>
                </div>
            ) : data?.result.config ? (
                <Tabs defaultValue="overview" className="w-full">
                    <TabsList>
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="agent">Agent</TabsTrigger>
                        <TabsTrigger value="deep-dive">Deep Dive</TabsTrigger>
                        <TabsTrigger value="settings">Settings</TabsTrigger>
                    </TabsList>
                    <TabsContent value="overview" className='mt-5'>
                        <Overview config={data?.result.config}/>
                    </TabsContent>
                    <TabsContent value="agent" className='mt-5 w-full'>
                        <Agent />
                    </TabsContent>
                    <TabsContent value="deep-dive" className='mt-5 w-full'>
                        <DeepDive etls={data?.result.etls} userConnectorId={data?.result.id} />
                    </TabsContent>
                    <TabsContent value="settings" className='mt-5 w-full'>
                        <Settings />
                    </TabsContent>
                </Tabs>
            ) : (
                <div className="flex flex-col gap-4 h-72 items-center justify-center bg-foreground/5 p-2 rounded-md">
                    <h1 className="text-2xl font-bold">Google Calendar not connected</h1>
                    <p className="text-sm font-medium">
                        Connect your Google Calendar to access and manage your events
                    </p>
                    <Button
                        variant="outline"
                        className="bg-blue-500 text-white hover:bg-blue-600 hover:text-white"
                        onClick={handleConnect}
                        disabled={isLoading}
                    >
                        {isLoading
                            ? " Connecting..."
                            : "Connect Google Calendar"}
                    </Button>
                </div>
            )}
        </div>
    );
};

export default NotionPage;
