'use client';

import { ReactNode } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { ChevronLeft } from 'lucide-react';
import LayoutAnimation from '../Animation/layoutAnimation';

interface ConnectorLayoutProps {
    title?: string;
    description?: string;
    children: ReactNode;
    isLoading?: boolean;
    logo?: string;
    statusComponent?: ReactNode;
}

export function ConnectorLayout({
    title,
    description,
    children,
    isLoading,
    logo,
    statusComponent,
}: ConnectorLayoutProps) {
    return (
        // <LayoutAnimation>
            <div className="px-3 sm:px-5 md:px-20 mx-auto p-3 sm:p-6 w-full">
                <Link
                    href="/connectors"
                    className="inline-flex items-center text-[13px] sm:text-sm text-muted-foreground hover:text-foreground transition-colors mb-6"
                >
                    <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5" />
                    Back to Connectors
                </Link>
                {isLoading ? (
                    <>
                        <div className="space-y-2 w-full">
                            <div className="flex items-center space-x-2">
                                <Skeleton className="h-4 w-4" />
                                <Skeleton className="h-4 w-[120px]" />
                            </div>
                            <div className="flex flex-row items-center gap-2">
                                {logo && <Skeleton className="h-12 w-12" />}
                                <Skeleton className="h-10 w-[200px]" />
                            </div>
                            <Skeleton className="h-4 w-full max-w-[500px]" />
                        </div>
                        <div className="border rounded-lg p-6 mt-5">
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-2">
                                        <Skeleton className="h-4 w-[100px]" />
                                        <Skeleton className="h-4 w-[80px]" />
                                    </div>
                                    <Skeleton className="h-6 w-[100px] rounded-full" />
                                </div>
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="flex flex-col sm:block">
                        <div className="space-y-6 sm:space-y-4 mb-8 sm:mb-0">
                            <div className="flex flex-col sm:flex-row sm:items-start gap-2 sm:gap-2">
                                {logo && (
                                    <div className="flex justify-center sm:justify-start">
                                        <img
                                            src={logo}
                                            alt="Connector Logo"
                                            className="h-16 w-16 sm:h-12 sm:w-12 object-contain rounded-xl"
                                        />
                                    </div>
                                )}
                                <div className="flex flex-col items-center sm:items-start gap-3">
                                    <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-center sm:text-left">{title}</h1>
                                    {statusComponent && (
                                        <div className="mt-1 sm:mt-0">
                                            {statusComponent}
                                        </div>
                                    )}
                                    {description && (
                                        <p className="text-sm sm:text-base text-muted-foreground text-center sm:text-left max-w-[90%] sm:max-w-none">
                                            {description}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="mt-auto sm:mt-8">
                            {children}
                        </div>
                    </div>
                )}
            </div>
        // </LayoutAnimation>
    );
} 