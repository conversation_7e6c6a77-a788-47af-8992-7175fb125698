import { Metadata } from "next"
import { ConnectorLayout } from "@/components/Layouts/ConnectorLayout"
import { assets } from "@/lib/assets"
export const metadata: Metadata = {
    title: "Google Drive",
}

interface GmailLayoutProps {
    children: React.ReactNode
}

export default function GmailLayout({ children }: GmailLayoutProps) {
    return (
        <ConnectorLayout
            title="Gmail"
            description="Integrate your Gmail emails and attachments"
            isLoading={false}
            logo={assets.Icons.Gmail}
        >
            {children}
        </ConnectorLayout>
    )
}