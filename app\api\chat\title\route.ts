import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import OpenAI from 'openai';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || "",
});

export const maxDuration = 30;
export const dynamic = "force-dynamic";

export async function POST(req: NextRequest) {
    try {
        const session = await getSession();
        if (!session) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const data = await req.json();
        if (!data.input) {
            return NextResponse.json({ title: "New Chat" });
        }

        const completion = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
                {
                    role: "system",
                    content: `You are an AI title maker bot. 
                From the input summarise a chat title with more than 2 words and max 5 words.
                Make a title that describes the conversation.
                If the input is missing or is gibberish then just generate any cool chat name with in 5 words, never generate a title more than 5 words, and never generate same title twice.
                This is the input entered by the user:`
                },
                {
                    role: "user",
                    content: data.input,
                }
            ],
            temperature: 0.3,
            max_tokens: 200
        });

        const title = completion.choices[0]?.message?.content?.trim() || "New Chat";
        return NextResponse.json({ title });
    } catch (error: any) {
        console.error('Error generating title:', error?.message || error);
        return NextResponse.json(
            { error: "Failed to generate title", details: error?.message },
            { status: 500 }
        );
    }
}