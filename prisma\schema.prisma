generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

////////////////////////////////////////////////////////////
// ENUMS
////////////////////////////////////////////////////////////

enum Status {
  NEW
  UPLOADED
  PROCESSING
  SUCCESS
  ERROR
}

enum ConnectorsStatus {
  AVAILABLE
  COMING_SOON
}

enum ConnectorsType {
  FREE
  PAID
  ENTERPRISE
}

enum Type {
  USER
  ADMIN
}

enum Theme {
  LIGHT
  DARK
}

enum UserType {
  INDIVIDUAL
  BUSINESS
}

enum PlanType {
  FREE
  PRO
  ENTERPRISE
  TRIAL
}

enum Role {
  USER
  ADMIN
  SUPERADMIN
  TEAMADMIN
}

enum EtlStatus {
  QUEUED
  RUNNING
  SUCCESS
  FAILED
  NEW
  UPLOADED
  PROCESSING
  ERROR
}

enum IntegrationsType {
  FREE
  PRO
  ENTERPRISE
}

enum IntegrationsStatus {
  ACTIVE
  INACTIVE
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}

enum InvitationType {
  ORGANIZATION
  TEAM
}

enum ActivityType {
  USER_JOINED_ORGANIZATION
  USER_LEFT_ORGANIZATION
  USER_ROLE_CHANGED
  TEAM_CREATED
  TEAM_DELETED
  TEAM_MEMBER_ADDED
  TEAM_MEMBER_REMOVED
  TEAM_ADMIN_ASSIGNED
  CONNECTOR_CONNECTED
  CONNECTOR_DISCONNECTED
  FILE_UPLOADED
  KNOWLEDGE_BASE_CREATED
  KNOWLEDGE_BASE_SHARED
  ONBOARDING_STEP_COMPLETED
  ONBOARDING_COMPLETED
  CALENDAR_CONNECTED
  CHAT_CREATED
}

////////////////////////////////////////////////////////////
// Base Models (No Foreign Keys)
////////////////////////////////////////////////////////////

model Plan {
  id                String         @id @default(uuid())
  type              PlanType       @default(TRIAL)
  name              String         @unique
  messagesPerDay    Int
  maxStorage        Int
  maxKnowledgeBases Int?
  priceMonthly      Float
  priceYearly       Float
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  users             User[]
  subscriptions     Subscription[]
}

model Connectors {
  id                     String                   @id @default(uuid())
  name                   String
  logo                   String
  category               String?
  description            String
  slug                   String?
  status                 ConnectorsStatus
  type                   ConnectorsType
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  UserConnectors         UserConnectors[]
  TeamConnectors         TeamConnectors[]
  OrganizationConnectors OrganizationConnectors[]
}

model Integrations {
  id          String             @id @default(uuid())
  name        String
  description String
  logo        String
  slug        String
  status      IntegrationsStatus
  type        IntegrationsType
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
}

////////////////////////////////////////////////////////////
// User Model (Depends on Plan)
////////////////////////////////////////////////////////////

model User {
  id                           String    @id @default(uuid())
  email                        String    @unique
  name                         String?
  emailVerified                Boolean   @default(false)
  type                         Type      @default(USER)
  userType                     UserType  @default(INDIVIDUAL)
  themePreference              Theme     @default(LIGHT)
  role                         Role      @default(USER)
  password                     String?
  hasOnboarded                 Boolean   @default(false)
  profileImageUrl              String?
  organizationId               String?
  refreshToken                 String?
  twoFactorsecret              String?
  twoFactorEnabled             Boolean   @default(false)
  emailVerificationToken       String?
  emailVerificationTokenExpiry DateTime?
  planId                       String?
  onboardingCompleted          Boolean   @default(false)
  onboardingData               Json?
  createdAt                    DateTime  @default(now())
  updatedAt                    DateTime  @updatedAt

  // Relations
  plan                 Plan?            @relation(fields: [planId], references: [id])
  subscription         Subscription?
  sharedKnowledgebases KnowledgeBase[]
  knowledgeBases       KnowledgeBase[]  @relation("KnowledgeBaseOwner")
  files                File[]
  supports             Support[]
  feedbacks            Feedback[]
  connectors           UserConnectors[]
  teams                Team[]
  organization         Organization?    @relation(fields: [organizationId], references: [id])
  createdOrganizations Organization[]   @relation("OrganizationCreator")
  sentInvitations      Invitation[]     @relation("InvitationInviter")
  onboardingStatus     OnboardingStatus?
  activityLogs         ActivityLog[]

  @@index([email])
  @@index([organizationId])
  @@index([planId])
}

////////////////////////////////////////////////////////////
// Dependent Models
////////////////////////////////////////////////////////////

model Subscription {
  id         String    @id @default(uuid())
  startDate  DateTime  @default(now())
  endDate    DateTime?
  isActive   Boolean   @default(true)
  canceledAt DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  // Relations
  userId String @unique
  user   User   @relation(fields: [userId], references: [id])
  planId String
  plan   Plan   @relation(fields: [planId], references: [id])
}

model KnowledgeBase {
  id          String   @id @default(uuid())
  name        String
  status      Status   @default(NEW)
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  ownerId        String?
  owner          User?         @relation("KnowledgeBaseOwner", fields: [ownerId], references: [id])
  teamId         String?
  team           Team?         @relation(fields: [teamId], references: [id])
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  members User[]
  files   File[]
  etls    Etl[]

  @@index([ownerId])
}

model File {
  id        String   @id @default(uuid())
  name      String
  size      Int
  mimeType  String
  status    Status
  url       String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId          String?
  user            User?          @relation(fields: [userId], references: [id])
  knowledgeBaseId String?
  knowledgeBase   KnowledgeBase? @relation(fields: [knowledgeBaseId], references: [id])

  @@index([userId])
  @@index([knowledgeBaseId])
}

model Support {
  id        String   @id @default(uuid())
  email     String
  message   String
  area      String
  status    String   @default("new")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id])
}

model Feedback {
  id        String   @id @default(uuid())
  email     String
  rating    Int
  area      String
  message   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id])
}

model UserConnectors {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  config    Json?

  // Relations
  userId      String
  user        User       @relation(fields: [userId], references: [id])
  connectorId String
  connector   Connectors @relation(fields: [connectorId], references: [id])
  etls        Etl[]
}

model TeamConnectors {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  config    Json?

  // Relations
  teamId      String
  team        Team       @relation(fields: [teamId], references: [id])
  connectorId String
  connector   Connectors @relation(fields: [connectorId], references: [id])
  etls        Etl[]
}

model OrganizationConnectors {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  config    Json?

  // Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  connectorId    String
  connector      Connectors   @relation(fields: [connectorId], references: [id])
  etls           Etl[]
}

model Etl {
  id                      String    @id @default(uuid())
  userConnectorId         String?
  teamConnectorId         String?
  organizationConnectorId String?
  knowledgeBaseId         String?
  taskId                  String    @unique
  status                  EtlStatus
  startDate               DateTime
  endDate                 DateTime?
  createdAt               DateTime  @default(now())
  updatedAt               DateTime  @updatedAt

  // Relations
  knowledgeBase         KnowledgeBase?          @relation(fields: [knowledgeBaseId], references: [id])
  userConnector         UserConnectors?         @relation(fields: [userConnectorId], references: [id])
  teamConnector         TeamConnectors?         @relation(fields: [teamConnectorId], references: [id])
  organizationConnector OrganizationConnectors? @relation(fields: [organizationConnectorId], references: [id])
}

////////////////////////////////////////////////////////////
// Organization Models
////////////////////////////////////////////////////////////

model Team {
  id             String           @id @default(uuid())
  name           String
  description    String?
  organizationId String
  organization   Organization     @relation(fields: [organizationId], references: [id])
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  connectors     TeamConnectors[]
  members        User[]
  KnowledgeBase  KnowledgeBase[]
  invitations    Invitation[]

  @@index([organizationId])
}

model Organization {
  id            String                   @id @default(uuid())
  name          String
  slug          String?
  description   String?
  logo          String?
  createdAt     DateTime                 @default(now())
  updatedAt     DateTime                 @updatedAt
  createdBy     String? // ID of the superadmin who created it
  creator       User?                    @relation("OrganizationCreator", fields: [createdBy], references: [id])
  connectors    OrganizationConnectors[]
  teams         Team[]
  users         User[]
  KnowledgeBase KnowledgeBase[]
  invitations   Invitation[]
  activityLogs  ActivityLog[]

  @@index([createdBy])
}

model Invitation {
  id             String           @id @default(uuid())
  email          String
  type           InvitationType
  status         InvitationStatus @default(PENDING)
  token          String           @unique
  expiresAt      DateTime
  invitedBy      String
  inviter        User             @relation("InvitationInviter", fields: [invitedBy], references: [id])
  organizationId String
  organization   Organization     @relation(fields: [organizationId], references: [id])
  teamId         String?
  team           Team?            @relation(fields: [teamId], references: [id])
  acceptedAt     DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  @@index([email])
  @@index([organizationId])
  @@index([teamId])
  @@index([status])
  @@index([expiresAt])
}

model OnboardingStatus {
  id                    String   @id @default(uuid())
  userId                String   @unique
  user                  User     @relation(fields: [userId], references: [id])
  hasCreatedChat        Boolean  @default(false)
  hasConnectedCalendar  Boolean  @default(false)
  hasCompletedOnboarding Boolean @default(false)
  chatCreatedAt         DateTime?
  calendarConnectedAt   DateTime?
  onboardingCompletedAt DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@index([userId])
}

model ActivityLog {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  userId         String?      // Actor who performed the action (nullable for system actions)
  user           User?        @relation(fields: [userId], references: [id])
  activityType   ActivityType
  description    String       // Human-readable description
  metadata       Json?        // Additional context data
  ipAddress      String?      // IP address of the actor
  userAgent      String?      // User agent string
  createdAt      DateTime     @default(now())

  @@index([organizationId])
  @@index([userId])
  @@index([activityType])
  @@index([createdAt])
  @@index([organizationId, createdAt])
}
