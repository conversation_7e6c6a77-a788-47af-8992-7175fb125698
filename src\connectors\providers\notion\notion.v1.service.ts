import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { ConnectorsService } from '../../v1/connectors.v1.service';
import {
  BaseConnectorService,
  IBaseCallbackResponse,
} from '../../interfaces/base.interface';
import { EtlService } from '@/etl/etl.service';
import { EtlStatus } from '@prisma/client';
import { CrawlNotionDto } from './dtos/notion.dto';

const NOTION_AUTH_URL = 'https://api.notion.com/v1/oauth/authorize';

@Injectable()
export class NotionService extends BaseConnectorService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
    private etlService: EtlService,
  ) {
    super();
  }

  async generateRedirectUrl(userId: string): Promise<string | { url: string }> {
    const clientId = this.configService.get('notion.clientId');
    const redirectUri = this.configService.get('notion.redirectUri');
    if (!clientId) {
      throw new BadRequestException('Notion client ID not configured');
    }
    const authUrl = `${NOTION_AUTH_URL}?owner=user&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&state=${userId}`;
    return authUrl;
  }

  async handleCallback(
    code: string,
    state: string,
  ): Promise<IBaseCallbackResponse> {
    try {
      const clientId = this.configService.get('notion.clientId');
      const clientSecret = this.configService.get('notion.clientSecret');
      const redirectUri = this.configService.get('notion.redirectUri');

      if (!clientId || !clientSecret) {
        throw new BadRequestException(
          'Notion client credentials not configured',
        );
      }

      // Exchange code for access token
      const tokenResponse = await fetch(
        'https://api.notion.com/v1/oauth/token',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          },
          body: JSON.stringify({
            grant_type: 'authorization_code',
            code,
            redirect_uri: redirectUri,
          }),
        },
      );

      const tokenData = await tokenResponse.json();
      if (tokenData.error) {
        throw new Error(tokenData.error_description);
      }

      const { access_token } = tokenData;
      const userId = state;
      const userData = await this.getNotionUserDetails(access_token);

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Notion',
        },
        select: {
          id: true,
          type: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('Notion connector not found');
      }

      await this.prisma.userConnectors.create({
        data: {
          userId,
          connectorId: connector.id,
          config: {
            name: userData.currentUser.name,
            email: userData.currentUser.email,
            image: userData.currentUser.avatarUrl,
            metadata: JSON.stringify(userData),
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(access_token);

      const userConnectorDetails = {
        user_id: userId,
        connector_id: connector.id,
        connector_type: connector.type,
        name: 'Notion',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        userConnectorDetails,
      );

      return {
        success: true,
        message: 'Notion integration connected successfully',
        data: userConnectorDetails,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to process Notion callback: ${error.message}`,
      );
    }
  }

  async getNotionUserConfigs(userId: string) {
    const connector = await this.prisma.connectors.findFirst({
      where: {
        slug: 'notion',
      },
      include: {
        UserConnectors: {
          where: {
            userId: userId,
          },
          select: {
            config: true,
            id: true,
            etls: true,
          },
        },
      },
    });
    if (!connector.UserConnectors.length) {
      return { config: null, id: null, etls: [] };
    }

    return {
      config: connector.UserConnectors[0].config,
      id: connector.UserConnectors[0].id,
      etls: connector.UserConnectors[0].etls,
    };
  }

  async disconnect(
    userId: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const userConnector = await this.dynamoDBService.get(
        this.dynamoDBTableName,
        {
          user_id: userId,
          name: 'Notion',
        },
      );

      if (!userConnector) {
        throw new NotFoundException('Notion connector not found');
      }

      const decryptedAccessToken = this.connectorsService.decrypt(
        userConnector.credentials.access_token,
        userConnector.credentials.access_token_iv,
      );

      if (decryptedAccessToken) {
        try {
          await fetch('https://api.notion.com/v1/oauth/revoke', {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${decryptedAccessToken}`,
              'Notion-Version': '2022-06-28',
              'Content-Type': 'application/json',
            },
          });
        } catch (error) {
          console.error('Failed to revoke Notion token:', error.message);
        }
      }
      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'Notion',
      });

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Notion',
        },
        select: {
          id: true,
        },
      });

      if (connector) {
        await this.prisma.userConnectors.deleteMany({
          where: {
            userId,
            connectorId: connector.id,
          },
        });
      }
      return {
        success: true,
        message: 'Notion integration disconnected successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to disconnect Notion: ${error.message}`,
      );
    }
  }

  async crawlNotion(userId: string, data: CrawlNotionDto) {
    const { userConnectorId } = data;
    const etlRun = await this.prisma.etl.findFirst({
      where: {
        userConnectorId: userConnectorId,
      },
    });

    if (etlRun && etlRun.status !== EtlStatus.FAILED) {
      throw new BadRequestException('ETL in progress!');
    }

    if (etlRun && etlRun.status === EtlStatus.FAILED) {
      await this.prisma.etl.delete({
        where: {
          id: etlRun.id,
        },
      });
    }

    // const etlResponse = await this.etlService.publish(
    //   userId,
    //   ConnectorName.NOTION,
    //   userConnectorId,
    // );

    // await this.prisma.etl.create({
    //   data: {
    //     dagId: etlResponse.dagId,
    //     dagRunId: etlResponse.dagRunId,
    //     status: etlResponse.state,
    //     startDate: etlResponse.executionDate,
    //     userConnectorId: userConnectorId,
    //   },
    // });

    return {
      success: true,
      message: 'Notion crawl started successfully',
    };
  }

  async getCrawlStatus(userId: string, userConnectorId: string) {
    const etlRun = await this.prisma.etl.findFirst({
      where: {
        userConnectorId: userConnectorId,
      },
    });
    if (!etlRun) {
      throw new BadRequestException('Crawl not found');
    }
    // const status = await this.etlService.getStatus(etlRun.dagRunId);
    // await this.prisma.etl.update({
    //   where: {
    //     id: etlRun.id,
    //   },
    //   data: {
    //     status: EtlStatus[status.toUpperCase()],
    //   },
    // });
    return {
      status: etlRun.status,
    };
  }

  async getNotionAccessToken(userId: string) {
    try {
      const userConnector = await this.dynamoDBService.get(
        this.dynamoDBTableName,
        {
          user_id: userId,
          name: 'Notion',
        },
      );

      if (!userConnector || !userConnector.credentials) {
        throw new NotFoundException('Notion connector not found');
      }

      const decryptedAccessToken = this.connectorsService.decrypt(
        userConnector.credentials.access_token,
        userConnector.credentials.access_token_iv,
      );

      return {
        accessToken: decryptedAccessToken,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get Notion access token: ${error.message}`,
      );
    }
  }

  private async getNotionUserDetails(accessToken: string) {
    try {
      // Get current user details
      const userResponse = await fetch('https://api.notion.com/v1/users/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Notion-Version': '2022-06-28',
        },
      });
      const userData = await userResponse.json();
      if (userData.error) {
        throw new Error(
          userData.error_description || 'Failed to fetch user data',
        );
      }

      // Get all workspace users (which includes workspace info)
      const workspaceUsersResponse = await fetch(
        'https://api.notion.com/v1/users',
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Notion-Version': '2022-06-28',
          },
        },
      );
      const workspaceData = await workspaceUsersResponse.json();
      if (workspaceData.error) {
        throw new Error(
          workspaceData.error_description || 'Failed to fetch workspace data',
        );
      }

      // Get all pages (search through all content)
      const searchResponse = await fetch('https://api.notion.com/v1/search', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Notion-Version': '2022-06-28',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filter: {
            property: 'object',
            value: 'page',
          },
        }),
      });
      const pagesData = await searchResponse.json();
      if (pagesData.error) {
        throw new Error(
          pagesData.error_description || 'Failed to fetch pages data',
        );
      }

      // Extract unique workspaces from users list
      const workspaces = [
        ...new Set(workspaceData.results.map((user) => user.workspace_name)),
      ];

      return {
        currentUser: {
          id: userData.id,
          name: userData.name,
          avatarUrl: userData.avatar_url,
          email: userData.person?.email,
          type: userData.type,
        },
        workspaces,
        pages: pagesData.results.map((page) => ({
          id: page.id,
          title: page.properties?.title?.title?.[0]?.plain_text || 'Untitled',
          url: page.url,
          createdTime: page.created_time,
          lastEditedTime: page.last_edited_time,
        })),
        workspaceUsers: workspaceData.results.map((user) => ({
          id: user.id,
          name: user.name,
          avatarUrl: user.avatar_url,
          type: user.type,
        })),
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get Notion user details: ${error.message}`,
      );
    }
  }
}
