import { lazy } from 'react';
export const connectorComponents: Record<string, React.LazyExoticComponent<any>> = {
    'jira': lazy(() => import('./Jira/index')),
    'googledrive': lazy(() => import('./GoogleDrive/index')),
    'onedrive': lazy(() => import('./OneDrive/index')),
    'gmail': lazy(() => import('./Gmail/index')),
    'slack': lazy(() => import('./Slack/index')),
    'github': lazy(() => import('./Github/index')),
    'googlecalendar': lazy(() => import('./GoogleCalendar/index')),
    // 'notion': lazy(() => import('./Notion/index')),
    'hubspot': lazy(() => import('./Hubspot/index')),
};

export const getConnectorComponent = (slug: string) => {
    return connectorComponents[slug] || null;
}; 