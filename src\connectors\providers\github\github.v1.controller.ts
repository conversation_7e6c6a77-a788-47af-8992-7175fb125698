import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { GitHubService } from './github.v1.service';
import { GitHubCallbackDto } from './dtos/github.dto';

@ApiTags('GitHub-V1')
@Controller({ version: '1', path: 'github' })
export class GitHubController {
  constructor(private githubService: GitHubService) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate GitHub Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated GitHub Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.githubService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle GitHub Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed GitHub Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: GitHubCallbackDto) {
    return this.githubService.handleCallback(body.code, body.state);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect GitHub Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected GitHub Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.githubService.disconnect(user.sub);
  }
}
