import { Injectable } from '@nestjs/common';
import { ActivitiesService } from './v1/activities.v1.service';
import { ActivityType } from '@prisma/client';

/**
 * Activity Logger Service
 * 
 * This service provides a simple interface for logging activities throughout the application.
 * It can be injected into any service that needs to log user or system activities.
 * 
 * Usage Examples:
 * 
 * // In a service constructor
 * constructor(private activityLogger: ActivityLoggerService) {}
 * 
 * // Log user activities
 * await this.activityLogger.logUserJoinedOrganization(orgId, userId, userName);
 * await this.activityLogger.logTeamCreated(orgId, userId, userName, teamName, teamId);
 * await this.activityLogger.logConnectorConnected(orgId, userId, userName, 'Google Drive');
 * 
 * // Log with additional context
 * await this.activityLogger.logActivity(
 *   orgId, 
 *   userId, 
 *   ActivityType.USER_ROLE_CHANGED,
 *   '<PERSON> was promoted to Admin',
 *   { previousRole: 'USER', newRole: 'ADMIN' },
 *   req.ip,
 *   req.headers['user-agent']
 * );
 */
@Injectable()
export class ActivityLoggerService {
  constructor(private activitiesService: ActivitiesService) {}

  /**
   * Generic method to log any activity
   */
  async logActivity(
    organizationId: string,
    userId: string | null,
    activityType: ActivityType,
    description: string,
    metadata?: any,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    try {
      await this.activitiesService.createActivityLog({
        organizationId,
        userId: userId || undefined,
        activityType,
        description,
        metadata,
        ipAddress,
        userAgent,
      });
    } catch (error) {
      // Log error but don't throw to avoid breaking main operations
      console.error('Failed to log activity:', error);
    }
  }

  // User Management Activities
  async logUserJoinedOrganization(
    organizationId: string,
    userId: string,
    userName: string,
    metadata?: any,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.USER_JOINED_ORGANIZATION,
      `${userName} joined the organization`,
      metadata,
    );
  }

  async logUserLeftOrganization(
    organizationId: string,
    userId: string,
    userName: string,
    metadata?: any,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.USER_LEFT_ORGANIZATION,
      `${userName} left the organization`,
      metadata,
    );
  }

  async logUserRoleChanged(
    organizationId: string,
    targetUserId: string,
    targetUserName: string,
    previousRole: string,
    newRole: string,
    changedByUserId?: string,
    changedByUserName?: string,
  ): Promise<void> {
    const description = changedByUserName
      ? `${changedByUserName} changed ${targetUserName}'s role from ${previousRole} to ${newRole}`
      : `${targetUserName}'s role was changed from ${previousRole} to ${newRole}`;

    await this.logActivity(
      organizationId,
      changedByUserId || targetUserId,
      ActivityType.USER_ROLE_CHANGED,
      description,
      {
        targetUserId,
        targetUserName,
        previousRole,
        newRole,
        changedByUserId,
        changedByUserName,
      },
    );
  }

  // Team Management Activities
  async logTeamCreated(
    organizationId: string,
    userId: string,
    userName: string,
    teamName: string,
    teamId: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.TEAM_CREATED,
      `${userName} created team "${teamName}"`,
      { teamId, teamName },
    );
  }

  async logTeamDeleted(
    organizationId: string,
    userId: string,
    userName: string,
    teamName: string,
    teamId: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.TEAM_DELETED,
      `${userName} deleted team "${teamName}"`,
      { teamId, teamName },
    );
  }

  async logTeamMemberAdded(
    organizationId: string,
    addedByUserId: string,
    addedByUserName: string,
    memberName: string,
    memberId: string,
    teamName: string,
    teamId: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      addedByUserId,
      ActivityType.TEAM_MEMBER_ADDED,
      `${addedByUserName} added ${memberName} to team "${teamName}"`,
      { memberId, memberName, teamId, teamName },
    );
  }

  async logTeamMemberRemoved(
    organizationId: string,
    removedByUserId: string,
    removedByUserName: string,
    memberName: string,
    memberId: string,
    teamName: string,
    teamId: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      removedByUserId,
      ActivityType.TEAM_MEMBER_REMOVED,
      `${removedByUserName} removed ${memberName} from team "${teamName}"`,
      { memberId, memberName, teamId, teamName },
    );
  }

  async logTeamAdminAssigned(
    organizationId: string,
    assignedByUserId: string,
    assignedByUserName: string,
    adminName: string,
    adminId: string,
    teamName: string,
    teamId: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      assignedByUserId,
      ActivityType.TEAM_ADMIN_ASSIGNED,
      `${assignedByUserName} assigned ${adminName} as admin of team "${teamName}"`,
      { adminId, adminName, teamId, teamName },
    );
  }

  // Integration Activities
  async logConnectorConnected(
    organizationId: string,
    userId: string,
    userName: string,
    connectorName: string,
    metadata?: any,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.CONNECTOR_CONNECTED,
      `${userName} connected ${connectorName}`,
      { connectorName, ...metadata },
    );
  }

  async logConnectorDisconnected(
    organizationId: string,
    userId: string,
    userName: string,
    connectorName: string,
    metadata?: any,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.CONNECTOR_DISCONNECTED,
      `${userName} disconnected ${connectorName}`,
      { connectorName, ...metadata },
    );
  }

  // File and Knowledge Base Activities
  async logFileUploaded(
    organizationId: string,
    userId: string,
    userName: string,
    fileName: string,
    fileId: string,
    metadata?: any,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.FILE_UPLOADED,
      `${userName} uploaded file "${fileName}"`,
      { fileId, fileName, ...metadata },
    );
  }

  async logKnowledgeBaseCreated(
    organizationId: string,
    userId: string,
    userName: string,
    knowledgeBaseName: string,
    knowledgeBaseId: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.KNOWLEDGE_BASE_CREATED,
      `${userName} created knowledge base "${knowledgeBaseName}"`,
      { knowledgeBaseId, knowledgeBaseName },
    );
  }

  async logKnowledgeBaseShared(
    organizationId: string,
    userId: string,
    userName: string,
    knowledgeBaseName: string,
    knowledgeBaseId: string,
    sharedWithEmails: string[],
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.KNOWLEDGE_BASE_SHARED,
      `${userName} shared knowledge base "${knowledgeBaseName}" with ${sharedWithEmails.length} user(s)`,
      { knowledgeBaseId, knowledgeBaseName, sharedWithEmails },
    );
  }

  // Onboarding Activities
  async logOnboardingStepCompleted(
    organizationId: string,
    userId: string,
    userName: string,
    step: string,
    metadata?: any,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.ONBOARDING_STEP_COMPLETED,
      `${userName} completed onboarding step: ${step}`,
      { step, ...metadata },
    );
  }

  async logOnboardingCompleted(
    organizationId: string,
    userId: string,
    userName: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.ONBOARDING_COMPLETED,
      `${userName} completed onboarding`,
    );
  }

  async logCalendarConnected(
    organizationId: string,
    userId: string,
    userName: string,
    calendarProvider: string = 'Google Calendar',
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.CALENDAR_CONNECTED,
      `${userName} connected ${calendarProvider}`,
      { calendarProvider },
    );
  }

  async logChatCreated(
    organizationId: string,
    userId: string,
    userName: string,
    chatId?: string,
  ): Promise<void> {
    await this.logActivity(
      organizationId,
      userId,
      ActivityType.CHAT_CREATED,
      `${userName} created a new chat`,
      { chatId },
    );
  }
}
