import { Metadata } from "next"
import { ConnectorLayout } from "@/components/Layouts/ConnectorLayout"
import { assets } from "@/lib/assets"
export const metadata: Metadata = {
    title: "Google Drive",
}

interface GmailLayoutProps {
    children: React.ReactNode
}

export default function GmailLayout({ children }: GmailLayoutProps) {
    return (
        <ConnectorLayout
            title="Google Calendar"
            description="Integrates with Google Calendar to manage events."
            isLoading={false}
            logo={assets.Icons["Google Calendar"]}
        >
            {children}
        </ConnectorLayout>
    )
}