import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    NEXTAUTH_SECRET_exists: !!process.env.NEXTAUTH_SECRET,
    NEXT_AUTH_SECRET_exists: !!process.env.NEXT_AUTH_SECRET,
    NEXTAUTH_URL_exists: !!process.env.NEXTAUTH_URL,
    API_URL_exists: !!process.env.NEXT_PUBLIC_API_URL,
    NODE_ENV: process.env.NODE_ENV,
    secrets_length: {
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET?.length || 0,
      NEXT_AUTH_SECRET: process.env.NEXT_AUTH_SECRET?.length || 0,
    }
  });
}
