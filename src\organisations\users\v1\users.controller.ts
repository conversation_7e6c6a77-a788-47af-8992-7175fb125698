import {
  Controller,
  Post,
  Get,
  Put,
  Param,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { OrgUsersService } from './users.service';
import { AdminAuthGuard } from '../../../auth/guards/admin-auth.guard';
import { AddUserDto, UpdateAdminDto, VerifyAdminDto } from '../dtos/users.dto';
import { UpdateUserRoleDto } from '../dtos/update-role.dto';
import { UpdateOrganizationDto } from '../../dtos/organization.dto';
import { GetCurrentUser } from '../../../common/decorators/getUser.decorator';
import { JwtPayload } from '../../../common/types/jwt-payload';
import {
  AddUserEmailDto,
  BulkAddUserEmailDto,
  VerifyOrgInvitationDto,
  CompleteOrgInvitationDto,
  VerifyTokenDto
} from '../dtos/add-user-email.dto';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';

@ApiTags('OrgUsers-V1')
@Controller({ version: '1', path: 'users' })
export class OrgUsersController {
  constructor(private readonly usersService: OrgUsersService) {}

  @Get('org')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({ summary: 'Get all users for the current user\'s organization' })
  @ApiResponse({ status: 200, description: 'All users for the organization' })
  @HttpCode(200)
  async getUsers(@GetCurrentUser() user: JwtPayload) {
    return this.usersService.getUsers(user.orgId);
  }

  @Post('verify-admin')
  @ApiOperation({ summary: 'Verify admin token' })
  @ApiResponse({ status: 200, description: 'Admin token verified' })
  @HttpCode(200)
  async verifyAdmin(@Body() data: VerifyAdminDto) {
    return this.usersService.verifyAdmin(data);
  }

  @Post('update-admin')
  @ApiOperation({
    summary: 'Complete admin setup with name and password',
    description: 'This endpoint completes the admin onboarding process by setting the admin\'s name and password. It returns access tokens for authentication.'
  })
  @ApiResponse({
    status: 200,
    description: 'Admin setup completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Admin setup completed successfully' },
        admin: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'admin-uuid' },
            email: { type: 'string', example: '<EMAIL>' },
            name: { type: 'string', example: 'Admin Name' },
            role: { type: 'string', example: 'ADMIN' },
            organizationId: { type: 'string', example: 'org-uuid' },
            profileImageUrl: { type: 'string', example: null, nullable: true },
            hasOnboarded: { type: 'boolean', example: true },
            onboardingCompleted: { type: 'boolean', example: true }
          }
        },
        organization: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'org-uuid' },
            name: { type: 'string', example: 'Example Organization' },
            logo: { type: 'string', example: 'https://example.com/logo.png', nullable: true }
          }
        },
        tokens: {
          type: 'object',
          properties: {
            accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
            refreshToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data provided',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Validation failed' },
        error: { type: 'string', example: 'Bad Request' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Admin not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Admin not found' },
        error: { type: 'string', example: 'Not Found' }
      }
    }
  })
  @HttpCode(200)
  async updateAdmin(@Body() data: UpdateAdminDto) {
    return this.usersService.updateAdmin(data);
  }
  @Get(':userId/org/:orgId')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({ summary: 'Get a user by id and org' })
  @ApiResponse({ status: 200, description: 'User details' })
  @HttpCode(200)
  async getUser(
    @Param('userId') userId: string,
    @Param('orgId') orgId: string,
  ) {
    return this.usersService.getUser(userId, orgId);
  }

  @Post('add')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({ summary: 'Add a single user' })
  @ApiResponse({ status: 201, description: 'User added successfully' })
  @HttpCode(201)
  async addUser(@Body() data: AddUserDto) {
    return this.usersService.addUser(data);
  }

  @Post('add-bulk')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({ summary: 'Add multiple users' })
  @ApiResponse({ status: 201, description: 'Users added successfully' })
  @HttpCode(201)
  async addBulkUsers(@Body() data: AddUserDto[]) {
    return this.usersService.addBulkUsers(data);
  }

  @Post('update-role')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({ summary: 'Update user role' })
  @ApiResponse({ status: 200, description: 'User role updated successfully' })
  @HttpCode(200)
  async updateUserRole(@Body() data: UpdateUserRoleDto) {
    return this.usersService.updateUserRole(data);
  }

  @Post('invite')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({ summary: 'Invite a user to an organization by email' })
  @ApiResponse({ status: 201, description: 'Invitation sent successfully' })
  @HttpCode(201)
  async inviteUser(@Body() data: AddUserEmailDto, @GetCurrentUser() user: JwtPayload) {
    data.invitedBy = user.sub;
    data.organizationId = user.orgId;
    return this.usersService.inviteUserByEmail(data);
  }

  @Post('invite-bulk')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({ summary: 'Invite multiple users to an organization by email' })
  @ApiResponse({ status: 201, description: 'Invitations sent successfully' })
  @HttpCode(201)
  async inviteBulkUsers(@Body() data: BulkAddUserEmailDto, @GetCurrentUser() user: JwtPayload) {
    data.invitedBy = user.sub;
    data.organizationId = user.orgId;
    return this.usersService.inviteBulkUsersByEmail(data);
  }

  @Post('invitation/verify')
  @ApiOperation({ summary: 'Verify organization invitation token' })
  @ApiResponse({ status: 200, description: 'Organization invitation token verified' })
  @HttpCode(200)
  async verifyOrgInvitation(@Body() data: VerifyOrgInvitationDto) {
    return this.usersService.verifyOrgInvitation(data);
  }

  @Post('verify-token')
  @ApiOperation({
    summary: 'Unified token verification for both admin and regular users',
    description: 'This endpoint can verify both admin invitation tokens and organization invitation tokens. It returns detailed information about the token type, user role, and next steps for the onboarding process.'
  })
  @ApiResponse({
    status: 200,
    description: 'Token verified successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Token verified successfully' },
        tokenType: { type: 'string', example: 'org-admin-invite', enum: ['org-admin-invite', 'org-invitation'] },
        isAdmin: { type: 'boolean', example: true },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'user-uuid' },
            email: { type: 'string', example: '<EMAIL>' },
            name: { type: 'string', example: 'User Name', nullable: true },
            role: { type: 'string', example: 'ADMIN', enum: ['SUPERADMIN', 'ADMIN', 'TEAMADMIN', 'USER'] }
          }
        },
        organization: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'org-uuid' },
            name: { type: 'string', example: 'Example Organization' },
            logo: { type: 'string', example: 'https://example.com/logo.png', nullable: true }
          }
        },
        nextStep: { type: 'string', example: 'update-admin', enum: ['update-admin', 'complete-invitation'] },
        completionEndpoint: { type: 'string', example: '/v1/users/update-admin' }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid or expired token',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Invalid or expired token: Token has expired' },
        error: { type: 'string', example: 'Bad Request' }
      }
    }
  })
  @HttpCode(200)
  async verifyToken(@Body() data: VerifyTokenDto) {
    return this.usersService.verifyUnifiedToken(data.token);
  }

  @Post('invitation/complete')
  @ApiOperation({ summary: 'Complete organization invitation process' })
  @ApiResponse({ status: 200, description: 'Organization invitation completed successfully' })
  @HttpCode(200)
  async completeOrgInvitation(@Body() data: CompleteOrgInvitationDto) {
    return this.usersService.completeOrgInvitation(data);
  }

  @Get('organization')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get organization details',
    description: 'Retrieves organization details including logo and description. Accessible by all users of the organization.'
  })
  @ApiResponse({
    status: 200,
    description: 'Organization details fetched successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Organization details fetched successfully' },
        organization: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'org-uuid' },
            name: { type: 'string', example: 'Acme Corporation' },
            description: { type: 'string', example: 'A leading provider of cloud solutions', nullable: true },
            logo: { type: 'string', example: 'https://example.com/logo.png', nullable: true },
            slug: { type: 'string', example: 'acme-corp', nullable: true },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            _count: {
              type: 'object',
              properties: {
                users: { type: 'number', example: 15 },
                teams: { type: 'number', example: 3 },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User not authorized to view this organization',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: { type: 'string', example: 'You must be a member of this organization to view it' },
        error: { type: 'string', example: 'Forbidden' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Organization not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Organization not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  async getOrganization(@GetCurrentUser() user: JwtPayload) {
    return this.usersService.getOrganization(user.orgId, user.sub);
  }

  @Put('organization')
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @ApiOperation({
    summary: 'Update organization description and logo',
    description: 'Updates the description and/or logo of an organization. Only admins of the organization can perform this action.'
  })
  @ApiResponse({
    status: 200,
    description: 'Organization updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Organization updated successfully' },
        organization: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'org-uuid' },
            name: { type: 'string', example: 'Acme Corporation' },
            description: { type: 'string', example: 'Updated description', nullable: true },
            logo: { type: 'string', example: 'https://example.com/new-logo.png', nullable: true },
            slug: { type: 'string', example: 'acme-corp', nullable: true },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or no fields provided',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'At least one field (description or logo) must be provided' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User not authorized to update this organization',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: { type: 'string', example: 'You must be an admin of this organization to update it' },
        error: { type: 'string', example: 'Forbidden' },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  async updateOrganization(
    @Body() updateData: UpdateOrganizationDto,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.usersService.updateOrganization(user.orgId, updateData, user.sub);
  }

  @Get('me')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get complete user details including connectors',
    description: 'Retrieves complete user details including basic information and connected connectors. Users can only access their own details.'
  })
  @ApiResponse({
    status: 200,
    description: 'User details fetched successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'User details fetched successfully' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'user-uuid' },
            email: { type: 'string', example: '<EMAIL>' },
            name: { type: 'string', example: 'John Doe' },
            role: { type: 'string', example: 'USER' },
            organizationId: { type: 'string', example: 'org-uuid' },
            profileImageUrl: { type: 'string', example: 'https://example.com/profile.jpg', nullable: true },
            hasOnboarded: { type: 'boolean', example: true },
            onboardingCompleted: { type: 'boolean', example: true },
            connectors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  connectorId: { type: 'string', example: 'connector-uuid' },
                  name: { type: 'string', example: 'Slack' },
                  type: { type: 'string', example: 'CHAT' },
                  logo: { type: 'string', example: 'https://example.com/logo.png' },
                  category: { type: 'string', example: 'Communication' },
                  description: { type: 'string', example: 'Connect with Slack' },
                  status: { type: 'string', example: 'AVAILABLE' },
                  connectedAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' },
                  config: { type: 'object' }
                }
              }
            },
            totalConnectors: { type: 'number', example: 3 }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'User not found' },
        error: { type: 'string', example: 'Not Found' }
      }
    }
  })
  @HttpCode(HttpStatus.OK)
  async getCurrentUserDetails(@GetCurrentUser() user: JwtPayload) {
    return this.usersService.getCurrentUserDetails(user.sub);
  }
}
