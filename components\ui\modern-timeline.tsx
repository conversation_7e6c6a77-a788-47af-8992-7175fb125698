"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>Circle, ChevronRight, LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

export interface TimelineStep {
  id: string | number;
  title: string;
  description: string;
  icon: LucideIcon;
  completed: boolean;
  clickable?: boolean;
  active?: boolean;
  route?: string;
}

interface ModernTimelineProps {
  steps: TimelineStep[];
  onStepClick?: (step: TimelineStep) => void;
  className?: string;
}

export const ModernTimeline: React.FC<ModernTimelineProps> = ({
  steps,
  onStepClick,
  className
}) => {
  return (
    <div className={cn("relative", className)}>
      <div className="space-y-4">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isLast = index === steps.length - 1;
          const isFirst = index === 0;

          return (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: index * 0.1, 
                duration: 0.4,
                ease: [0.4, 0, 0.2, 1]
              }}
              className="relative group"
            >
              {!isLast && (
                <div className="absolute left-6 top-12 w-px h-8 overflow-hidden">
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: step.completed ? "100%" : "100%" }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className={cn(
                      "w-full origin-top",
                      step.completed 
                        ? "bg-gradient-to-b from-emerald-500 via-emerald-400/60 to-emerald-300/40" 
                        : "bg-gradient-to-b from-neutral-200 via-neutral-300/60 to-neutral-400/40 dark:from-neutral-700 dark:via-neutral-600/60 dark:to-neutral-500/40"
                    )}
                  />
                  
                  {step.completed && (
                    <motion.div
                      animate={{ y: ["-100%", "100%"] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="absolute top-0 left-0 w-full h-3 bg-gradient-to-b from-transparent via-emerald-400/80 to-transparent blur-[1px]"
                    />
                  )}
                </div>
              )}

              <div
                className={cn(
                  "relative flex items-start gap-4 transition-all duration-300",
                  step.clickable && !step.completed && "cursor-pointer"
                )}
                onClick={() => step.clickable && onStepClick?.(step)}
              >
                <motion.div
                  whileHover={step.clickable ? { scale: 1.1 } : {}}
                  whileTap={step.clickable ? { scale: 0.9 } : {}}
                  className="relative flex-shrink-0"
                >
                  <div
                    className={cn(
                      "relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300",
                      step.completed
                        ? "bg-emerald-500 border-emerald-400 shadow-lg shadow-emerald-500/30"
                        : step.active
                        ? "bg-white dark:bg-neutral-900 border-neutral-900 dark:border-neutral-100 shadow-lg"
                        : step.clickable
                        ? "bg-white dark:bg-neutral-900 border-neutral-300 dark:border-neutral-600 hover:border-neutral-900 dark:hover:border-neutral-100 hover:shadow-md"
                        : "bg-neutral-100 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                    )}
                  >
                    {step.completed ? (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      >
                        <CheckCircle className="w-6 h-6 text-white" />
                      </motion.div>
                    ) : (
                      <Icon
                        className={cn(
                          "w-5 h-5 transition-all duration-300",
                          step.active
                            ? "text-neutral-900 dark:text-neutral-100"
                            : step.clickable
                            ? "text-neutral-600 dark:text-neutral-400 group-hover:text-neutral-900 dark:group-hover:text-neutral-100"
                            : "text-neutral-400 dark:text-neutral-600"
                        )}
                      />
                    )}
                  </div>

                  {/* Ripple Effect for Active */}
                  {step.active && !step.completed && (
                    <div className="absolute inset-0 rounded-full">
                      <motion.div
                        animate={{ scale: [1, 1.4], opacity: [0.3, 0] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="absolute inset-0 rounded-full bg-neutral-900/20 dark:bg-neutral-100/20"
                      />
                      <motion.div
                        animate={{ scale: [1, 1.2], opacity: [0.5, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                        className="absolute inset-0 rounded-full bg-neutral-900/30 dark:bg-neutral-100/30"
                      />
                    </div>
                  )}

                  {/* Success Glow */}
                  {step.completed && (
                    <motion.div
                      animate={{ opacity: [0.5, 0.8, 0.5] }}
                      transition={{ duration: 3, repeat: Infinity }}
                      className="absolute -inset-1 rounded-full bg-emerald-500/20 blur-md"
                    />
                  )}

                  {/* Step Number Overlay */}
                  {!step.completed && !step.active && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-neutral-800 dark:bg-neutral-200 text-white dark:text-neutral-800 text-xs font-bold rounded-full flex items-center justify-center">
                      {index + 1}
                    </div>
                  )}
                </motion.div>

                {/* Content */}
                <div className="flex-1 min-w-0 pb-2">
                  <div className="flex items-center justify-between mb-1">
                    <motion.h3
                      className={cn(
                        "font-semibold text-base transition-all duration-300",
                        step.completed
                          ? "text-neutral-900 dark:text-neutral-100"
                          : step.active
                          ? "text-neutral-900 dark:text-neutral-100"
                          : step.clickable
                          ? "text-neutral-700 dark:text-neutral-300 group-hover:text-neutral-900 dark:group-hover:text-neutral-100"
                          : "text-neutral-500 dark:text-neutral-500"
                      )}
                      whileHover={step.clickable ? { x: 2 } : {}}
                    >
                      {step.title}
                    </motion.h3>

                    {/* Status Indicators */}
                    <div className="flex items-center gap-2">
                      {step.completed && (
                        <motion.span
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="text-xs font-medium text-emerald-600 dark:text-emerald-400 bg-emerald-50 dark:bg-emerald-950/50 px-2 py-1 rounded-full"
                        >
                          Done
                        </motion.span>
                      )}
                      
                      {step.active && !step.completed && (
                        <motion.div
                          animate={{ opacity: [1, 0.5, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="flex items-center gap-1"
                        >
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full" />
                          <span className="text-xs font-medium text-orange-600 dark:text-orange-400">
                            Current
                          </span>
                        </motion.div>
                      )}

                      {step.clickable && !step.completed && !step.active && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          whileHover={{ opacity: 1 }}
                          className="text-neutral-400 dark:text-neutral-600"
                        >
                          <ChevronRight className="w-4 h-4" />
                        </motion.div>
                      )}
                    </div>
                  </div>

                  <motion.p
                    className={cn(
                      "text-sm leading-relaxed transition-all duration-300",
                      step.completed || step.active
                        ? "text-neutral-600 dark:text-neutral-400"
                        : step.clickable
                        ? "text-neutral-500 dark:text-neutral-500 group-hover:text-neutral-600 dark:group-hover:text-neutral-400"
                        : "text-neutral-400 dark:text-neutral-600"
                    )}
                    whileHover={step.clickable ? { x: 2 } : {}}
                  >
                    {step.description}
                  </motion.p>

                  {step.clickable && !step.completed && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      whileHover={{ opacity: 1, height: "auto" }}
                      transition={{ duration: 0.2 }}
                      className="mt-2 overflow-hidden"
                    >
                      <div className="flex items-center gap-1 text-xs text-neutral-500 dark:text-neutral-500">
                        <motion.div
                          animate={{ x: [0, 2, 0] }}
                          transition={{ duration: 1.5, repeat: Infinity }}
                        >
                          →
                        </motion.div>
                        Click to proceed
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>

              {isFirst && (
                <div className="absolute -left-1 top-0 bottom-0 w-1 bg-gradient-to-b from-transparent via-neutral-200 to-transparent dark:via-neutral-700 rounded-full opacity-30" />
              )}
            </motion.div>
          );
        })}
      </div>

      {steps.every(step => step.completed) && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mt-6 p-4 bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/30 dark:to-teal-950/30 rounded-xl border border-emerald-200 dark:border-emerald-800"
        >
          <div className="flex items-center gap-3">
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center"
            >
              <CheckCircle className="w-5 h-5 text-white" />
            </motion.div>
            <div>
              <h4 className="font-semibold text-emerald-900 dark:text-emerald-100">
                All steps completed! 🎉
              </h4>
              <p className="text-sm text-emerald-700 dark:text-emerald-300">
                Great work! You've successfully finished the entire process.
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ModernTimeline;