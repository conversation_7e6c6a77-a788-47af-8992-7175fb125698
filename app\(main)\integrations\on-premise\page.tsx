"use client"
import React from 'react';
import ComingSoon from '@/components/ComingSoon';
import { Shield, Lock, Database, Cpu, Network } from 'lucide-react';

const OnPremiseDeployment = () => {
    const features = [
        {
            icon: <Shield />,
            title: "Data Sovereignty",
            description: "Maintain complete control over your data with on-premise deployment"
        },
        {
            icon: <Lock />,
            title: "Enterprise Security",
            description: "Bank-grade encryption and security measures to protect sensitive information"
        },
        {
            icon: <Database />,
            title: "Custom Storage",
            description: "Use your preferred storage solution and database infrastructure"
        },
        {
            icon: <Cpu />,
            title: "Resource Control",
            description: "Optimize hardware allocation and scaling based on your needs"
        },
        {
            icon: <Network />,
            title: "Network Isolation",
            description: "Keep all processing within your secure network boundary"
        }
    ];

    return (
        <ComingSoon 
            title="On-Premise Deployment"
            subtitle="Deploy and manage AIDE within your own infrastructure with complete control and security"
            features={features}
        />
    );
};

export default OnPremiseDeployment;