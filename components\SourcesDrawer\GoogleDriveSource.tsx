import Image from 'next/image';
import { FileText, FileImage, FileVideo, FileAudio, File } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface GoogleDriveSourceProps {
  source: {
    id: string;
    name: string;
    content: string;
    mime_type?: string;
    url?: string;
    web_view_link?: string;
    size?: number;
    modified_time?: string;
    created_time?: string;
    title?: string;
    owners?: any[];
    shared?: boolean;
    icon_link?: string;
  };
}

export function GoogleDriveSource({ source }: GoogleDriveSourceProps) {
  const [iconError, setIconError] = useState(false);

  const getPreview = (text: string) => {
    return text.split(/\s+/).slice(0, 20).join(' ') + '...';
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (e) {
      return dateStr;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (!bytes) return '';
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const getFileIcon = (mimeType?: string) => {
    if (!mimeType) return <File className="w-4 h-4 text-blue-500" />;
    
    if (mimeType.startsWith('image/')) {
      return <FileImage className="w-4 h-4 text-green-500" />;
    } else if (mimeType.startsWith('video/')) {
      return <FileVideo className="w-4 h-4 text-red-500" />;
    } else if (mimeType.startsWith('audio/')) {
      return <FileAudio className="w-4 h-4 text-purple-500" />;
    } else if (mimeType.includes('text') || mimeType.includes('document')) {
      return <FileText className="w-4 h-4 text-blue-500" />;
    } else {
      return <File className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleClick = () => {
    // Prefer web_view_link over url for Google Drive files
    const linkToOpen = source.web_view_link || source.url;
    if (linkToOpen) {
      window.open(linkToOpen, '_blank', 'noopener noreferrer');
    }
  };

  return (
    <div 
      className="p-2 border-b border-border last:border-0 hover:bg-accent/50 transition-colors cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-start gap-2">
        <div className="mt-1 flex-shrink-0">
          <div className="w-4 h-4 rounded-full overflow-hidden bg-muted flex items-center justify-center">
            {source.icon_link && !iconError ? (
              <img
                src={source.icon_link}
                alt={`${source.mime_type} icon`}
                className="w-full h-full object-contain"
                onError={() => setIconError(true)}
              />
            ) : (
              getFileIcon(source.mime_type)
            )}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-foreground truncate">
            {source.title || source.name}
          </h4>
          <div className="flex items-center gap-2 mt-0.5">
            <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 whitespace-nowrap">
              Google Drive
            </span>
            {source.size && (
              <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-muted/50 text-muted-foreground/70 whitespace-nowrap">
                {formatFileSize(source.size)}
              </span>
            )}
            {source.modified_time && (
              <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-muted/50 text-muted-foreground/70 whitespace-nowrap">
                {formatDate(source.modified_time)}
              </span>
            )}
          </div>
          <p className={cn(
            "text-xs text-muted-foreground mt-1 line-clamp-2",
            !source.name && "mt-0"
          )}>
            {getPreview(source.content)}
          </p>
        </div>
      </div>
    </div>
  );
}
