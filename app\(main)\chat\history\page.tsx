"use client"

import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@steps-ai/ui"
import { ScrollArea } from "@steps-ai/ui"
import { Avatar, AvatarFallback } from "@steps-ai/ui"
import { Skeleton } from "@steps-ai/ui"
import { Button } from "@steps-ai/ui"
import { Separator } from "@steps-ai/ui"
import { Plus, MessageSquare, RefreshCcw, Search, Star } from "lucide-react"
import Link from 'next/link'
import { chatClient } from '@/lib/chatClient'
import { ChatSessionResponse } from '@/types/chat'
import { Input } from "@steps-ai/ui"
import { ChatLoader } from "@/components/ui/chat-loader";
import { AnimatePresence } from "framer-motion";
import { getChats } from '@/lib/aiClient'
import { toast } from 'sonner'

const ChatHistoryList = () => {
    const [chats, setChats] = useState<any[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<Error | null>(null)
    const [searchQuery, setSearchQuery] = useState("")

    const fetchChats = async () => {
        try {
            setIsLoading(true);
            const response = await getChats();
            setChats(response.chats || []);
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error : new Error('Failed to fetch chats'));
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchChats();
    }, []);

    const formatDate = (dateString: string) => {
        try {
            const date = dateString ? new Date(dateString) : new Date('2024-01-01');
            const now = new Date();
            const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
            const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

            if (diffInMinutes < 1) {
                return 'Just now';
            } else if (diffInMinutes < 60) {
                return `${diffInMinutes}m ago`;
            } else if (diffInMinutes < 24 * 60 && date.getDate() === now.getDate()) {
                return 'Today';
            } else if (diffInDays === 1) {
                return 'Yesterday';
            } else if (diffInDays < 7) {
                return date.toLocaleDateString('en-US', { weekday: 'long' });
            } else {
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }
        } catch (error) {
            return 'Jan 1, 2024';
        }
    }

    const truncateMessage = (message: string | undefined, limit: number = 200) => {
        if (!message) return 'No messages yet';
        return message.length > limit ? `${message.slice(0, limit)}...` : message;
    };

    const filteredChats = chats.filter(chat =>
        chat.title?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
        <div className="flex justify-center w-full h-[90vh] md:p-6">
            <div className="w-full h-full max-w-full">
                <Card className="h-full border-none shadow-none bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <CardHeader className="space-y-4 p-4">
                        <div className="flex flex-col space-y-2">
                            <div>
                                <CardTitle className="text-xl font-bold">Chat History</CardTitle>
                                <CardDescription className="text-sm text-muted-foreground">
                                    View and manage your previous conversations
                                </CardDescription>
                            </div>
                            <div className="flex items-center justify-between gap-2">
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => fetchChats()}
                                    disabled={isLoading}
                                    className="shrink-0"
                                >
                                    <RefreshCcw className="h-4 w-4" />
                                </Button>
                                <Link href="/chat" className="shrink-0">
                                    <Button>
                                        <Plus className="h-4 w-4 mr-2" />
                                        New Chat
                                    </Button>
                                </Link>
                            </div>
                        </div>
                        <div className="relative w-full">
                            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input
                                placeholder="Search conversations..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-9 pr-4"
                            />
                        </div>
                    </CardHeader>
                    <Separator />
                    <CardContent className="p-0 flex-1 h-[calc(90vh-180px)]">
                        <ScrollArea className="h-full w-full">
                            {error ? (
                                <div className="flex flex-col items-center justify-center p-4 text-center">
                                    <div className="text-red-500 mb-2 text-sm sm:text-base">Error loading chat history</div>
                                    <p className="text-sm sm:text-base text-muted-foreground mb-2">{error.message}</p>
                                    <Button variant="outline" onClick={() => fetchChats()}>
                                        Try Again
                                    </Button>
                                </div>
                            ) : isLoading ? (
                                <div className="space-y-4 p-4">
                                    {[...Array(5)].map((_, i) => (
                                        <div key={i} className="flex items-start space-x-4 animate-pulse">
                                            <div className="h-10 w-10 sm:h-12 sm:w-12 rounded-full bg-muted"></div>
                                            <div className="flex-1 space-y-2">
                                                <div className="h-4 bg-muted rounded w-1/3"></div>
                                                <div className="h-3 bg-muted rounded w-full"></div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : filteredChats.length > 0 ? (
                                <div className="grid grid-cols-1 gap-0">
                                    {filteredChats.map((chat: any) => (
                                        <div key={chat.chat_id} className="group relative flex items-center p-4 hover:bg-muted/50 transition-all">
                                            <Link href={`/chat/${chat.chat_id}`} className="flex items-center space-x-4 flex-1 min-w-0">
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center justify-between mb-1">
                                                        <h3 className="font-semibold text-sm sm:text-base truncate pr-2">
                                                            {chat.title || 'Untitled Chat'}
                                                        </h3>
                                                        <span className="text-xs sm:text-sm text-muted-foreground flex-shrink-0">
                                                            {formatDate(chat.updated_at)}
                                                        </span>
                                                    </div>
                                                    <p className="text-sm text-muted-foreground truncate">
                                                        {truncateMessage(chat.last_message)}
                                                    </p>
                                                </div>
                                            </Link>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center p-4 text-center">
                                    <MessageSquare className="h-12 w-12 sm:h-16 sm:w-16 text-muted-foreground mb-4" />
                                    <p className="text-sm sm:text-base text-muted-foreground mb-4">No chats found</p>
                                    <Link href="/chat">
                                        <Button variant="outline" className="h-9 sm:h-10">
                                            Start a New Chat
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </ScrollArea>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

export default function Page() {
    return <ChatHistoryList />
}