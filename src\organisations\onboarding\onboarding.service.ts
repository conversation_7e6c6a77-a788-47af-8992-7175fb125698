import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { UpcomingMeetingsResponseDto, MeetingDto } from './dtos/onboarding.dto';
import { GoogleCalendarService } from 'src/connectors/providers/google/calendar/calendar.v1.service';
import { ActivityLoggerService } from 'src/activities/activity-logger.service';

@Injectable()
export class OnboardingService {
  constructor(
    private prisma: PrismaService,
    private googleCalendarService: GoogleCalendarService,
    private activityLogger: ActivityLoggerService,
  ) {}

  async getUpcomingMeetings(userId: string, currentUserId: string): Promise<UpcomingMeetingsResponseDto> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        knowledgeBases: {
          include: {
            files: true
          }
        },
        onboardingStatus: true,
        connectors: {
          where: {
            connector: {
              name: 'Google Calendar'
            },
            config: {
              not: null
            }
          },
          include: {
            connector: true
          }
        }
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if current user belongs to the same organization
    const currentUser = await this.prisma.user.findUnique({
      where: { id: currentUserId },
    });

    if (!currentUser || currentUser.organizationId !== user.organizationId) {
      throw new UnauthorizedException('You can only view onboarding status of users in your organization');
    }

    const hasCreatedChat = user.knowledgeBases.length > 0;
    const hasCompletedOnboarding = user.onboardingCompleted || user.onboardingStatus?.hasCompletedOnboarding || false;
    const hasConnectedCalendar = user.connectors.length > 0;

    if (hasCompletedOnboarding && !user.onboardingStatus?.hasCompletedOnboarding) {
      await this.activityLogger.logOnboardingCompleted(
        user.organizationId,
        userId,
        user.name
      );
    }

    if (hasConnectedCalendar && !user.onboardingStatus?.hasConnectedCalendar) {
      const calendarConnector = user.connectors[0];
      await this.activityLogger.logConnectorConnected(
        user.organizationId,
        userId,
        user.name,
        calendarConnector.connector.name,
        { connectorId: calendarConnector.id }
      );
    }

    if (hasCreatedChat && !user.onboardingStatus?.hasCreatedChat) {
      const knowledgeBase = user.knowledgeBases[0];
      await this.activityLogger.logKnowledgeBaseCreated(
        user.organizationId,
        userId,
        user.name,
        knowledgeBase.name,
        knowledgeBase.id
      );

      if (knowledgeBase.files.length > 0) {
        for (const file of knowledgeBase.files) {
          await this.activityLogger.logFileUploaded(
            user.organizationId,
            userId,
            user.name,
            file.name,
            file.id,
            { knowledgeBaseId: knowledgeBase.id, knowledgeBaseName: knowledgeBase.name }
          );
        }
      }
    }

    let upcomingMeetings: MeetingDto[] = [];
    if (hasConnectedCalendar) {
      try {
        upcomingMeetings = await this.googleCalendarService.getUpcomingMeetings(userId);
      } catch (error) {
        console.error('Error fetching upcoming meetings:', error);
      }
    }

    return {
      success: true,
      message: 'Onboarding status retrieved successfully',
      hasCreatedChat,
      hasCompletedOnboarding,
      hasConnectedCalendar,
      finalVerdict: hasCreatedChat && hasCompletedOnboarding && hasConnectedCalendar,
      upcomingMeetings,
    };
  }
} 