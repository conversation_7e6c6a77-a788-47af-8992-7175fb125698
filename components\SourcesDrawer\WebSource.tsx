import { Globe } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image'; // Import Image component for Next.js optimization

interface WebSearchResult {
  url: string;
  title: string;
  content: string;
  score: number;
}

interface WebSourceProps {
  source: {
    type: string;
    url: string;
    title: string;
    content: string;
    score: number;
  };
}

export function WebSource({ source }: WebSourceProps) {
  const getPreview = (text: string) => {
    // Limit preview to first 30 words
    return text.split(/\s+/).slice(0, 30).join(' ') + (text.split(/\s+/).length > 30 ? '...' : '');
  };

  // Safely extract domain from URL
  let domain = 'Unknown Domain';
  let safeUrl = '#';
  let faviconUrl = ''; // Initialize faviconUrl

  if (source?.url) {
    try {
      const urlObj = new URL(source.url);
      domain = urlObj.hostname;
      safeUrl = source.url;
      // Use a service like Google's S2 for favicons
      faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    } catch (e) {
      console.error('Error parsing URL:', source.url, e);
      // If URL is invalid, try to extract domain using regex as a fallback
      const domainMatch = source.url.match(/^(?:https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:\/\n?]+)/im);
      domain = domainMatch ? domainMatch[1] : source.url;
      // Ensure URL is properly formatted for linking, but favicon might still fail
      safeUrl = source.url.startsWith('http') ? source.url : `https://${source.url}`;
       faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=32`; // Still attempt favicon
    }
  }

  return (
    <div className="p-2 border-b border-border last:border-0 hover:bg-accent/50 transition-colors cursor-pointer" onClick={() => window.open(safeUrl, '_blank', 'noopener noreferrer')}>
      <div className="flex items-start gap-2">
        <div className="mt-1 flex-shrink-0">
           {faviconUrl ? (
              <div className="w-4 h-4 rounded-full overflow-hidden bg-muted flex items-center justify-center">
                 {/* Using regular img tag for external favicon service */}
                 <img 
                    src={faviconUrl} 
                    alt={`${domain} favicon`} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                        // Fallback to a default icon if favicon fails to load
                        const target = e.target as HTMLImageElement;
                        target.onerror = null; // Prevent infinite loop
                        target.parentElement!.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe text-muted-foreground"><circle cx="12" cy="12" r="10"/><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/><path d="M2 12h20"/></svg>'; // Replace img with Globe icon
                    }}
                 />
              </div>
           ) : (
              // Default icon if no URL or domain found
              <Globe className="w-4 h-4 text-muted-foreground" />
           )}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-foreground truncate">
            {source.title}
          </h4>
           <p className="text-xs text-muted-foreground mt-0.5 truncate">
            {domain}
          </p>
          <p className={cn("text-xs text-muted-foreground mt-1 line-clamp-2", !source.title && "mt-0")}>
            {getPreview(source.content)}
          </p>
        </div>
      </div>
    </div>
  );
} 