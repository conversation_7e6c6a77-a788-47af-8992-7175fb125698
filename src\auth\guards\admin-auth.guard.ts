import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class AdminAuthGuard extends AuthGuard('jwt') {
  constructor(private prismaService: PrismaService) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isAuthenticated = await super.canActivate(context);
    if (!isAuthenticated) {
      return false;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.sub) {
      throw new UnauthorizedException('User authentication failed');
    }

    const userId = request.user.sub;

    const dbUser = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!dbUser || !(dbUser.role == 'ADMIN' || dbUser.role == 'SUPERADMIN' || dbUser.role == 'TEAMADMIN')) {
      throw new UnauthorizedException('Admin access required');
    }

    return true;
  }
}
