"use client"

import * as React from "react"
import { ChevronsUpDown, Plus } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
    SidebarHeader
} from "@/components/ui/sidebar"
export function LogoSwitcher() {
    const { state } = useSidebar();
    return (
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    {state === "collapsed" ? (
                        <SidebarMenuButton size="lg" className="hover:bg-transparent items-center justify-center">
                            <Image
                                src="/new-logo.svg"
                                alt="AI Logo"
                                width={30}
                                height={30}
                                className="min-w-[30px] items-center justify-center"
                            />
                        </SidebarMenuButton>
                    ) : (
                        <SidebarMenuButton size="lg" className="hover:bg-transparent">
                            <Link href="/chat" className="flex  flex-col justify-start items-center px-4 md:px-6 lg:px-10">
                                <div className="flex items-center gap-2 w-full">
                                    <Image
                                        src="/new-logo.svg"
                                        alt="AI Logo"
                                        width={40}
                                        height={40}
                                        className="min-w-[40px]"
                                    />
                                    <span className="text-lg md:text-xl lg:text-2xl font-bold bg-gradient-to-r from-[#C95158] to-[#4C68A8] bg-clip-text text-transparent whitespace-nowrap">
                                        AIDE
                                    </span>
                                </div>
                                <div className="flex flex-col items-start w-full">
                                    <span className="text-xs text-muted-foreground -mt-1 ml-[40px] pl-2">
                                        by STEPS AI
                                    </span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    )}
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>
    )
}
