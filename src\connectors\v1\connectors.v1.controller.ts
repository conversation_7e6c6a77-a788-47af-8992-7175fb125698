import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  HttpCode,
  UseGuards,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { ConnectorsService } from './connectors.v1.service';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { CreateConnectorDto } from '../dtos/addConnectors.dto';
import { UpdateStatusDto } from '../dtos/updateStatus.dto';
import {
  OrganizationConnectedConnectorsResponseDto,
  UserConnectedConnectorsResponseDto,
} from '../dtos/connected-connectors.dto';

@ApiTags('Connectors-V1')
@Controller({ version: '1', path: 'connectors' })
export class ConnectorsController {
  constructor(private connectorsService: ConnectorsService) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all connectors' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Connectors fetched successfully',
  })
  @ApiQuery({
    name: 'teamId',
    required: false,
    description: 'Team ID to filter connectors',
  })
  @ApiQuery({
    name: 'orgId',
    required: false,
    description: 'Organization ID to filter connectors',
  })
  @HttpCode(HttpStatus.OK)
  async getConnectors(
    @GetCurrentUser() user: JwtPayload,
    @Query('teamId') teamId?: string,
    @Query('orgId') orgId?: string,
  ) {
    return this.connectorsService.getConnectors(user.sub, teamId, orgId);
  }

  @Get(':slug')
  @ApiOperation({ summary: 'Get connector by slug' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Connector fetched successfully',
  })
  @HttpCode(HttpStatus.OK)
  async getConnectorById(
    @GetCurrentUser() user: JwtPayload,
    @Param('slug') connectorslug: string,
  ) {
    return this.connectorsService.connectorByslug(user.sub, connectorslug);
  }

  @Post()
  @ApiOperation({ summary: 'Create connectors' })
  @HttpCode(HttpStatus.OK)
  async createConnectors(@Body() data: CreateConnectorDto) {
    return this.connectorsService.createConnectors(data);
  }

  @Post('status')
  @ApiOperation({ summary: 'Update status' })
  @HttpCode(HttpStatus.OK)
  async updateStatus(@Body() data: UpdateStatusDto) {
    return this.connectorsService.updateStatus(data);
  }

  @Get('organization/connected')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get all connected connectors for organization (Admin level)',
    description: `
      Retrieve all connected connectors for all users in an organization.
      Only accessible by organization admins and team admins.

      Returns detailed information about:
      - All users in the organization
      - Their connected connectors with details
      - Summary statistics of connector usage
      - Total connections count
    `,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Organization connected connectors retrieved successfully',
    type: OrganizationConnectedConnectorsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Organization not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Only admins can view organization-wide connector information',
  })
  @HttpCode(HttpStatus.OK)
  async getOrganizationConnectedConnectors(
    @GetCurrentUser() user: JwtPayload,
  ): Promise<OrganizationConnectedConnectorsResponseDto> {
    return this.connectorsService.getOrganizationConnectedConnectors(
      user.orgId,
      user.sub,
    );
  }

  @Get('user/:userId/connected')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get connected connectors for a specific user (User level)',
    description: `
      Retrieve all connected connectors for a specific user.

      Access permissions:
      - Users can view their own connected connectors
      - Admins and team admins can view connectors for users in their organization

      Returns detailed information about:
      - User details
      - All connected connectors with configuration
      - Connection timestamps
      - Total connector count
    `,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User connected connectors retrieved successfully',
    type: UserConnectedConnectorsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Not authorized to view this user\'s connectors',
  })
  @HttpCode(HttpStatus.OK)
  async getUserConnectedConnectors(
    @Param('userId') userId: string,
    @GetCurrentUser() user: JwtPayload,
  ): Promise<UserConnectedConnectorsResponseDto> {
    return this.connectorsService.getUserConnectedConnectors(userId, user.sub);
  }
}
