"use client"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@steps-ai/ui"
import { Input } from "@steps-ai/ui"
import { ScrollArea } from "@steps-ai/ui"
import { Skeleton } from "@steps-ai/ui"
import { Users, Plus, UserPlus, Loader2 } from 'lucide-react'
import Image from "next/image"
import { motion, Variants } from "framer-motion";
import { useApiMutation } from "@/lib/apiClient"
import { queryClient } from "@/lib/apiClient"
import { createMutationFn } from "@/lib/apiClient"
import { toast } from "sonner"

interface TeamMember {
    id: number;
    name: string;
    email: string;
    profileImageUrl: string | null;
}

const TeamMemberSkeleton = () => (
    <li className="flex items-center justify-between rounded-lg border border-gray-200 dark:border-gray-800 px-4 py-3">
        <div className="flex items-center">
            <Skeleton className="h-10 w-10 rounded-full dark:bg-gray-800" />
            <div className="ml-3 space-y-2">
                <Skeleton className="h-4 w-32 dark:bg-gray-800" />
                <Skeleton className="h-3 w-24 dark:bg-gray-800" />
            </div>
        </div>
        <Skeleton className="h-8 w-8 rounded-full dark:bg-gray-800" />
    </li>
);

const OwnerSkeleton = () => (
    <div className="flex items-center">
        <Skeleton className="h-10 w-10 rounded-full dark:bg-gray-800" />
        <div className="ml-3 space-y-2">
            <Skeleton className="h-4 w-40 dark:bg-gray-800" />
            <Skeleton className="h-3 w-32 dark:bg-gray-800" />
        </div>
    </div>
);

export default function ShareKnowledgebase({ data, isLoading }: { data: any, isLoading: boolean }) {
    const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
    const [owner, setOwner] = useState<TeamMember | null>(null)
    const [inviteEmail, setInviteEmail] = useState<string>('')
    const [loading, setLoading] = useState(false)

    useEffect(() => {
        if (data) {
            setOwner(data.result.owner)
            setTeamMembers(data.result.members)
        }
    }, [data]);

    const handleInviteEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setInviteEmail(e.target.value)
    }

    const sendInvite = useApiMutation(
        createMutationFn.post('/knowledgebase/share'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({
                        queryKey: [
                            "knowledgebasedata",
                            data.result.id?.toString() ?? "",
                            "1", // page
                            "10", // pageSize
                            "", // search
                            "name", // sortBy
                            "asc" // sortOrder
                        ]
                    });
                    toast.success("Invite sent successfully");
                    setLoading(false);
                } else {
                    setLoading(false)
                    toast.error("Something went wrong. Please try again.");
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again.";
                    toast.error(errorMessage);
                    setLoading(false)
                } else if (error.request) {
                    toast.error("No response received from server");
                    setLoading(false)
                } else {
                    toast.error("Error setting up the request");
                    setLoading(false)
                }
            }
        }
    );



    const handleSendInvite = () => {
        setLoading(true)
        sendInvite.mutate({
            email: inviteEmail,
            kid: data.result.id
        })
    }

    return (
        <div className="px-2 sm:px-4 mt-5">
            <div className="flex flex-col lg:flex-row justify-between gap-6 mb-6">
                <div className="space-y-2">
                    <h2 className="text-xl sm:text-2xl font-semibold tracking-tight text-text-primary dark:text-gray-100 flex items-center gap-2">
                        <Users className="h-6 w-6 text-text-tertiary dark:text-gray-400 flex-shrink-0" />
                        <span className="truncate">Share with your team</span>
                    </h2>
                    <p className="text-sm text-text-secondary dark:text-gray-400">
                        Invite team members to collaborate on this knowledge base.
                    </p>
                </div>

                <div className="flex flex-col items-start lg:items-end gap-2">
                    <h2 className="text-gray-500 dark:text-gray-400 font-bold">Owner</h2>
                    {isLoading ? (
                        <OwnerSkeleton />
                    ) : (owner && (
                        <div className="flex items-center p-2 rounded-lg border border-gray-100 dark:border-gray-800 dark:bg-gray-900">
                            <Image
                                src={owner.profileImageUrl || '/ai-logo.svg'}
                                alt={owner.name}
                                width={40}
                                height={40}
                                className="rounded-full flex-shrink-0"
                            />
                            <div className="ml-3 overflow-hidden">
                                <p className="text-sm font-bold text-gray-900 dark:text-gray-100 truncate">{owner.name}</p>
                                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{owner.email}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <div className="mt-6 space-y-6">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col gap-4">
                        <h2 className="text-gray-500 dark:text-gray-400 font-bold">Members</h2>
                        <div className="flex flex-col sm:flex-row gap-3">
                            <div className="relative flex-1">
                                <Input
                                    type="email"
                                    placeholder="Enter an email"
                                    className="w-full"
                                    value={inviteEmail}
                                    onChange={handleInviteEmailChange}
                                    disabled={loading}
                                />
                            </div>
                            <Button
                                className="relative bg-gradient-to-r from-[#2c487c] to-[#3B6BB5] text-white 
                            transition-all duration-200 shadow-lg  pr-6 pl-4"
                                onClick={handleSendInvite}
                                disabled={loading || !inviteEmail}
                            >

                                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Send invite"}
                            </Button>
                        </div>
                    </div>

                    <ScrollArea className="max-h-[45vh] overflow-y-auto scroll-smooth">
                        {isLoading ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <TeamMemberSkeleton />
                                <TeamMemberSkeleton />
                                <TeamMemberSkeleton />
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                {teamMembers && teamMembers.length > 0 ? (
                                    teamMembers.map((member) => (
                                        <li
                                            key={member.id}
                                            className="flex items-center justify-between rounded-lg border border-gray-200 dark:border-gray-800 dark:bg-gray-900 p-3"
                                        >
                                            <div className="flex items-center min-w-0">
                                                <Image
                                                    src={member.profileImageUrl || '/ai-logo.svg'}
                                                    alt={member.name}
                                                    width={32}
                                                    height={32}
                                                    className="rounded-full flex-shrink-0"
                                                />
                                                <div className="ml-3 overflow-hidden">
                                                    <p className="text-sm font-medium text-text-primary dark:text-gray-100 truncate">
                                                        {member.name}
                                                    </p>
                                                    <p className="text-xs text-text-secondary dark:text-gray-400 truncate">
                                                        {member.email}
                                                    </p>
                                                </div>
                                            </div>
                                        </li>
                                    ))
                                ) : (
                                    <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                                        <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                                            <UserPlus className="h-8 w-8 text-gray-500 dark:text-gray-400" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-text-primary dark:text-gray-100">
                                            No team members yet
                                        </h3>
                                        <p className="mt-2 text-sm text-text-tertiary dark:text-gray-400 max-w-md">
                                            Invite your team to collaborate and share knowledge
                                        </p>
                                    </div>
                                )}
                            </div>
                        )}
                    </ScrollArea>
                </div>
            </div>
        </div>
    )
}