import {
  <PERSON>,
  Get,
  MaxFileSizeValidator,
  ParseFilePipe,
  Post,
  Req,
  UploadedFiles,
  UseInterceptors,
  Body,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { CommonService } from './common.service';

@ApiTags('Common')
@Controller({ path: 'common' })
export class CommonController {
  constructor(private commonService: CommonService) { }

  @Post('upload-files')
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFiles(
    @UploadedFiles(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 1000 * 1000 * 1000 })],
      }),
    )
    files: Express.Multer.File[],
    @Req() req: any,
  ) {
    const folderName = req.body.folderName || 'default-folder';
    const uploadResults = await this.commonService.uploadFilesToS3(
      files,
      folderName,
    );
    return {
      message: 'Files uploaded successfully',
      uploadResults,
    };
  }

  @Post('generate-presigned-urls')
  async generatePresignedUrls(
    @Body() body: { files: { name: string; type: string }[]; folderName?: string }
  ) {
    const folder = body.folderName || 'default-folder';
    const urls = await Promise.all(
      body.files.map(file =>
        this.commonService.getPresignedUrl(`${folder}/${this.sanitizeFileName(file.name)}`, file.type)
      )
    );
    return { urls };
  }


  private sanitizeFileName(originalName: string): string {
    return originalName
      .normalize('NFC')
      .replace(/[^\p{L}\p{N}._-]/gu, '_')
      .replace(/^\.+/, '')
      .replace(/\.{2,}/g, '.')
      .trim()
      .substring(0, 255);
  }
}
