'use client';

import { type Invitation } from '@/lib/api/organization';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Clock,
  Mail,
  MoreHorizontal,
  RotateCcw,
  X,
  Loader2,
  User,
  Building,
  Calendar
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface InvitationCardProps {
  invitation: Invitation;
  onCancel: (invitationId: string) => void;
  onResend: (invitationId: string) => void;
  isActionLoading: boolean;
  canPerformActions: boolean;
}

export default function InvitationCard({
  invitation,
  onCancel,
  onResend,
  isActionLoading,
  canPerformActions
}: InvitationCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const isExpiringSoon = () => {
    const expiryDate = new Date(invitation.expiresAt);
    const now = new Date();
    const diffInDays = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffInDays <= 3 && diffInDays > 0;
  };

  const isExpired = () => {
    const expiryDate = new Date(invitation.expiresAt);
    const now = new Date();
    return expiryDate < now;
  };

  return (
    <div className="p-4 hover:bg-muted/30 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1">
          {/* User Avatar */}
          <Avatar className="h-10 w-10 border-2 border-border">
            <AvatarFallback className="bg-primary/10 text-primary">
              <User className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <p className="font-medium text-sm truncate">{invitation.email}</p>
              <Badge
                variant="secondary"
                className={`text-xs px-2 py-0.5 h-5 ${
                  isExpired()
                    ? 'bg-red-100 dark:bg-red-950/30 text-red-700 dark:text-red-300'
                    : isExpiringSoon()
                    ? 'bg-yellow-100 dark:bg-yellow-950/30 text-yellow-700 dark:text-yellow-300'
                    : 'bg-orange-100 dark:bg-orange-950/30 text-orange-700 dark:text-orange-300'
                }`}
              >
                <Clock className="h-3 w-3 mr-1" />
                {isExpired() ? 'Expired' : 'Pending'}
              </Badge>
            </div>

            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Mail className="h-3 w-3" />
                <span>Invited by {invitation.inviter.name}</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Sent {formatDate(invitation.createdAt)}</span>
              </div>
              {invitation.type === 'TEAM' && invitation.team && (
                <div className="flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  <span>Team: {invitation.team.name}</span>
                </div>
              )}
            </div>

            <div className="mt-1">
              <p className="text-xs text-muted-foreground">
                Expires: {formatDate(invitation.expiresAt)}
                {isExpiringSoon() && !isExpired() && (
                  <span className="text-yellow-600 ml-1 font-medium">(Expires soon)</span>
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        {canPerformActions && (
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-muted"
                  disabled={isActionLoading}
                >
                  {isActionLoading ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <MoreHorizontal className="h-3 w-3" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem
                  onClick={() => onResend(invitation.id)}
                  disabled={isActionLoading}
                  className="text-sm"
                >
                  <RotateCcw className="h-3 w-3 mr-2" />
                  Resend
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onCancel(invitation.id)}
                  disabled={isActionLoading}
                  className="text-sm text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                >
                  <X className="h-3 w-3 mr-2" />
                  Cancel
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    </div>
  );
}
