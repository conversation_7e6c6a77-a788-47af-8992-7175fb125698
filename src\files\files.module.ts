import { Module } from '@nestjs/common';
import { FilesService } from './v1/files.v1.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { FileController } from './v1/files.v1.controller';
import { CommonService } from 'src/common/common.service';

@Module({
  imports: [PrismaModule],
  controllers: [FileController],
  providers: [FilesService, CommonService],
})
export class FilesModule {}
