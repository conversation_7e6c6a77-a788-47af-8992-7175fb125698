import { apiRouter } from './apiRouter';
import type {
  Team,
  TeamMember,
  OrganizationUser,
  CreateTeamRequest,
  InviteUsersRequest,
  UpdateUserRoleRequest,
  AddMemberToTeamRequest,
  BulkAddMembersRequest,
  BulkAddMembersResult,
  AssignTeamLeaderRequest,
  Invitation,
  InvitationsResponse,
  GetInvitationsParams,
  CancelInvitationRequest,
  ResendInvitationRequest,
  InvitationActionResponse,
  ConnectorConfig,
  Connector,
  UserConnectors,
  OrganizationConnectors,
  UpdateOrganizationRequest,
  OrganizationDetails,
  OnboardingStatus,
  Meeting,
  OnboardingStatusResponse,
  OrganizationDetailsResponse,
  ActivityFeedItem,
  ActivityFeedResponse,
  GetActivityFeedParams,
  UserMeResponse,
  GetUserMeApiResponse
} from '../../types/organization';

export const organizationApi = {
  async getTeams(): Promise<Team[]> {
    const response = await apiRouter.mainApi.get<{ result?: Team[]; data?: Team[] }>(`teams/org`);
    return response.data.result || response.data.data || response.data as any;
  },

  async createTeam(data: CreateTeamRequest): Promise<Team> {
    const { organizationId, ...teamData } = data;
    const response = await apiRouter.mainApi.post<{ result?: Team; data?: Team }>(`teams`, teamData);
    return response.data.result || response.data.data || response.data as any;
  },

  async updateTeam(teamId: string, data: Partial<CreateTeamRequest>): Promise<Team> {
    const response = await apiRouter.mainApi.put<{ result?: Team; data?: Team }>(`teams/${teamId}`, data);
    return response.data.result || response.data.data || response.data as any;
  },

  async deleteTeam(teamId: string): Promise<void> {
    await apiRouter.mainApi.delete(`teams/${teamId}`);
  },

  async getTeam(teamId: string): Promise<Team> {
    const response = await apiRouter.mainApi.get<{ result?: { team?: Team }; team?: Team; data?: Team }>(`teams/${teamId}`);
    return response.data.result?.team || response.data.team || response.data.result as any || response.data.data as any;
  },

  async getTeamMembers(teamId: string): Promise<TeamMember[]> {
    const response = await apiRouter.mainApi.get<{ members?: TeamMember[]; result?: TeamMember[]; data?: TeamMember[] }>(`teams/${teamId}`);
    return response.data.members || response.data.result || response.data.data as any;
  },

  async addTeamMember(teamId: string, data: AddMemberToTeamRequest): Promise<void> {
    await apiRouter.mainApi.post(`teams/${teamId}/members`, data);
  },

  async removeTeamMember(teamId: string, memberId: string): Promise<void> {
    await apiRouter.mainApi.delete(`teams/${teamId}/members/${memberId}`);
  },

  async bulkAddMembersToTeam(data: BulkAddMembersRequest): Promise<{ results: BulkAddMembersResult[] }> {
    const response = await apiRouter.mainApi.post<{ results: BulkAddMembersResult[] }>(`teams/members/bulk-add`, data);
    return response.data;
  },

  async assignTeamLeader(teamId: string, data: AssignTeamLeaderRequest): Promise<void> {
    await apiRouter.mainApi.post(`teams/${teamId}/assign-leader`, data);
  },

  async getOrganizationUsers(): Promise<OrganizationUser[]> {
    const response = await apiRouter.mainApi.get<{ result?: OrganizationUser[]; data?: OrganizationUser[] }>(`users/org`);
    return response.data.result || response.data.data as any;
  },

  async inviteUsers(data: InviteUsersRequest): Promise<void> {
    const { organizationId, ...inviteData } = data;
    await apiRouter.mainApi.post(`users/invite-bulk`, {
      emails: inviteData.emails
    });
  },

  async updateUserRole(userId: string, role: 'ADMIN' | 'TEAMADMIN' | 'USER'): Promise<void> {
    await apiRouter.mainApi.post(`users/update-role`, {
      userId,
      role
    });
  },

  async removeUser(userId: string): Promise<void> {
    await apiRouter.mainApi.delete(`users/${userId}`);
  },

  async  getUserMe(): Promise<UserMeResponse> {
    const response = await apiRouter.mainApi.get<{ 
      result: { user: UserMeResponse } 
    }>('users/me');

    return response.data.result.user;
  },


  async getUserTeams(userId: string): Promise<Team[]> {
    const response = await apiRouter.mainApi.get<{ result?: Team[]; data?: Team[] }>(`users/${userId}/teams`);
    return response.data.result || response.data.data as any;
  },

  async getOrganizationDetails(): Promise<OrganizationDetails> {
    const response = await apiRouter.mainApi.get<{
      result?: {
        organization?: OrganizationDetails;
      };
      organization?: OrganizationDetails;
      data?: OrganizationDetails;
    }>(`users/organization`);
    if (response.data.result?.organization) {
      return response.data.result.organization;
    }
    if (response.data.organization) {
      return response.data.organization;
    }
    return response.data.data as OrganizationDetails;
  },

  async updateOrganization(data: UpdateOrganizationRequest): Promise<OrganizationDetails> {
    const response = await apiRouter.mainApi.put<{
      result?: {
        organization?: OrganizationDetails;
      };
      organization?: OrganizationDetails;
      data?: OrganizationDetails;
    }>(`users/organization`, data);
    
    if (response.data.result?.organization) {
      return response.data.result.organization;
    }
    if (response.data.organization) {
      return response.data.organization;
    }
    return response.data.data as OrganizationDetails;
  },

  async getInvitations(params: GetInvitationsParams): Promise<InvitationsResponse> {
    const queryParams = new URLSearchParams();
    if (params.status) queryParams.append('status', params.status);
    if (params.type) queryParams.append('type', params.type);

    const url = `invitations?${queryParams.toString()}`;
    const response = await apiRouter.mainApi.get<InvitationsResponse>(url);
    return response.data;
  },

  async cancelInvitation(request: CancelInvitationRequest): Promise<InvitationActionResponse> {
    const response = await apiRouter.mainApi.post<InvitationActionResponse>(`invitations/cancel`, request);
    return response.data;
  },

  async resendInvitation(request: ResendInvitationRequest): Promise<InvitationActionResponse> {
    const response = await apiRouter.mainApi.post<InvitationActionResponse>(`invitations/resend`, request);
    return response.data;
  },

  async getOrganizationConnectors(): Promise<OrganizationConnectors> {
    const response = await apiRouter.mainApi.get<OrganizationConnectors>(`connectors/organization/connected`);
    return response.data;
  },

  async getUserConnectors(userId: string): Promise<UserConnectors> {
    const response = await apiRouter.mainApi.get<UserConnectors>(`connectors/user/${userId}/connected`);
    return response.data;
  },

  async getOnboardingStatus(userId: string): Promise<OnboardingStatusResponse> {
    const response = await apiRouter.mainApi.get<OnboardingStatusResponse>(`onboarding/${userId}/meetings`);
    return response.data;
  },

  async getActivityFeed(params: GetActivityFeedParams): Promise<ActivityFeedResponse> {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.type) queryParams.append('type', params.type);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    const url = `activities${queryString}`;
    const response = await apiRouter.mainApi.get<ActivityFeedResponse>(url);
    return response.data;
  }
};

export default organizationApi;

export type {
  Team,
  TeamMember,
  OrganizationUser,
  CreateTeamRequest,
  InviteUsersRequest,
  UpdateUserRoleRequest,
  AddMemberToTeamRequest,
  BulkAddMembersRequest,
  BulkAddMembersResult,
  AssignTeamLeaderRequest,
  Invitation,
  InvitationsResponse,
  GetInvitationsParams,
  CancelInvitationRequest,
  ResendInvitationRequest,
  InvitationActionResponse,
  ConnectorConfig,
  Connector,
  UserConnectors,
  OrganizationConnectors,
  UpdateOrganizationRequest,
  OrganizationDetails,
  OnboardingStatus,
  Meeting,
  OnboardingStatusResponse,
  OrganizationDetailsResponse,
  ActivityFeedItem,
  ActivityFeedResponse,
  GetActivityFeedParams,
  UserMeResponse,
} from '../../types/organization';
