import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsUUID, IsArray, IsOptional } from 'class-validator';

export class AddUserEmailDto {
  @ApiProperty({
    description: 'User email',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Organization ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  organizationId?: string;

  @ApiProperty({
    description: 'ID of the user sending the invitation',
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  invitedBy?: string;
}

export class BulkAddUserEmailDto {
  @ApiProperty({
    description: 'Organization ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  organizationId?: string;

  @ApiProperty({
    description: 'List of user emails',
    required: true,
    type: [String],
  })
  @IsArray()
  @IsEmail({}, { each: true })
  @IsNotEmpty()
  emails: string[];

  @ApiProperty({
    description: 'ID of the user sending the invitations',
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  invitedBy?: string;
}

export class VerifyOrgInvitationDto {
  @ApiProperty({
    description: 'Organization invitation token',
    required: true,
  })
  @IsNotEmpty()
  token: string;
}

export class CompleteOrgInvitationDto {
  @ApiProperty({
    description: 'Organization invitation token',
    required: true,
  })
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'User name',
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'User password',
    required: true,
  })
  @IsNotEmpty()
  password: string;
}

export class VerifyTokenDto {
  @ApiProperty({
    description: 'Token to verify (can be admin invitation or organization invitation token)',
    required: true,
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @IsNotEmpty()
  token: string;
}
