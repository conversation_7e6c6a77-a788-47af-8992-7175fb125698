import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator';

export class SlackCredentialsDto {
    @ApiProperty({
        description: 'The Slack team ID',
        example: 'T01234567',
    })
    @IsString()
    @IsNotEmpty()
    team_id: string;

    @ApiProperty({
        description: 'The Slack user ID',
        example: 'U01234567',
    })
    @IsString()
    @IsNotEmpty()
    user_id: string;

    @ApiProperty({
        description: 'The Slack team name',
        example: 'Acme Inc',
    })
    @IsString()
    @IsNotEmpty()
    team_name: string;

    @ApiProperty({
        description: 'Timestamp when the integration was created',
        example: 1623456789000,
    })
    @IsNumber()
    created_at: number;

    @ApiProperty({
        description: 'The Slack scopes granted to the integration',
        example: 'chat:write,channels:read',
    })
    @IsString()
    scope: string;
}

export class SlackAuthUrlResponseDto {
    @ApiProperty({
        description: 'The authorization URL for Slack OAuth',
        example: 'https://slack.com/oauth/v2/authorize?client_id=...',
    })
    authUrl: string;
}

export class SlackCallbackDto {
    @ApiProperty({
        description: 'The authorization code from Slack',
        example: 'xoxe-1234567890',
    })
    @IsString()
    @IsNotEmpty()
    code: string;

    @ApiProperty({
        description: 'The state parameter to prevent CSRF attacks',
        example: 'user-123:abcdef1234567890',
    })
    @IsString()
    @IsNotEmpty()
    state: string;
}

export class SlackTeamIdDto {
    @ApiProperty({
        description: 'The Slack team ID',
        example: 'T01234567',
    })
    @IsString()
    @IsNotEmpty()
    team_id: string;
}

export class SlackRemoveIntegrationDto {
    @ApiProperty({
        description: 'The Slack team ID',
        example: 'T01234567',
    })
    @IsString()
    @IsNotEmpty()
    team_id: string;
}

export class SlackOAuthCallbackResponseDto {
    @ApiProperty({
        description: 'Whether the operation was successful',
        example: true,
    })
    success: boolean;

    @ApiProperty({
        description: 'The Slack team information',
        example: { id: 'T01234567', name: 'Acme Inc' },
        required: false,
    })
    @IsOptional()
    team?: {
        id: string;
        name: string;
    };

    @ApiProperty({
        description: 'Error message if the operation failed',
        example: 'Failed to complete Slack integration',
        required: false,
    })
    @IsOptional()
    error?: string;
}

export class SlackCredentialsResponseDto {
    @ApiProperty({
        description: 'Whether the operation was successful',
        example: true,
    })
    success: boolean;

    @ApiProperty({
        description: 'List of Slack integrations',
        type: [SlackCredentialsDto],
        required: false,
    })
    @IsOptional()
    integrations?: SlackCredentialsDto[];

    @ApiProperty({
        description: 'Error message if the operation failed',
        example: 'No Slack integration found for this team',
        required: false,
    })
    @IsOptional()
    error?: string;
}

export class SlackRemoveIntegrationResponseDto {
    @ApiProperty({
        description: 'Whether the operation was successful',
        example: true,
    })
    success: boolean;

    @ApiProperty({
        description: 'Success message',
        example: 'Slack integration removed successfully',
        required: false,
    })
    @IsOptional()
    message?: string;

    @ApiProperty({
        description: 'Error message if the operation failed',
        example: 'Failed to remove Slack integration',
        required: false,
    })
    @IsOptional()
    error?: string;
} 