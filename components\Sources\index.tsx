"use client"
import useChatStore from '@/store/chatStore'
import React, { useEffect } from 'react'
import { Button } from '../ui/button'
import { X } from 'lucide-react'
import KnowledgebaseSources from './agents/knowledgebase'
import { ScrollArea } from '../ui/scroll-area'

const Sources = () => {
    const { messages, sourcesViewId, setSourcesViewId } = useChatStore();
    const message = messages.find(message => message.id === sourcesViewId);
    const rawSources = message?.sources;
    let sources: any[] = [];
    if (typeof rawSources === "string") {
        try {
            sources = JSON.parse(rawSources);
        } catch (e) {
            sources = [];
        }
    } else if (Array.isArray(rawSources)) {
        sources = rawSources;
    } else {
        sources = [];
    }


    return (
        <div className="flex h-[92vh] flex-col gap-2 w-full p-4  rounded-xl ">
            <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-semibold">Sources</h2>
                <Button onClick={() => setSourcesViewId("")} variant="ghost" size="icon">
                    <X className="w-4 h-4" />
                </Button>
            </div>
            {sources.length === 0 ? (
                <div className="text-muted-foreground">No sources found for this message.</div>
            ) : (
                <ScrollArea className="h-[92vh]">
                    <div className="flex flex-col gap-2">
                        {sources.map((source, idx) =>
                            source.Agent === "Knowledge Base" ? <KnowledgebaseSources key={idx} sources={source.sources} /> : null
                        )}
                    </div>
                </ScrollArea>
            )}
        </div>
    )
}

export default Sources
