"use client"

import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Sparkles,
  User,
  Settings2,
  BarChart2,
  LifeBuoy,
  Send,
  BookOpen
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useState } from "react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { VerifyEmail } from "../Modal/VerifyEmail"
import { signOut } from "next-auth/react"
import { useApiQuery } from "@/lib/apiClient"
import { Skeleton } from "@/components/ui/skeleton"
import { useEffect } from "react"
import AppWalkthrough from "../Walkthrough"
import { SupportDialog } from "@/components/Dialogs/SupportDialog";
import { FeedbackDialog } from "@/components/Dialogs/FeedbackDialog";
import { useUser } from "@/contexts/UserContext"
import { ProductUpdates } from "../Upgrade"

export function NavUser() {
  const { isMobile } = useSidebar()
  const { user, isLoading } = useUser()
  const [showSupportModal, setShowSupportModal] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  const renderUserAvatar = () => {
    if (isLoading) {
      return <Skeleton className="h-8 w-8 rounded-full" />;
    }

    if (user?.image) {
      return (
        <Image
          src={user?.image}
          alt={user?.name || ""}
          height={40}
          width={40}
          className="h-8 w-8 rounded-full object-cover"
        />
      );
    }

    return (
      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
        <User className="h-4 w-4 text-primary" />
      </div>
    );
  };

  const renderUserInfo = () => {
    if (isLoading) {
      return (
        <div className="grid flex-1 gap-1">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-3 w-32" />
        </div>
      );
    }

    return (
      <div className="grid flex-1 text-left text-sm leading-tight">
        <span className="truncate font-semibold">{user?.name}</span>
        <span className="truncate text-xs">{user?.email}</span>
      </div>
    );
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex items-center gap-2 flex-1">
                {renderUserAvatar()}
                {renderUserInfo()}
              </div>
              {/* <ChevronsUpDown className="ml-auto size-4" /> */}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5">
                {renderUserAvatar()}
                {renderUserInfo()}
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <Link href={`https://docs.stepsai.co/`} target="_blank">
                <DropdownMenuItem className="cursor-pointer">
                  <BookOpen className="mr-2 h-4 w-4" />
                  Documentation
                </DropdownMenuItem>
              </Link>
             
              <DropdownMenuItem className="cursor-pointer">
                <LifeBuoy className="mr-2 h-4 w-4" />
                Support
              </DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer">
                <Send className="mr-2 h-4 w-4" />
                Feedback
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => signOut()} className="cursor-pointer">
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
      <SupportDialog
        open={showSupportModal}
        onOpenChange={setShowSupportModal}
      />

      <FeedbackDialog
        open={showFeedbackModal}
        onOpenChange={setShowFeedbackModal}
      />
    </SidebarMenu>
  );
}