import sitemap from '../../sitemap'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const sitemapData = await sitemap()
    if (!sitemapData || sitemapData.length === 0) {
      throw new Error('No sitemap data generated')
    }

    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="/sitemap.xsl"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${sitemapData.map(entry => {
  const url = entry.url.startsWith('http') ? entry.url : `http://localhost:3000${entry.url}`
  return `
  <url>
    <loc>${url}</loc>
    <lastmod>${entry.lastModified instanceof Date ? entry.lastModified.toISOString() : entry.lastModified}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
    <description>${entry.description || ''}</description>
  </url>`}).join('')}
</urlset>`.trim()


    return new NextResponse(xmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
      },
    })
  } catch (error) {
    // console.error('Sitemap generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate sitemap', details: error },
      { status: 500 }
    )
  }
} 