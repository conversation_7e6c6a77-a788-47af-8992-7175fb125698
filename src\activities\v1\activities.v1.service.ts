import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { ActivityType } from '@prisma/client';
import {
  GetActivitiesResponseDto,
  ActivityLogDto,
  CreateActivityLogDto,
} from '../dtos/activities.dto';

@Injectable()
export class ActivitiesService {
  constructor(private prisma: PrismaService) {}

  async getActivities(
    organizationId: string,
    requestingUserId: string,
  ): Promise<GetActivitiesResponseDto> {
    // Verify requesting user belongs to the organization
    const requestingUser = await this.prisma.user.findUnique({
      where: { id: requestingUserId },
      select: { organizationId: true },
    });

    if (!requestingUser) {
      throw new NotFoundException('User not found');
    }

    if (requestingUser.organizationId !== organizationId) {
      throw new ForbiddenException(
        'You can only view activities for your own organization',
      );
    }

    // Verify organization exists
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
      select: { id: true, name: true },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Get total count
    const totalActivities = await this.prisma.activityLog.count({
      where: { organizationId },
    });

    // Fetch top 10 recent activities with user details
    const activities = await this.prisma.activityLog.findMany({
      where: { organizationId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            profileImageUrl: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
    });

    // Transform to DTOs
    const activityDtos: ActivityLogDto[] = activities.map((activity) => ({
      id: activity.id,
      organizationId: activity.organizationId,
      user: activity.user
        ? {
            id: activity.user.id,
            name: activity.user.name || 'Unknown User',
            email: activity.user.email,
            profileImageUrl: activity.user.profileImageUrl || undefined,
            role: activity.user.role,
          }
        : undefined,
      activityType: activity.activityType,
      description: activity.description,
      metadata: activity.metadata,
      ipAddress: activity.ipAddress || undefined,
      userAgent: activity.userAgent || undefined,
      createdAt: activity.createdAt,
    }));

    return {
      activities: activityDtos,
      totalActivities,
    };
  }

  async createActivityLog(data: CreateActivityLogDto): Promise<void> {
    try {
      // Verify organization exists
      const organization = await this.prisma.organization.findUnique({
        where: { id: data.organizationId },
        select: { id: true },
      });

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      // Verify user exists if userId is provided
      if (data.userId) {
        const user = await this.prisma.user.findUnique({
          where: { id: data.userId },
          select: { id: true, organizationId: true },
        });

        if (!user) {
          throw new NotFoundException('User not found');
        }

        if (user.organizationId !== data.organizationId) {
          throw new BadRequestException(
            'User does not belong to the specified organization',
          );
        }
      }

      // Create activity log
      await this.prisma.activityLog.create({
        data: {
          organizationId: data.organizationId,
          userId: data.userId,
          activityType: data.activityType,
          description: data.description,
          metadata: data.metadata,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
        },
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      // Log the error but don't throw to avoid breaking the main operation
      console.error('Failed to create activity log:', error);
    }
  }

  // Helper method to create activity logs with common patterns
  async logUserActivity(
    organizationId: string,
    userId: string,
    activityType: ActivityType,
    description: string,
    metadata?: any,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    await this.createActivityLog({
      organizationId,
      userId,
      activityType,
      description,
      metadata,
      ipAddress,
      userAgent,
    });
  }

  // Helper method for system activities (no user)
  async logSystemActivity(
    organizationId: string,
    activityType: ActivityType,
    description: string,
    metadata?: any,
  ): Promise<void> {
    await this.createActivityLog({
      organizationId,
      activityType,
      description,
      metadata,
    });
  }

  // Helper methods for specific activity types
  async logUserJoinedOrganization(
    organizationId: string,
    userId: string,
    userName: string,
    metadata?: any,
  ): Promise<void> {
    await this.logUserActivity(
      organizationId,
      userId,
      ActivityType.USER_JOINED_ORGANIZATION,
      `${userName} joined the organization`,
      metadata,
    );
  }

  async logUserLeftOrganization(
    organizationId: string,
    userId: string,
    userName: string,
    metadata?: any,
  ): Promise<void> {
    await this.logUserActivity(
      organizationId,
      userId,
      ActivityType.USER_LEFT_ORGANIZATION,
      `${userName} left the organization`,
      metadata,
    );
  }

  async logUserRoleChanged(
    organizationId: string,
    userId: string,
    userName: string,
    previousRole: string,
    newRole: string,
    changedBy?: string,
  ): Promise<void> {
    await this.logUserActivity(
      organizationId,
      changedBy || userId,
      ActivityType.USER_ROLE_CHANGED,
      `${userName}'s role was changed from ${previousRole} to ${newRole}`,
      { userId, previousRole, newRole, changedBy },
    );
  }

  async logTeamCreated(
    organizationId: string,
    userId: string,
    userName: string,
    teamName: string,
    teamId: string,
  ): Promise<void> {
    await this.logUserActivity(
      organizationId,
      userId,
      ActivityType.TEAM_CREATED,
      `${userName} created team "${teamName}"`,
      { teamId, teamName },
    );
  }

  async logConnectorConnected(
    organizationId: string,
    userId: string,
    userName: string,
    connectorName: string,
    metadata?: any,
  ): Promise<void> {
    await this.logUserActivity(
      organizationId,
      userId,
      ActivityType.CONNECTOR_CONNECTED,
      `${userName} connected ${connectorName}`,
      { connectorName, ...metadata },
    );
  }

  async logOnboardingCompleted(
    organizationId: string,
    userId: string,
    userName: string,
  ): Promise<void> {
    await this.logUserActivity(
      organizationId,
      userId,
      ActivityType.ONBOARDING_COMPLETED,
      `${userName} completed onboarding`,
    );
  }
}
