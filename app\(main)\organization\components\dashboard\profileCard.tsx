"use client"
import Image from "next/image"
import { Card } from "@/components/ui/card"
import { useUser } from "@/contexts/UserContext"

export default function ProfileCard() {
    const { user } = useUser()
    return (
        <div className="flex p-4">
            <Card className="bg-white shadow-lg items-center justify-center flex bg-transparent flex-col gap-2 p-4">
                <Image src={user?.image || "/profile-image.png"} alt="Profile picture" width={100} height={100} className="rounded-full" />

                <div className="flex items-end justify-between">
                    <div>
                        <h2 className="text-sm font-semibold mb-1">{user?.name}</h2>
                        <p className="text-xs">{user?.email}</p>
                    </div>
                </div>
            </Card>
        </div>
    )
}
