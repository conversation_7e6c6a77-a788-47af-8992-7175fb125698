'use client'

import { motion } from "framer-motion"

export default function WelcomeScreen({ user }: { user: string }) {
    return (
        <div className="max-w-4xl mx-auto w-full flex flex-col gap-3 items-start justify-center  md:mt-20 mt-10 px-4">
            <motion.h1
                className="text-2xl sm:text-3xl md:text-5xl font-semibold  bg-gradient-to-r from-[#DE4D4D] via-[#9C5876] via-[#746091] to-[#3B6BB5] text-transparent bg-clip-text"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
            >
                Hello, {user.split(" ")[0]}
            </motion.h1>
            <motion.h1
                className="text-2xl sm:text-3xl md:text-4xl  text-gray-800 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
            >
                how can i help you today?
            </motion.h1>
        </div>
    )
}

