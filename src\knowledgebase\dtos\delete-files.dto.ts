import {
  IsString,
  IsArray,
  Validate<PERSON>ested,
  IsNumber,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class FileDto {
  @ApiProperty({ description: 'File ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'URL of the file' })
  @IsString()
  url: string;
}

export class DeleteFilesDto {
  @ApiProperty({ description: 'ID of the knowledge base' })
  @IsString()
  kid: string;

  @ApiProperty({ type: [FileDto], description: 'Array of file metadata' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}
