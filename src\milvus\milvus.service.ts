import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class MilvusService {
  private readonly baseUrl: string;

  constructor(private configService: ConfigService) {
    this.baseUrl =
      this.configService.get('milvus.baseUrl') ||
      'http://localhost:19530/v2/vectordb';
  }

  async createCollection(params: {
    collectionName: string;
    dimension?: number;
  }) {
    const { collectionName, dimension = 3072 } = params;
    try {
      const listResponse = await fetch(`${this.baseUrl}/collections/list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (!listResponse.ok) {
        throw new Error('Failed to list collections');
      }

      const collections = await listResponse.json();
      if (collections.data.includes(collectionName)) {
        return false;
      }

      const createResponse = await fetch(`${this.baseUrl}/collections/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dimension: dimension,
          collectionName: collectionName,
          schema: {
            fields: [
              {
                fieldName: 'id',
                dataType: 'VarChar',
                isPrimary: true,
                elementTypeParams: {
                  max_length: 65535,
                },
              },
              {
                fieldName: 'embedding',
                dataType: 'FloatVector',
                elementTypeParams: {
                  dim: dimension,
                },
              },
              {
                fieldName: 'kid',
                dataType: 'VarChar',
                elementTypeParams: {
                  max_length: 65535,
                },
              },
            ],
          },
          enable_dynamic_field: true,
        }),
      });

      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        throw new Error(
          `Failed to create collection: ${errorData.message || createResponse.statusText}`,
        );
      }

      const indexConfig = {
        fieldName: 'embedding',
        indexName: 'embedding',
        index_type: 'FLAT',
        metricType: 'IP',
        params: {},
      };

      const indexResponse = await fetch(`${this.baseUrl}/indexes/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          collectionName: collectionName,
          indexParams: [indexConfig],
        }),
      });

      if (!indexResponse.ok) {
        let errorText = await indexResponse.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { message: errorText };
        }
        throw new Error(
          `Failed to create index: ${errorData.message || indexResponse.statusText}`,
        );
      }
      return true;
    } catch (error) {
      console.error(`❌ Error creating/accessing collection: ${error.message}`);
      throw error;
    }
  }
}
