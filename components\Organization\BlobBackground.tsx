'use client';

import React from 'react';

interface BlobBackgroundProps {
    children: React.ReactNode;
    colorScheme?: 'red-blue' | 'blue-purple' | 'green-teal' | 'purple-pink';
    className?: string;
}

export default function BlobBackground({
    children,
    colorScheme = 'red-blue',
    className = ''
}: BlobBackgroundProps) {
    const getColorScheme = () => {
        switch (colorScheme) {
            case 'red-blue':
                return {
                    gradient: {
                        light: 'from-red-200/30 via-transparent to-blue-200/30',
                        dark: 'from-red-600/20 via-transparent to-blue-600/20',
                    },
                    blob1: {
                        light: 'from-red-300/40 to-red-400/30',
                        dark: 'from-red-500/40 to-red-600/30',
                    },
                    blob2: {
                        light: 'from-blue-300/40 to-blue-400/30',
                        dark: 'from-blue-500/40 to-blue-600/30',
                    },
                    blob3: {
                        light: 'from-purple-200/30 to-indigo-200/30',
                        dark: 'from-purple-500/30 to-indigo-500/30',
                    },
                };
            case 'blue-purple':
                return {
                    gradient: {
                        light: 'from-blue-200/30 via-transparent to-purple-200/30',
                        dark: 'from-blue-600/20 via-transparent to-purple-600/20',
                    },
                    blob1: {
                        light: 'from-blue-300/40 to-blue-400/30',
                        dark: 'from-blue-500/40 to-blue-600/30',
                    },
                    blob2: {
                        light: 'from-purple-300/40 to-purple-400/30',
                        dark: 'from-purple-500/40 to-purple-600/30',
                    },
                    blob3: {
                        light: 'from-indigo-200/30 to-cyan-200/30',
                        dark: 'from-indigo-500/30 to-cyan-500/30',
                    },
                };
            case 'green-teal':
                return {
                    gradient: {
                        light: 'from-green-200/30 via-transparent to-teal-200/30',
                        dark: 'from-green-600/20 via-transparent to-teal-600/20',
                    },
                    blob1: {
                        light: 'from-green-300/40 to-green-400/30',
                        dark: 'from-green-500/40 to-green-600/30',
                    },
                    blob2: {
                        light: 'from-teal-300/40 to-teal-400/30',
                        dark: 'from-teal-500/40 to-teal-600/30',
                    },
                    blob3: {
                        light: 'from-emerald-200/30 to-cyan-200/30',
                        dark: 'from-emerald-500/30 to-cyan-500/30',
                    },
                };
            case 'purple-pink':
                return {
                    gradient: {
                        light: 'from-purple-200/30 via-transparent to-pink-200/30',
                        dark: 'from-purple-600/20 via-transparent to-pink-600/20',
                    },
                    blob1: {
                        light: 'from-purple-300/40 to-purple-400/30',
                        dark: 'from-purple-500/40 to-purple-600/30',
                    },
                    blob2: {
                        light: 'from-pink-300/40 to-pink-400/30',
                        dark: 'from-pink-500/40 to-pink-600/30',
                    },
                    blob3: {
                        light: 'from-violet-200/30 to-fuchsia-200/30',
                        dark: 'from-violet-500/30 to-fuchsia-500/30',
                    },
                };
            default:
                return {
                    gradient: {
                        light: 'from-red-200/30 via-transparent to-blue-200/30',
                        dark: 'from-red-600/20 via-transparent to-blue-600/20',
                    },
                    blob1: {
                        light: 'from-red-300/40 to-red-400/30',
                        dark: 'from-red-500/40 to-red-600/30',
                    },
                    blob2: {
                        light: 'from-blue-300/40 to-blue-400/30',
                        dark: 'from-blue-500/40 to-blue-600/30',
                    },
                    blob3: {
                        light: 'from-purple-200/30 to-indigo-200/30',
                        dark: 'from-purple-500/30 to-indigo-500/30',
                    },
                };
        }
    };

    const colors = getColorScheme();

    return (
        <div className={`bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-900 dark:to-slate-800 relative overflow-hidden flex items-center justify-center p-8 ${className}`}>
            {/* Grainy Gradient Overlay */}
            <div
                className={`absolute inset-0 bg-gradient-to-br ${colors.gradient.light} dark:${colors.gradient.dark} opacity-60`}
                style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.1'/%3E%3C/svg%3E")`,
                    backgroundSize: '200px 200px'
                }}
            />

            {/* Organic Blob Shapes */}
            <div className="absolute inset-0">
                {/* Primary Blob */}
                <div
                    className={`absolute top-1/4 left-1/4 w-24 h-32 bg-gradient-to-br ${colors.blob1.light} dark:${colors.blob1.dark} opacity-80 blur-xl transform rotate-12`}
                    style={{
                        borderRadius: '60% 40% 30% 70% / 60% 30% 70% 40%',
                        animation: 'morphBlob1 8s ease-in-out infinite alternate'
                    }}
                />

                {/* Secondary Blob */}
                <div
                    className={`absolute bottom-1/3 right-1/4 w-28 h-20 bg-gradient-to-br ${colors.blob2.light} dark:${colors.blob2.dark} opacity-70 blur-lg transform -rotate-45`}
                    style={{
                        borderRadius: '30% 70% 70% 30% / 30% 30% 70% 70%',
                        animation: 'morphBlob2 10s ease-in-out infinite alternate-reverse'
                    }}
                />

                {/* Tertiary Blob */}
                <div
                    className={`absolute top-1/2 right-1/3 w-20 h-24 bg-gradient-to-br ${colors.blob3.light} dark:${colors.blob3.dark} opacity-60 blur-lg transform rotate-90`}
                    style={{
                        borderRadius: '60% 40% 40% 60% / 60% 60% 40% 40%',
                        animation: 'morphBlob3 12s ease-in-out infinite'
                    }}
                />
            </div>

            {/* Content */}
            <div className="relative z-10">
                {children}
            </div>

            {/* Keyframes for Blob Morphing */}
            <style jsx>{`
        @keyframes morphBlob1 {
          0%, 100% { 
            border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
            transform: translate(0, 0) rotate(12deg);
          }
          50% { 
            border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
            transform: translate(10px, -10px) rotate(22deg);
          }
        }
        @keyframes morphBlob2 {
          0%, 100% { 
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            transform: translate(0, 0) rotate(-45deg);
          }
          50% { 
            border-radius: 70% 30% 50% 50% / 60% 70% 40% 30%;
            transform: translate(-8px, 8px) rotate(-35deg);
          }
        }
        @keyframes morphBlob3 {
          0%, 100% { 
            border-radius: 60% 40% 40% 60% / 60% 60% 40% 40%;
            transform: translate(0, 0) rotate(90deg);
          }
          33% { 
            border-radius: 40% 60% 60% 40% / 40% 40% 60% 60%;
            transform: translate(5px, -5px) rotate(95deg);
          }
          66% { 
            border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
            transform: translate(-5px, 5px) rotate(85deg);
          }
        }
      `}</style>
        </div>
    );
}