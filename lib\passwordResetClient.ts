"use client";

import axios from 'axios';

const passwordResetApiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
    },
});

interface IPasswordResetOperations {
    requestReset(email: string): Promise<{ status: boolean; message: string }>;
    verifyToken(token: string): Promise<{ status: boolean; user: any }>;
    resetPassword(token: string, newPassword: string): Promise<{ status: boolean; message: string }>;
}

class PasswordResetClient implements IPasswordResetOperations {
    private static instance: PasswordResetClient;

    private constructor() {}

    public static getInstance(): PasswordResetClient {
        if (!PasswordResetClient.instance) {
            PasswordResetClient.instance = new PasswordResetClient();
        }
        return PasswordResetClient.instance;
    }

    async requestReset(email: string): Promise<{ status: boolean; message: string }> {
        try {
            const response = await passwordResetApiClient.post('/auth/password-reset/request', { email });
            return response.data;
        } catch (error: any) {
            throw error.response?.data || error;
        }
    }

    async verifyToken(token: string): Promise<{ status: boolean; user: any }> {
        try {
            const response = await passwordResetApiClient.post('/auth/password-reset/verify-token', { token });
            return response.data;
        } catch (error: any) {
            throw error.response?.data || error;
        }
    }

    async resetPassword(token: string, newPassword: string): Promise<{ status: boolean; message: string }> {
        try {
            const response = await passwordResetApiClient.post('/auth/password-reset/reset', {
                token,
                newPassword
            });
            return response.data;
        } catch (error: any) {
            throw error.response?.data || error;
        }
    }
}

export const passwordResetClient = PasswordResetClient.getInstance(); 