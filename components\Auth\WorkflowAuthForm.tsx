import { useState, Suspense } from "react";
import { AuthForm } from "@/components/Forms/AuthForm";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { passwordResetClient } from '@/lib/passwordResetClient';
import { motion } from "framer-motion";
import { Mail, ArrowLeft, LockKeyhole, ShieldCheck, Building, Globe } from "lucide-react";
import Image from "next/image";

const CompanyInfo = {
    name: "STEPS AI",
    logo: "/new-logo.svg",
    tagline: "Empowering Education and Enterprise with Intelligent AI Solutions",
    description: "Our platform connects seamlessly with your favorite tools to enhance productivity and streamline workflows.",
};

const ForgotPasswordForm = ({ onBack }: { onBack: () => void }) => {
    const [email, setEmail] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        try {
            const response = await passwordResetClient.requestReset(email);
            toast.success("Password reset link has been sent to your email");
            setIsLoading(false);
            onBack();
        } catch (error: any) {
            setIsLoading(false);
            toast.error(error.message || "Failed to send reset email");
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
        >
            <div className="space-y-3 text-center">
                <div className="inline-flex justify-center items-center w-16 h-16 bg-gradient-to-br from-[#C95158]/10 to-[#4C68A8]/10 rounded-full mb-2">
                    <LockKeyhole className="w-8 h-8 text-[#4C68A8]" />
                </div>
                <h1 className="text-2xl font-semibold tracking-tight bg-gradient-to-r from-[#C95158] to-[#4C68A8] bg-clip-text text-transparent">
                    Reset Password
                </h1>
                <p className="text-sm text-muted-foreground max-w-xs mx-auto">
                    Enter your email address and we'll send you a link to reset your password
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5">
                <div className="space-y-3">
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <Mail className="h-5 w-5 text-gray-400" />
                        </div>
                        <Input
                            id="email"
                            placeholder="<EMAIL>"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            className="w-full pl-10 pr-4 py-2 transition-all duration-200 border rounded-lg focus:ring-2 focus:ring-[#4C68A8]/50 focus:border-transparent shadow-sm"
                        />
                    </div>
                </div>
                <div className="pt-3">
                    <Button
                        type="submit"
                        className="w-full bg-gradient-to-r from-[#C95158] to-[#4C68A8] hover:opacity-90 transition-opacity shadow-md py-6 font-medium"
                        disabled={isLoading || !email}
                    >
                        {isLoading ? "Sending..." : "Send Reset Link"}
                    </Button>
                </div>
            </form>

            <div className="text-center pt-2">
                <Button
                    variant="ghost"
                    className="text-sm text-gray-500 hover:text-gray-700 transition-colors inline-flex items-center gap-1"
                    onClick={onBack}
                >
                    <ArrowLeft className="w-4 h-4" /> Back to Login
                </Button>
            </div>
        </motion.div>
    );
};

export default function WorkflowAuthForm() {
    const [showForgotPassword, setShowForgotPassword] = useState(false);

    const formVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }
    };

    const LoadingSpinner = () => (
        <div className="flex items-center justify-center h-40">
            <div className="relative">
                <div className="w-12 h-12 rounded-full border-4 border-gray-200 dark:border-gray-700"></div>
                <div className="absolute top-0 left-0 w-12 h-12 rounded-full border-t-4 border-l-4 border-r-4 border-transparent animate-spin"
                    style={{
                        borderImageSource: 'linear-gradient(to right, #C95158, #4C68A8)',
                        borderImageSlice: 1,
                    }}
                ></div>
            </div>
        </div>
    );

    return (
        <div className="flex w-full flex-col justify-center text-gray-900 dark:text-gray-200">
            <div className="mb-6 text-center">
                <div className="flex justify-center mb-3">
                    <Image
                        src={CompanyInfo.logo}
                        alt={CompanyInfo.name}
                        width={60}
                        height={60}
                        className="rounded-xl"
                    />
                </div>
                <h2 className="text-2xl font-bold mb-1 bg-gradient-to-r from-[#C95158] to-[#4C68A8] bg-clip-text text-transparent">
                    {CompanyInfo.name}
                </h2>
                <p className="text-sm text-muted-foreground max-w-md mx-auto mb-1">
                    {CompanyInfo.tagline}
                </p>
            </div>

            <div className="mx-auto flex w-full flex-col justify-center space-y-6">
                <div className="w-full bg-white dark:bg-gray-800/30 rounded-xl shadow-sm p-6 border border-gray-100 dark:border-gray-700">
                    <Suspense fallback={<LoadingSpinner />}>
                        {showForgotPassword ? (
                            <ForgotPasswordForm onBack={() => setShowForgotPassword(false)} />
                        ) : (
                            <motion.div
                                initial="hidden"
                                animate="visible"
                                variants={formVariants}
                                className="space-y-5"
                            >
                                <AuthForm />
                                <div className="pt-3 text-center">
                                    <Button
                                        variant="ghost"
                                        className="text-sm text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1"
                                        onClick={() => setShowForgotPassword(true)}
                                    >
                                        Forgot your password?
                                    </Button>
                                </div>
                            </motion.div>
                        )}
                    </Suspense>
                </div>

                <div className="pt-4">
                    <div className="flex items-center justify-center gap-2 pb-4">
                        <div className="h-[1px] bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-800 to-transparent w-1/4"></div>
                        <div className="flex items-center gap-1.5">
                            <ShieldCheck className="w-4 h-4 text-gray-400" />
                            <span className="text-xs text-gray-400 font-medium">Protected & Secured</span>
                        </div>
                        <div className="h-[1px] bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-800 to-transparent w-1/4"></div>
                    </div>
                    <p className="text-center text-xs text-gray-500 max-w-sm mx-auto">
                        By signing up, you agree to our{" "}
                        <a
                            href="/terms-of-service/"
                            target="_blank"
                            className="underline underline-offset-4 hover:text-gray-900 transition-colors"
                        >
                            Terms of Service
                        </a>{" "}
                        and{" "}
                        <a
                            href="/privacy-policy/"
                            target="_blank"
                            className="underline underline-offset-4 hover:text-gray-900 transition-colors"
                        >
                            Privacy Policy
                        </a>
                        .
                    </p>
                </div>
            </div>
        </div>
    );
}