'use client';

import React, { useState, useEffect } from 'react';
import { organizationApi, type OrganizationUser } from '@/lib/api/organization';
import { useUser } from '@/contexts/UserContext';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  User,
  Crown,
  Shield,
  Users,
  Clock,
  CheckCircle,
  Grid3X3,
  List,
  Search
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import SkeletonCard from '@/components/ui/skeletonCard';
import { Input } from '@/components/ui/input';
import UserDetailDrawer from './userDetailDrawer';

interface UserCardsProps {
  organizationId: string;
}

export default function UserCards({ organizationId }: UserCardsProps) {
  const [users, setUsers] = useState<OrganizationUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<OrganizationUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<OrganizationUser | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  const [searchQuery, setSearchQuery] = useState('');
  const { user: currentUser, isAdmin } = useUser();

  useEffect(() => {
    if (organizationId) {
      loadUsers();
    }
  }, [organizationId]);

  useEffect(() => {
    // Filter users based on search query
    if (searchQuery.trim() === '') {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(user =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.role.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredUsers(filtered);
    }
  }, [users, searchQuery]);

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      const usersData = await organizationApi.getOrganizationUsers();

      // Filter only onboarded users
      console.log(usersData);
      const onboardedUsers = usersData.filter(user => user.hasOnboarded === true);
      setUsers(usersData);
    } catch (error) {
      console.error('Failed to load users:', error);
      toast.error('Failed to load organization members');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserClick = (user: OrganizationUser) => {
    if (isAdmin) {
      setSelectedUser(user);
      setIsDrawerOpen(true);
    }
  };

  const handleUserUpdate = () => {
    loadUsers(); // Refresh users list after updates
  };

  const getRoleConfig = (role: OrganizationUser['role']) => {
    switch (role) {
      case 'ADMIN':
        return {
          color: 'bg-red-500 text-white',
          icon: <Crown className="h-3 w-3" />,
          label: 'Admin'
        };
      case 'TEAMADMIN':
        return {
          color: 'bg-blue-500 text-white',
          icon: <Shield className="h-3 w-3" />,
          label: 'Team Admin'
        };
      case 'USER':
        return {
          color: 'bg-green-500 text-white',
          icon: <User className="h-3 w-3" />,
          label: 'User'
        };
      default:
        return {
          color: 'bg-gray-500 text-white',
          icon: <User className="h-3 w-3" />,
          label: 'Unknown'
        };
    }
  };

  const formatLastActive = (lastActive?: string) => {
    if (!lastActive) return 'Never';

    const date = new Date(lastActive);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  if (!isAdmin) {
    const userProfile = users.find(u => u.id === currentUser.id);

    if (isLoading) {
      return (
        <div className="w-full">
          <div className="mb-4">
            <Skeleton className="h-7 w-32" />
          </div>
          <SkeletonCard />
        </div>
      );
    }

    if (!userProfile) {
      return (
        <div className="w-full">
          <h2 className="text-xl font-bold mb-4">Your Profile</h2>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Profile not found</p>
          </div>
        </div>
      );
    }

    const roleConfig = getRoleConfig(userProfile.role);

    return (
      <div className="w-full">
        <h2 className="text-xl font-bold mb-4">Your Profile</h2>
        <Card className="bg-transparent backdrop-blur-sm border-border">
          <CardHeader className="flex flex-row items-center gap-4">
            <Avatar className="h-12 w-12 border-2 border-border">
              <AvatarImage src={userProfile.profileImageUrl} alt={userProfile.name} />
              <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                {userProfile.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-lg text-foreground">{userProfile.name}</h3>
                <Badge variant="outline" className={`text-xs px-2 py-1 ${roleConfig.color} border`}>
                  {roleConfig.icon}
                  <span className="ml-1">{roleConfig.label}</span>
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">{userProfile.email}</p>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>{userProfile.teams.length} teams</span>
              </div>
              <span className="text-muted-foreground/60">•</span>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>Active {formatLastActive(userProfile.lastActive)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Admin view with all users
  if (isLoading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="space-y-1">
            <Skeleton className="h-7 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
        <Skeleton className="h-10 w-full" />
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <SkeletonCard key={index} />
          ))}
        </div>
      </div>
    );
  }

  const renderTableView = () => (
    <div className="border rounded-lg bg-card">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">User</TableHead>
            <TableHead>Role</TableHead>
            <TableHead className="hidden md:table-cell">Teams</TableHead>
            <TableHead className="hidden lg:table-cell">Last Active</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredUsers.map((user) => {
            const roleConfig = getRoleConfig(user.role);

            return (
              <TableRow
                key={user.id}
                className="cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => handleUserClick(user)}
              >
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8 border border-border">
                      <AvatarImage src={user.profileImageUrl} alt={user.name} />
                      <AvatarFallback className="bg-primary/10 text-primary text-sm font-semibold">
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <p className="font-medium text-sm truncate">{user.name}</p>
                      <p className="text-xs text-muted-foreground truncate">{user.email}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={`text-xs px-2 py-1 ${roleConfig.color} border`}>
                    {roleConfig.icon}
                    <span className="ml-1">{roleConfig.label}</span>
                  </Badge>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Users className="h-3 w-3" />
                    <span>{user.teams?.length || 0}</span>
                  </div>
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{formatLastActive(user.lastActive)}</span>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );

  const renderCardsView = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {filteredUsers.map((user) => {
        const roleConfig = getRoleConfig(user.role);

        return (
          <Card
            key={user.id}
            className="cursor-pointer bg-transparent border-[1px] border-border rounded-none hover:border-primary/20 transition-all duration-200 group"
            onClick={() => handleUserClick(user)}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="relative flex-shrink-0">
                  <Avatar className="h-12 w-12 border-2 border-border group-hover:border-primary/30 transition-colors">
                    <AvatarImage src={user.profileImageUrl} alt={user.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/20 text-primary font-semibold">
                      {user.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>

                  <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-background flex items-center justify-center ${roleConfig.color} shadow-sm`}>
                    {React.cloneElement(roleConfig.icon, { className: "h-2.5 w-2.5" })}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm text-foreground truncate">{user.name}</h3>
                  <p className="text-xs text-muted-foreground truncate">{user.email}</p>

                  <div className="mt-1">
                    <span className="text-xs font-medium text-muted-foreground/80">
                      {roleConfig.label}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );

  return (
    <div className="w-full space-y-6">
      {/* Controls bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search bar */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search members by name, email, or role..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-10"
          />
        </div>

        {/* View toggle and stats */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>{filteredUsers.length} of {users.length}</span>
          </div>

          <div className="flex items-center border rounded-lg p-1 bg-muted/50">
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="h-7 px-2"
            >
              <Grid3X3 className="h-3 w-3" />
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
              className="h-7 px-2"
            >
              <List className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="max-h-[70vh] w-full">
        {filteredUsers.length === 0 ? (
          <div className="text-center py-12">
            <div className="flex flex-col items-center gap-3">
              <div className="p-3 bg-muted/50 rounded-full">
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
              <div>
                <p className="font-medium text-muted-foreground">No members found</p>
                <p className="text-sm text-muted-foreground">
                  {searchQuery ? 'Try adjusting your search criteria' : 'No members to display'}
                </p>
              </div>
            </div>
          </div>
        ) : (
          viewMode === 'cards' ? renderCardsView() : renderTableView()
        )}
      </ScrollArea>

      {/* User Detail Drawer */}
      <UserDetailDrawer
        user={selectedUser}
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        onUserUpdate={handleUserUpdate}
        organizationId={organizationId}
      />
    </div>
  );
}
