import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { ConnectorsStatus, ConnectorsType, EtlStatus } from '@prisma/client';
import { CreateConnectorDto } from '../dtos/addConnectors.dto';
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';
import { ConfigService } from '@nestjs/config';
import { UpdateStatusDto } from '../dtos/updateStatus.dto';
import {
  OrganizationConnectedConnectorsResponseDto,
  UserConnectedConnectorsResponseDto,
  ConnectedConnectorDto,
  UserConnectedConnectorsDto,
} from '../dtos/connected-connectors.dto';

@Injectable()
export class ConnectorsService {
  private encryptionKey: Buffer;
  constructor(
    private prisma: PrismaService,
    private dynamoDBService: DynamoDBService,
    private configService: ConfigService,
  ) {
    const key = this.configService.get<string>('encryption.key');
    if (!key)
      throw new Error('ENCRYPTION_KEY environment variable is required');
    this.encryptionKey = Buffer.from(key, 'hex');
  }

  async getConnectors(userId?: string, teamId?: string, orgId?: string) {
    const include: any = {};

    if (userId) {
      include.UserConnectors = {
        where: { userId },
        select: { id: true },
      };
    }

    if (teamId) {
      include.TeamConnectors = {
        where: { teamId },
        select: { id: true },
      };
    }

    if (orgId) {
      include.OrganizationConnectors = {
        where: { organizationId: orgId },
        select: { id: true },
      };
    }

    const allConnectors = await this.prisma.connectors.findMany({
      include,
    });

    const processedConnectors = allConnectors
      .map((connector) => ({
        ...connector,
        UserConnectors: undefined,
        TeamConnectors: undefined,
        OrganizationConnectors: undefined,
        isUserConnected: userId
          ? connector.UserConnectors?.length > 0 && connector.status === "AVAILABLE"
          : undefined,
        isTeamConnected: teamId
          ? connector.TeamConnectors?.length > 0
          : undefined,
        isOrgConnected: orgId
          ? connector.OrganizationConnectors?.length > 0
          : undefined,
      }))
      .sort((a, b) => {
        return a.status.localeCompare(b.status);
      });

    const groupedConnectors = processedConnectors.reduce(
      (acc, connector) => {
        const category = connector.category || 'Uncategorized';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(connector);
        return acc;
      },
      {} as Record<string, typeof processedConnectors>,
    );

    return Object.entries(groupedConnectors).map(([category, connectors]) => ({
      category,
      connectors,
    }));
  }

  async createConnectors(data: CreateConnectorDto) {
    if (!Array.isArray(data.connectors) || data.connectors.length === 0) {
      throw new Error('Invalid input: Expected non-empty array of connectors');
    }

    return await this.prisma.connectors.createMany({
      data: data.connectors,
      skipDuplicates: true,
    });
  }

  async connectorByslug(userId: string, connectorslug: string) {
    const connector = await this.prisma.connectors.findFirst({
      where: {
        slug: connectorslug,
      },
      include: {
        UserConnectors: {
          where: {
            userId: userId,
          },
          include: {
            etls: true,
          },
        },
      },
    });

    if (!connector) {
      throw new NotFoundException('Connector not found');
    }

    return {
      ...connector,
      UserConnectors: undefined,
      isConnected: connector.UserConnectors.length > 0,
      userConnectorId:
        connector.UserConnectors.length > 0
          ? connector.UserConnectors[0].id
          : '',
      connectionDetails:
        connector.UserConnectors.length
          ? {
            status: 'connected',
            createdAt: connector.UserConnectors[0].createdAt,
            updatedAt: connector.UserConnectors[0].updatedAt,
          }
          : {
            status: 'not_connected',
          },
      config: connector?.UserConnectors[0]?.config || {},
    };
  }

  encrypt(text: string): { encryptedData: string; iv: string } {
    const iv = randomBytes(16);
    const cipher = createCipheriv('aes-256-gcm', this.encryptionKey, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      encryptedData: encrypted + ':' + authTag.toString('hex'),
      iv: iv.toString('hex'),
    };
  }

  decrypt(encryptedData: string, iv: string): string {
    const [encrypted, authTag] = encryptedData.split(':');
    const decipher = createDecipheriv(
      'aes-256-gcm',
      this.encryptionKey,
      Buffer.from(iv, 'hex'),
    );

    decipher.setAuthTag(Buffer.from(authTag, 'hex'));

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  async updateStatus(data: UpdateStatusDto) {
    const { id, status } = data;
    await this.prisma.etl.update({
      where: { taskId: id },
      data: { status: EtlStatus[status.toUpperCase()] },
    });
    return {
      message: 'Status updated successfully',
      status: status,
    };
  }

  async getOrganizationConnectedConnectors(
    organizationId: string,
    requestingUserId: string,
  ): Promise<OrganizationConnectedConnectorsResponseDto> {
    const requestingUser = await this.prisma.user.findUnique({
      where: { id: requestingUserId },
      select: { organizationId: true, role: true },
    });

    if (!requestingUser) {
      throw new NotFoundException('User not found');
    }

    if (requestingUser.organizationId !== organizationId) {
      throw new ForbiddenException('You can only view connectors for your own organization');
    }

    if (!['ADMIN', 'TEAMADMIN'].includes(requestingUser.role)) {
      throw new ForbiddenException('Only admins can view organization-wide connector information');
    }

    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
      select: { id: true, name: true },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const users = await this.prisma.user.findMany({
      where: { organizationId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        connectors: {
          include: {
            connector: {
              select: {
                id: true,
                name: true,
                type: true,
                logo: true,
                category: true,
                description: true,
                status: true,
              },
            },
          },
        },
      },
    });

    const connectorSummary: Record<string, number> = {};
    let totalConnections = 0;

    const usersWithConnectors: UserConnectedConnectorsDto[] = await Promise.all(
      users.map(async (user) => {
        const connectors: ConnectedConnectorDto[] = [];

        for (const userConnector of user.connectors) {
          try {
            const dynamoConnectors = await this.dynamoDBService.query('UserConnectors', {
              keyConditionExpression: 'user_id = :userId',
              expressionAttributeValues: {
                ':userId': user.id,
              },
            });

            const dynamoConnector = dynamoConnectors.find(
              (dc: any) => dc.connector_id === userConnector.connector.id,
            );

            const connectorDto: ConnectedConnectorDto = {
              connectorId: userConnector.connector.id,
              name: userConnector.connector.name,
              type: userConnector.connector.type,
              logo: userConnector.connector.logo,
              category: userConnector.connector.category,
              description: userConnector.connector.description,
              status: userConnector.connector.status,
              connectedAt: userConnector.createdAt,
              updatedAt: userConnector.updatedAt,
              config: userConnector.config,
            };

            connectors.push(connectorDto);

            const connectorName = userConnector.connector.name;
            connectorSummary[connectorName] = (connectorSummary[connectorName] || 0) + 1;
            totalConnections++;
          } catch (error) {
            console.error(`Error fetching DynamoDB data for user ${user.id}:`, error);
            const connectorDto: ConnectedConnectorDto = {
              connectorId: userConnector.connector.id,
              name: userConnector.connector.name,
              type: userConnector.connector.type,
              logo: userConnector.connector.logo,
              category: userConnector.connector.category,
              description: userConnector.connector.description,
              status: userConnector.connector.status,
              connectedAt: userConnector.createdAt,
              updatedAt: userConnector.updatedAt,
              config: userConnector.config,
            };

            connectors.push(connectorDto);
            const connectorName = userConnector.connector.name;
            connectorSummary[connectorName] = (connectorSummary[connectorName] || 0) + 1;
            totalConnections++;
          }
        }

        return {
          userId: user.id,
          userName: user.name || 'Unknown User',
          userEmail: user.email,
          userRole: user.role,
          connectors,
          totalConnectors: connectors.length,
        };
      }),
    );

    return {
      organizationId: organization.id,
      organizationName: organization.name,
      users: usersWithConnectors,
      totalUsers: users.length,
      totalConnections,
      connectorSummary,
    };
  }

  async getUserConnectedConnectors(
    userId: string,
    requestingUserId: string,
  ): Promise<UserConnectedConnectorsResponseDto> {
    const requestingUser = await this.prisma.user.findUnique({
      where: { id: requestingUserId },
      select: { organizationId: true, role: true },
    });

    if (!requestingUser) {
      throw new NotFoundException('Requesting user not found');
    }

    const targetUser = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        organizationId: true,
        connectors: {
          include: {
            connector: {
              select: {
                id: true,
                name: true,
                type: true,
                logo: true,
                category: true,
                description: true,
                status: true,
              },
            },
          },
        },
      },
    });

    if (!targetUser) {
      throw new NotFoundException('Target user not found');
    }

    const canView =
      requestingUserId === userId || 
      (requestingUser.organizationId &&
       targetUser.organizationId === requestingUser.organizationId &&
       ['ADMIN', 'TEAMADMIN'].includes(requestingUser.role));

    if (!canView) {
      throw new ForbiddenException('Not authorized to view this user\'s connectors');
    }

    const connectors: ConnectedConnectorDto[] = [];

    for (const userConnector of targetUser.connectors) {
      try {
        const dynamoConnectors = await this.dynamoDBService.query('UserConnectors', {
          keyConditionExpression: 'user_id = :userId',
          expressionAttributeValues: {
            ':userId': userId,
          },
        });

        const dynamoConnector = dynamoConnectors.find(
          (dc: any) => dc.connector_id === userConnector.connector.id,
        );

        const connectorDto: ConnectedConnectorDto = {
          connectorId: userConnector.connector.id,
          name: userConnector.connector.name,
          type: userConnector.connector.type,
          logo: userConnector.connector.logo,
          category: userConnector.connector.category,
          description: userConnector.connector.description,
          status: userConnector.connector.status,
          connectedAt: userConnector.createdAt,
          updatedAt: userConnector.updatedAt,
          config: userConnector.config,
        };

        connectors.push(connectorDto);
      } catch (error) {
        console.error(`Error fetching DynamoDB data for user ${userId}:`, error);
        const connectorDto: ConnectedConnectorDto = {
          connectorId: userConnector.connector.id,
          name: userConnector.connector.name,
          type: userConnector.connector.type,
          logo: userConnector.connector.logo,
          category: userConnector.connector.category,
          description: userConnector.connector.description,
          status: userConnector.connector.status,
          connectedAt: userConnector.createdAt,
          updatedAt: userConnector.updatedAt,
          config: userConnector.config,
        };

        connectors.push(connectorDto);
      }
    }

    return {
      userId: targetUser.id,
      userName: targetUser.name || 'Unknown User',
      userEmail: targetUser.email,
      organizationId: targetUser.organizationId,
      connectors,
      totalConnectors: connectors.length,
    };
  }
}
