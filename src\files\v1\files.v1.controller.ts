import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  HttpCode,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { FilesService } from './files.v1.service';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { RenameFileDto } from '../dtos/rename-file.dto';

@ApiTags('File-V1')
@Controller({ version: '1', path: 'file' })
export class FileController {
  constructor(private fileService: FilesService) {}

  @Put(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Rename file' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'File renamed successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async renameFile(
    @Body() data: RenameFileDto,
    @Param('id') kid: string,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.fileService.renameFile(data, kid, user.sub);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Delete file' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'File deleted successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async deleteFile(
    @Param('id') id: string,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.fileService.deleteFile(id, user.sub);
  }
}
