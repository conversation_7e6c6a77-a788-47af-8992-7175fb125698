import { Controller, Get, HttpCode, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { UsageService } from './usage.v1.service';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';

@ApiTags('usage-V1')
@Controller({ version: '1', path: 'usage' })
export class UsageController {
  constructor(private usageService: UsageService) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get current plan of the user' })
  @ApiResponse({
    status: 200,
    description: 'The current plan of the user',
  })
  @HttpCode(200)
  async getCurrentPlan(@GetCurrentUser() user: JwtPayload) {
    return this.usageService.getCurrentPlan(user.sub);
  }
}
