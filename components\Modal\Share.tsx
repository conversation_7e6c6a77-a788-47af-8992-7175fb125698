"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Book, Share2, Loader2, CheckCircle2, <PERSON>rkles } from "lucide-react";
import axios from "axios";
import { useRouter } from "next/navigation";

interface ShareProps {
    className?: string;
}

const Share: React.FC<ShareProps> = ({
    className,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [email, setEmail] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [error, setError] = useState("");
    const [isHovered, setIsHovered] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!email.trim()) {
            setError("Please enter a email");
            return;
        }
        setError("");
        setIsSubmitting(true);

        try {
            // const res = await axios.post("/api/knowledgebase", { name });
            setIsSuccess(true);
        } catch (err) {
            setError("Something went wrong. Please try again.");
        } finally {
            handleReset();
            setIsSubmitting(false);
        }
    };

    const handleReset = () => {
        setEmail("");
        setIsSubmitting(false);
        setIsSuccess(false);
        setError("");
    };

    const buttonVariants = {
        initial: { scale: 1 },
        hover: { scale: 1.02 },
        tap: { scale: 0.98 },
    };

    const iconVariants = {
        initial: { rotate: 0 },
        hover: { rotate: 180, transition: { duration: 0.3 } },
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <motion.div
                    variants={buttonVariants}
                    initial="initial"
                    whileHover="hover"
                    whileTap="tap"
                    onHoverStart={() => setIsHovered(true)}
                    onHoverEnd={() => setIsHovered(false)}
                >
                    <Button
                        className={`relative bg-blue-600 hover:bg-blue-900 text-white 
        transition-all duration-200 shadow-lg hover:shadow-blue-200 pr-6 pl-4 
        ${className}`}
                    >
                        <motion.div
                            variants={iconVariants}
                            initial="initial"
                            animate={isHovered ? "hover" : "initial"}
                            className="mr-0"
                        >
                            <Share2 className="h-4 w-4 mr-2" />
                        </motion.div>
                        Share
                    </Button>
                </motion.div>
            </DialogTrigger>

            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <motion.div
                            initial={{ rotate: -20, opacity: 0 }}
                            animate={{ rotate: 0, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                        >
                            <Share2 className="h-5 w-5 text-primary" />
                        </motion.div>
                        <motion.span
                            initial={{ x: -20, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            className="text-text-primary"
                        >
                            Share
                        </motion.span>
                    </DialogTitle>
                    <DialogDescription>
                        <motion.div
                            initial={{ y: -10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.3 }}
                            className="text-text-secondary"
                        >
                            Share your knowledge base with others.
                        </motion.div>
                    </DialogDescription>
                </DialogHeader>

                <motion.form
                    onSubmit={handleSubmit}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="space-y-6"
                >
                    <div className="space-y-2">
                        <Label htmlFor="email" className="text-right">
                            Email
                        </Label>
                        <div className="relative">
                            <Input
                                id="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="Enter email address"
                                className={`pr-8 ${error ? "border-red-500 focus:ring-red-500" : ""
                                    }`}
                                disabled={isSubmitting || isSuccess}
                            />
                            <AnimatePresence>
                                {error && (
                                    <motion.p
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -10 }}
                                        className="text-destructive text-sm mt-1"
                                    >
                                        {error}
                                    </motion.p>
                                )}
                            </AnimatePresence>
                        </div>
                    </div>

                    <DialogFooter className="flex gap-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleReset}
                            disabled={isSubmitting}
                            className="flex-1"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting || isSuccess}
                            className="flex-1 relative bg-gradient-to-r from-[#2c487c] to-[#3B6BB5]"
                        >
                            <AnimatePresence mode="wait">
                                {isSubmitting ? (
                                    <motion.div
                                        key="submitting"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        className="flex items-center gap-2"
                                    >
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        Sharing...
                                    </motion.div>
                                ) : isSuccess ? (
                                    <motion.div
                                        key="success"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        className="flex items-center gap-2"
                                    >
                                        <CheckCircle2 className="h-4 w-4" />
                                        Shared!
                                    </motion.div>
                                ) : (
                                    <motion.span
                                        key="create"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                    >
                                        Share Knowledge Base
                                    </motion.span>
                                )}
                            </AnimatePresence>
                        </Button>
                    </DialogFooter>
                </motion.form>
            </DialogContent>
        </Dialog>
    );
};

export default Share;
