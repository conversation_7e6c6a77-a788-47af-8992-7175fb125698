"use client";

import { <PERSON>, CardContent, CardHeader } from "@steps-ai/ui";
import { Database, Lock, FileText } from "lucide-react";
import { motion, Variants } from "framer-motion";
import Link from "next/link";
import { ScrollArea } from "@steps-ai/ui";

interface Props {
    name: string;
    id: string;
    index: number;
    files: any[];
    owner: { name: string; avatar?: string };
    hoveredCard: string | null;
    status: 'NEW' | 'UPLOADED' | 'PROCESSING' | 'SUCCESS' | 'ERROR';
}

// const container: Variants = {
//     hidden: { opacity: 0 },
//     show: {
//         opacity: 1,
//         transition: {
//             staggerChildren: 0.1,
//             delayChildren: 0.3,
//         },
//     },
// };

const item: Variants = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 },
};

const KnowledgeBaseCards = ({ data }: { data: Props[] }) => {
    const getStatusConfig = (status: Props['status']) => {
        switch (status) {
            case 'NEW':
                return { color: 'text-blue-500', label: 'New' };
            case 'UPLOADED':
                return { color: 'text-purple-500', label: 'Uploaded' };
            case 'PROCESSING':
                return { color: 'text-yellow-500', label: 'Processing' };
            case 'SUCCESS':
                return { color: 'text-green-500', label: 'Ready' };
            case 'ERROR':
                return { color: 'text-red-500', label: 'Error' };
            default:
                return { color: 'text-gray-500', label: 'Unknown' };
        }
    };

    return (
        <div
            // variants={container}
            className="space-y-8"
        >
            <ScrollArea className="max-h-[60vh] w-full overflow-y-auto scroll-smooth custom-scrollbar">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {data.map((props) => (
                        <Link
                            key={props.index}
                            href={`/knowledgebase/configuration?id=${props.id}`}
                        >
                            <Card className="cursor-pointer animate-fadeIn hover:bg-muted/90 rounded-xl bg-card/80 backdrop-blur-sm border-border shadow-lg hover:shadow-xl transition-all duration-200">
                                <CardHeader className="flex flex-row items-center gap-4">
                                    <Database className="h-10 w-10 text-primary" />
                                    <div className="flex flex-col">
                                        <div className="flex items-center gap-2">
                                            <h3 className="font-semibold text-lg text-foreground">{props.name}</h3>
                                            <span className={`text-xs px-2 py-1 rounded-full bg-background ${getStatusConfig(props.status).color}`}>
                                                {getStatusConfig(props.status).label}
                                            </span>
                                        </div>
                                        <p className="text-sm text-muted-foreground">{props.owner.name}</p>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                        <Lock className="h-4 w-4" />
                                        <span>Private</span>
                                        <span className="mx-2 text-muted-foreground/60">•</span>
                                        <FileText className="h-4 w-4" />
                                        <span>{props.files.length} Files</span>
                                    </div>
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>
            </ScrollArea>
        </div>
    );
};

export default KnowledgeBaseCards;
