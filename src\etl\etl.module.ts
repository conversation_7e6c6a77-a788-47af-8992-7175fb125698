import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EtlService } from './etl.service';
// import { AirflowEtlProvider } from './providers/airlfow.provider';
import { CeleryEtlProvider } from './providers/celery.provider';

export enum EtlProviderType {
  AIRFLOW = 'airflow',
  CELERY = 'celery',
}

export interface EtlModuleOptions {
  type: EtlProviderType;
}

@Module({})
export class EtlModule {
  static register(options: EtlModuleOptions): DynamicModule {
    return {
      module: EtlModule,
      global: true,
      imports: [ConfigModule],
      providers: [
        {
          provide: 'ETL_PROVIDER',
          useFactory: (configService: ConfigService) => {
            switch (options.type) {
              // case EtlProviderType.AIRFLOW:
              //   return new AirflowEtlProvider(
              //     configService.get('airflow.username'),
              //     configService.get('airflow.password'),
              //     configService.get('airflow.baseUrl'),
              //   );
              case EtlProviderType.CELERY:
                return new CeleryEtlProvider(
                  configService.get('celery.baseUrl'),
                );
              default:
                throw new Error('Invalid etl provider');
            }
          },
          inject: [ConfigService],
        },
        EtlService,
      ],
      exports: [EtlService],
    };
  }
}
