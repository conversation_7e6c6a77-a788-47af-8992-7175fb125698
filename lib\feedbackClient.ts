"use client";

import axios from 'axios';

const feedbackApiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
    },
});


interface IFeedbackOperations {
    submitFeedback(message: string, rating: string, email?: string): Promise<{ status: boolean; message: string }>;
    submitSupport(message: string, area: string, email: string): Promise<{ status: boolean; message: string }>;
}

class FeedbackClient implements IFeedbackOperations {
    private static instance: FeedbackClient;
    
    private constructor() {}

    public static getInstance(): FeedbackClient {
        if (!FeedbackClient.instance) {
            FeedbackClient.instance = new FeedbackClient();
        }
        return FeedbackClient.instance;
    }

    async submitFeedback(message: string, rating: string, email?: string, area?: string): Promise<{ status: boolean; message: string }> {
        try {
            const response = await feedbackApiClient.post('/feedback', {
                message,
                rating: parseInt(rating, 10),
                email,
                area
            });

            return response.data;

        } catch (error: any) {
            throw error.response?.data || error;
        }
    }

    async submitSupport(email: string, message: string, area: string): Promise<{ status: boolean; message: string }> {
        try {
            const response = await feedbackApiClient.post('/support', {
                email,

                message,
                area
            });
            return response.data;
        } catch (error: any) {
            throw error.response?.data || error;
        }
    }
}

export const feedbackClient = FeedbackClient.getInstance(); 