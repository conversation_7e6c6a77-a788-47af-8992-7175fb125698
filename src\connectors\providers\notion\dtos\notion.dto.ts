import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class NotionCallbackDto {
  @ApiProperty({
    description: 'The code from Notion',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'The state from Notion',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  state: string;
}

export class CrawlNotionDto {
  @ApiProperty({
    description: 'user connector id',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  userConnectorId: string;
}
