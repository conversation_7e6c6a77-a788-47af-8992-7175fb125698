"use client"
import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Instagram, Facebook, Linkedin, Twitter, Bot, MessageSquare, Sparkles, Shield, Zap } from 'lucide-react';

interface Feature {
    icon: React.ReactNode;
    title: string;
    description: string;
}

interface ComingSoonProps {
    title?: string;
    subtitle?: string;
    features?: Feature[];
}

const defaultFeatures: Feature[] = [
    {
        icon: <Bot />,
        title: "AI-Powered Answers",
        description: "Get instant, accurate responses from your document base using advanced RAG technology."
    },
    {
        icon: <MessageSquare />,
        title: "Seamless Integration",
        description: "Access your knowledge base directly from your workspace without switching context."
    },
    {
        icon: <Sparkles />,
        title: "Smart Context",
        description: "AIDE understands the context of your conversations and provides precise, relevant answers."
    },
    {
        icon: <Shield />,
        title: "Enterprise Security",
        description: "Bank-grade encryption and security measures to keep your data safe and compliant."
    },
    {
        icon: <Zap />,
        title: "Lightning Fast",
        description: "Get responses in milliseconds with our optimized infrastructure and caching system."
    }
];

const ComingSoon = ({
    title = "Coming Soon",
    subtitle = "Something amazing is in the works. Stay tuned for updates.",
    features = defaultFeatures
}: ComingSoonProps) => {
    const socialIcons = [
        { Icon: Instagram, href: "#", label: "Follow us on Instagram" },
        { Icon: Facebook, href: "#", label: "Connect on Facebook" },
        { Icon: Linkedin, href: "#", label: "Join us on LinkedIn" },
        { Icon: Twitter, href: "#", label: "Follow us on Twitter" }
    ];

    return (
        <div className="relative w-full">
            {/* <div className="absolute inset-0 b-pattern opacity-[0.02] pointer-events-none" /> */}
            {/* <div className="absolute inset-0 bg-gradient-to-b from-primary/5 to-transparent pointer-events-none" /> */}

            <div className="relative w-full px-6 md:px-20 flex flex-col justify-between">
                <div className="max-w-8xl mx-auto w-full grid lg:grid-cols-2 gap-20 items-center">
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8 }}
                        className="space-y-12"
                    >
                        <div className="space-y-6">
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2 }}
                                className="flex gap-4 items-center"
                            >
                                <span className="px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium">
                                    Coming Soon
                                </span>
                                {/* <span className="px-4 py-2 rounded-full bg-secondary/10 text-secondary text-sm font-medium">
                                    Join Waitlist
                                </span> */}
                            </motion.div>

                            <h1 className="text-5xl md:text-6xl xl:text-7xl font-bold text-foreground tracking-tight">
                                {title}
                            </h1>
                            <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-2xl">
                                {subtitle}
                            </p>
                        </div>

                        {/* <motion.div */}

                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        className="grid gap-6 h-full place-content-center"
                    >
                        {features.map((feature, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2 * index }}
                            >
                                <Card className="group overflow-hidden border-border hover:border-primary/50 transition-all duration-300 hover:shadow-lg">
                                    <CardContent className="p-8">
                                        <div className="flex gap-6 items-start">
                                            <div className="p-3.5 rounded-xl bg-primary/10 group-hover:bg-primary/15 transition-colors duration-300">

                                            </div>
                                            <div className="flex-1 space-y-2">
                                                <h3 className="text-lg font-semibold text-foreground">
                                                    {feature.title}
                                                </h3>
                                                <p className="text-muted-foreground leading-relaxed">
                                                    {feature.description}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </motion.div>
                        ))}
                    </motion.div>
                </div>

                {/* <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                    className="max-w-8xl mx-auto w-full pt-20 border-t border-border"
                >
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                        {[
                            { label: "Active Users", value: "10K+" },
                            { label: "Data Processing", value: "99.9%" },
                            { label: "Response Time", value: "<100ms" },
                            { label: "Satisfaction", value: "98%" },
                        ].map((stat, index) => (
                            <div key={index} className="text-center">
                                <h4 className="text-3xl font-bold text-foreground mb-2">{stat.value}</h4>
                                <p className="text-sm text-muted-foreground">{stat.label}</p>
                            </div>
                        ))}
                    </div>
                </motion.div> */}
            </div>
        </div>
    );
};

export default ComingSoon;