import type React from "react"
import { FileText, ExternalLink, Hash, Target } from "lucide-react"

interface KnowledgebaseSourcesProps {
    sources: any[]
}

const truncate = (text: string, maxLength = 100) => {
    if (!text) return ""
    return text.length > maxLength ? text.slice(0, maxLength) + "..." : text
}

const KnowledgebaseSources: React.FC<KnowledgebaseSourcesProps> = ({ sources }) => {
    if (!sources || sources.length === 0) {
        return (
            <div className="flex items-center justify-center p-8 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 rounded-xl">
                <div className="text-center">
                    <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400 dark:text-gray-500" />
                    <p className="text-sm font-medium">No sources found for this agent</p>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-3">
            {sources.map((source, sourceIdx) =>
                Array.isArray(source)
                    ? source.map((item, idx) => {
                        let meta: Record<string, any> = {}
                        try {
                            meta =
                                typeof item.metadata === "string" ? JSON.parse(item.metadata.replace(/'/g, '"')) : item.metadata || {}
                        } catch {
                            meta = {}
                        }
                        return (
                            <div
                                key={`${sourceIdx}-${idx}`}
                                className="relative bg-white dark:bg-[#14171F] rounded-xl p-4  overflow-x-auto break-words"
                            >
                                {/* Header */}
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-2">
                                        <div className="flex items-center gap-1.5 px-2.5 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-full text-xs font-medium">
                                            <FileText className="w-3 h-3" />
                                            Knowledge Base
                                        </div>
                                        {meta?.name && meta?.link && (
                                            <a
                                                href={meta.link}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 hover:underline font-medium transition-colors duration-200"
                                            >
                                                {meta.name}
                                                <ExternalLink className="w-3 h-3" />
                                            </a>
                                        )}
                                    </div>
                                </div>

                                {/* Content */}
                                <div className="mb-4">
                                    <p className="text-sm text-gray-700 dark:text-gray-200 leading-relaxed">{truncate(item.text)}</p>
                                </div>

                                {/* Metadata Footer */}
                                <div className="flex items-center gap-4 pt-3">
                                    {meta?.file_name && (
                                        <div className="flex items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400">
                                            <FileText className="w-3.5 h-3.5" />
                                            <span className="font-medium">{meta.file_name}</span>
                                        </div>
                                    )}
                                    {meta?.page_number && (
                                        <div className="flex items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400">
                                            <Hash className="w-3.5 h-3.5" />
                                            <span>Page {meta.page_number}</span>
                                        </div>
                                    )}
                                    {item.score !== undefined && (
                                        <div className="flex items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400 ml-auto">
                                            <Target className="w-3.5 h-3.5" />
                                            <span className="font-mono">{item.score?.toFixed ? item.score.toFixed(2) : item.score}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )
                    })
                    : null,
            )}
        </div>
    )
}

export default KnowledgebaseSources
