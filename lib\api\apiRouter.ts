import axios, { AxiosRequestConfig } from 'axios';
export class ApiRouter {
  private baseUrl = '/api/proxy';

  public mainApi = {
    get: <T>(path: string, config?: AxiosRequestConfig) => 
      axios.get<T>(`${this.baseUrl}/${path}`, config),
    
    post: <T>(path: string, data?: any, config?: AxiosRequestConfig) => 
      axios.post<T>(`${this.baseUrl}/${path}`, data, config),
    
    put: <T>(path: string, data?: any, config?: AxiosRequestConfig) => 
      axios.put<T>(`${this.baseUrl}/${path}`, data, config),
    
    delete: <T>(path: string, config?: AxiosRequestConfig) => 
      axios.delete<T>(`${this.baseUrl}/${path}`, config),
  };

  public chatApi = {
    get: <T>(path: string, config?: AxiosRequestConfig) => 
      axios.get<T>(`${this.baseUrl}/chat/${path}`, config),
    
    post: <T>(path: string, data?: any, config?: AxiosRequestConfig) => 
      axios.post<T>(`${this.baseUrl}/chat/${path}`, data, config),
    
    put: <T>(path: string, data?: any, config?: AxiosRequestConfig) => 
      axios.put<T>(`${this.baseUrl}/chat/${path}`, data, config),
    
    delete: <T>(path: string, config?: AxiosRequestConfig) => 
      axios.delete<T>(`${this.baseUrl}/chat/${path}`, config),
  };

  public analyticsApi = {
    get: <T>(path: string, config?: AxiosRequestConfig) => 
      axios.get<T>(`${this.baseUrl}/analytics/${path}`, config),
    
    post: <T>(path: string, data?: any, config?: AxiosRequestConfig) => 
      axios.post<T>(`${this.baseUrl}/analytics/${path}`, data, config),
  };

  public filesApi = {
    get: <T>(path: string, config?: AxiosRequestConfig) => 
      axios.get<T>(`${this.baseUrl}/files/${path}`, config),
    
    post: <T>(path: string, data?: any, config?: AxiosRequestConfig) => 
      axios.post<T>(`${this.baseUrl}/files/${path}`, data, config),
    
    delete: <T>(path: string, config?: AxiosRequestConfig) => 
      axios.delete<T>(`${this.baseUrl}/files/${path}`, config),
  };

  public route<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    servicePath: string,
    data?: any,
    config?: AxiosRequestConfig
  ) {
    const url = `${this.baseUrl}/${servicePath}`;
    
    switch (method) {
      case 'GET':
        return axios.get<T>(url, config);
      case 'POST':
        return axios.post<T>(url, data, config);
      case 'PUT':
        return axios.put<T>(url, data, config);
      case 'DELETE':
        return axios.delete<T>(url, config);
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }
}

export const apiRouter = new ApiRouter();

export default apiRouter;
