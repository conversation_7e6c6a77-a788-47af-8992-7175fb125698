import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  HttpCode,
  UseGuards,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { FeedbackService } from './feedback.v1.service';
import { FeedbackDto } from '../dto/feedback.dto';

@ApiTags('Feedback-V1')
@Controller({ version: '1', path: 'feedback' })
export class FeedbackController {
  constructor(private feedbackService: FeedbackService) { }
  @Post()
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Create a new feedback' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Feedback created successfully',
  })
  async createFeedback(@Body() feedback: FeedbackDto, @GetCurrentUser() user: JwtPayload) {
    return this.feedbackService.createFeedback(feedback, user.sub);
  }
}
