import { Module } from '@nestjs/common';
import { PrismaModule } from '../prisma/prisma.module';
import { SupportV1Controller } from './v1/support.v1.controller';
import { SupportV1Service } from './v1/support.v1.service';

@Module({
    imports: [PrismaModule],
    controllers: [SupportV1Controller],
    providers: [SupportV1Service],
    exports: [SupportV1Service]
})
export class SupportModule { }
