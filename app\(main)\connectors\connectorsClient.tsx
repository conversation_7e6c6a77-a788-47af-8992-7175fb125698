"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { Categories } from "./categories"
import { Input } from "@/components/ui/input"

import LayoutAnimation from "@/components/Animation/layoutAnimation";
import { ScrollArea } from "@/components/ui/scroll-area"
import { useApiQuery } from "@/lib/apiClient"
import React from "react"
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { Star } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuCheckboxItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { Filter } from "lucide-react";
import { Separator } from "@/components/ui/separator";
interface Connector {
    id: string;
    name: string;
    logo: string;
    description: string;
    status: string;
    type: string;
    isUserConnected: boolean;
    category: string;
    slug: string;
}

interface ConnectorCategory {
    category: string;
    connectors: Connector[];
}

interface DataSourceListProps {
    category: string
    searchQuery: string
}


export default function ConnectorsClient() {
    const router = useRouter();
    const [activeCategory, setActiveCategory] = useState("All Sources")
    const [searchQuery, setSearchQuery] = useState("")
    const [showOnlyAvailable, setShowOnlyAvailable] = useState(false);
    const [selectedTypes, setSelectedTypes] = useState<string[]>([]);

    const { data, isLoading } = useApiQuery<{ result: ConnectorCategory[] }>(
        ["connectors"],
        `/connectors`
    );
    const filteredSources = React.useMemo(() => {
        if (!data?.result || !data?.result.length) return [];

        const allConnectors = data.result.flatMap((cat: ConnectorCategory) => cat.connectors);

        const filtered = allConnectors.filter((source: Connector) => {
            const matchesCategory = activeCategory === "All Sources" || source.category === activeCategory;
            const matchesSearch = source.name.toLowerCase().includes(searchQuery.toLowerCase());
            const matchesAvailability = showOnlyAvailable ? source.status === "AVAILABLE" : true;
            const matchesType = selectedTypes.length === 0 || selectedTypes.includes(source.type);

            return matchesCategory && matchesSearch && matchesAvailability && matchesType;
        });

        return filtered.sort((a, b) => {
            if (a.status === "AVAILABLE" && b.status !== "AVAILABLE") return -1;
            if (a.status !== "AVAILABLE" && b.status === "AVAILABLE") return 1;
            return 0;
        });
    }, [data, activeCategory, searchQuery, showOnlyAvailable, selectedTypes]);

    const availableTypes = React.useMemo(() => {
        if (!data?.result || !data?.result.length) return [];
        const types = new Set<string>(data.result.flatMap((cat: ConnectorCategory) =>
            cat.connectors.map((c: Connector) => c.type)
        ));
        return Array.from(types);
    }, [data]);

    const renderSkeletons = () => (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {[...Array(9)].map((_, i) => (
                <Card key={i} className="hover:bg-muted/50 h-[220px] sm:h-[180px] md:h-[220px] lg:h-auto">
                    <CardHeader className="flex flex-col items-center justify-center space-y-0 p-3 md:p-4">
                        <div className="h-12 w-12 md:h-16 md:w-16 bg-muted animate-pulse rounded-full" />
                        <div className="mt-2 md:mt-4 h-5 w-20 bg-muted animate-pulse rounded" />
                    </CardHeader>
                    <CardContent className="p-3 md:p-4">
                        <div className="h-4 w-full bg-muted animate-pulse rounded" />
                        <div className="h-4 w-2/3 bg-muted animate-pulse rounded mt-2" />
                    </CardContent>
                </Card>
            ))}
        </div>
    );

    return (
        // <LayoutAnimation>
            <div className="bg-background px-4 sm:px-6 md:px-15 h-[90vh]">
                <div className="container mx-auto py-4 sm:py-6 max-w-full">
                    <div className="flex flex-col md:flex-row justify-between items-start gap-6">
                        <div>
                            <h1 className="scroll-m-20 text-3xl sm:text-4xl font-bold tracking-tight mb-2 sm:mb-4">Connectors</h1>
                            <p className="text-base sm:text-lg text-muted-foreground ">
                                Connect AIDE to various data sources to enhance its knowledge base.
                            </p>
                            <a
                                href="https://docs.stepsai.co"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-primary hover:text-primary/90 inline-flex items-center mt-2 text-sm font-medium"
                            >
                                Learn how connectors work →
                            </a>
                        </div>
                        <Button variant="outline" className="w-full md:w-auto">Request a connector</Button>
                    </div>
                    <Separator className="my-4 mb-8" />

                    <div className="flex flex-col lg:flex-row gap-4 sm:gap-8 h-[calc(100vh-280px)]">
                        <aside className="w-full lg:w-64 shrink-0">
                            <div className="mb-4 sm:mb-6 gap-1 flex flex-col justify-between items-start">
                                <span className="text-lg font-semibold tracking-tight  mx-3">Search</span>
                                <Input
                                    type="search"
                                    placeholder="Search data sources..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-full sm:max-w-sm mx-2"
                                />
                            </div>
                            <Categories activeCategory={activeCategory} setActiveCategory={setActiveCategory} />
                        </aside>

                        <main className="flex-1 w-full flex flex-col">


                            <ScrollArea className="flex-1">
                                <div className="space-y-4 sm:space-y-6 pb-6">
                                    {isLoading ? renderSkeletons() : (
                                        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                                            {filteredSources.length === 0 ? (
                                                <div className="col-span-full text-center text-muted-foreground">
                                                    No data sources found.
                                                </div>
                                            ) : (
                                                filteredSources.map((source: Connector) => (
                                                    <Link href={`/connectors/${source.slug}`} key={source.id}>
                                                        <Card
                                                            key={source.id}
                                                            className={cn(
                                                                "hover:bg-muted/50 relative overflow-hidden",
                                                                "h-[220px] sm:h-[180px] md:h-[220px] lg:h-[280px]",
                                                                source.isUserConnected && "border-green-500/50",
                                                                source.status === "AVAILABLE" && "cursor-pointer"
                                                            )}
                                                        >
                                                            <CardHeader className="flex flex-col items-start gap-4 justify-center space-y-0 p-3 md:p-4">
                                                                {source.isUserConnected ? (
                                                                    <Badge variant="default" className="bg-green-500">Connected</Badge>
                                                                ) : (
                                                                    <Badge variant="default" className="bg-muted/50 text-muted-foreground border border-muted-foreground">Not Connected</Badge>
                                                                )}
                                                                <Image
                                                                    src={source.logo}
                                                                    alt={`${source.name} logo`}
                                                                    width={80}
                                                                    height={80}
                                                                    className="mx-auto mb-2 p-2 rounded-2xl border border-gray-300 dark:border-gray-800"
                                                                />
                                                            </CardHeader>
                                                            <CardTitle className="items-start flex flex-col gap-2 justify-start px-3 md:px-4">
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {source.type.charAt(0) + source.type.slice(1).toLowerCase()}
                                                                </Badge>

                                                                <span className="text-lg font-semibold truncate block lg:block md:hidden">
                                                                    {source.name}
                                                                </span>
                                                            </CardTitle>
                                                            <CardContent className="px-3 md:px-4 my-1">
                                                                <CardDescription className="line-clamp-2 text-xs sm:text-sm text-start block">
                                                                    {source.description}
                                                                </CardDescription>
                                                            </CardContent>
                                                            <CardFooter>

                                                            </CardFooter>

                                                        </Card>
                                                    </Link>
                                                ))
                                            )}
                                        </div>
                                    )}
                                </div>
                            </ScrollArea>
                        </main>
                    </div>
                </div>
            </div>
        // </LayoutAnimation>
    )
}

