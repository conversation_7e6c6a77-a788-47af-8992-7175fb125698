"use client"

import {
  MoreH<PERSON>zontal,
} from "lucide-react"

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Link from "next/link"

export function NavProjects({
  integrations,
}: {
  integrations: {
    name: string
    url: string
    icon: any
  }[]
}) {

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel>Integrations</SidebarGroupLabel>
      <SidebarMenu>
        {integrations.map((item) => (
          <SidebarMenuItem key={item.name}>
            <SidebarMenuButton asChild>
              <a href={item.url}>
                <item.icon />
                <span>{item.name}</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
        {/* <Link href="/integrations">
          <SidebarMenuItem>
            <SidebarMenuButton>
              <MoreHorizontal />
              <span> More</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </Link> */}
      </SidebarMenu>
    </SidebarGroup >
  )
}
