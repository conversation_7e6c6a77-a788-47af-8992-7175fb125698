import axios, { AxiosInstance } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import {
    ChatSession,
    ChatSessionResponse,
    ChatRequest,
    ChatResponse,
    Message,
    ChatUpdateRequest
} from '@/types/chat';


export const apiClient = async () => {
    const api = axios.create({
        baseURL: '/api/chat',
        headers: {
            'Content-Type': 'application/json'
        },
    });

    return api;
}

export const createChat = async (chat: ChatSession) => {
    const api = await apiClient();
    try {
        const response = await api.post('/v1/chat/new', {
            chat_id: chat.chat_id,
            title: chat.name
        });
        return response.data;
    } catch (error) {
        throw error;
    }
}

export const sendMessage = async (chatId: string, message: string) => {
    const api = await apiClient();
    const data = {
        "message": message,
        "chat_id": chatId,
        "kids": [

        ],
        "tools": [

        ],
        "data_sources": [

        ],
        "llm": "gpt-4o-mini"
    }
    try {
        const response = await api.post(`/v1/chat-completion/stream`, data);
        return response.data;
    } catch (error) {
        throw error;
    }
}


export const getChat = async (chatId: string) => {
    const api = await apiClient();
    try {
        const response = await api.get(`/v1/chat/${chatId}`);
        return response.data;
    } catch (error) {
        throw error;
    }
}


export const getChats = async () => {
    const api = await apiClient();
    try {
        const response = await api.get('/v1/chat/get_chats');
        const data = response.data;
        return {
            ...data,
            chats: (data.chats || []).map((chat: any) => ({
                chat_id: chat.chat_id,
                title: chat.title,
                updated_at: chat.updated_at,
                last_message: chat.last_message
            }))
        };
    } catch (error) {
        throw error;
    }
}
