import { PrismaService } from '@/prisma/prisma.service';
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { AddUserDto, UpdateAdminDto, VerifyAdminDto } from '../dtos/users.dto';
import { UpdateUserRoleDto } from '../dtos/update-role.dto';
import { UpdateOrganizationDto } from '../../dtos/organization.dto';
import {
  AddUserEmailDto,
  BulkAddUserEmailDto,
  VerifyOrgInvitationDto,
  CompleteOrgInvitationDto
} from '../dtos/add-user-email.dto';
import { EmailService } from 'src/email/email.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Role, InvitationType, InvitationStatus } from '@prisma/client';
@Injectable()
export class OrgUsersService {
  private saltRounds = 10;
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private emailService: EmailService,
  ) {}

  private async addUsers(data: AddUserDto[]) {
    if (!Array.isArray(data) || data.length === 0) {
      throw new BadRequestException('No user data provided');
    }
    const users = await this.prisma.user.createMany({
      data: data,
      skipDuplicates: true,
    });
    return users;
  }

  async getUsers(orgId: string) {
    const users = await this.prisma.user.findMany({
      where: {
        organizationId: orgId,
      },
    });
    return users;
  }

  async updateAdmin(data: UpdateAdminDto) {
    const hashedPassword = await bcrypt.hash(data.password, this.saltRounds);

    const admin = await this.prisma.user.update({
      where: {
        id: data.id,
        organizationId: data.organizationId,
      },
      data: {
        name: data.name,
        password: hashedPassword,
        emailVerified: true,
        hasOnboarded: true,
        onboardingCompleted: true,
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        organizationId: true,
        profileImageUrl: true,
        hasOnboarded: true,
        onboardingCompleted: true,
      },
    });

    const organization = await this.prisma.organization.findUnique({
      where: { id: data.organizationId },
      select: {
        id: true,
        name: true,
        logo: true,
      },
    });

    const accessToken = await this.jwtService.signAsync(
      {
        sub: admin.id,
        email: admin.email,
        name: admin.name,
        orgId: data.organizationId,
      },
      {
        secret: this.configService.get('auth.accessTokenKey.secret'),
        expiresIn: '7d',
      },
    );

    const refreshToken = await this.jwtService.signAsync(
      {
        sub: admin.id,
        email: admin.email,
        name: admin.name,
        orgId: data.organizationId,
      },
      {
        secret: this.configService.get('auth.refreshTokenKey.secret'),
        expiresIn: '30d',
      },
    );

    await this.prisma.user.update({
      where: { id: admin.id },
      data: {
        refreshToken,
      },
    });

    await this.prisma.invitation.updateMany({
      where: {
        email: admin.email,
        organizationId: data.organizationId,
        status: { in: ['PENDING', 'EXPIRED'] },
        type: 'ORGANIZATION',
      },
      data: {
        status: 'ACCEPTED',
        acceptedAt: new Date(),
      },
    });

    return {
      success: true,
      message: 'Admin setup completed successfully',
      admin,
      organization,
      tokens: {
        accessToken,
        refreshToken,
      },
    };
  }

  async getUser(userId: string, orgId: string) {
    const user = await this.prisma.user.findFirst({
      where: {
        id: userId,
        organizationId: orgId,
      },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return { success: true, user };
  }

  async verifyAdmin(verifyAdminDto: VerifyAdminDto) {
    try {
      const decoded = await this.jwtService.verifyAsync(verifyAdminDto.token, {
        secret: this.configService.get('auth.accessTokenKey.secret'),
      });

      if (decoded.type !== 'org-admin-invite') {
        throw new Error('Invalid token type');
      }

      const admin = await this.prisma.user.findFirst({
        where: {
          email: decoded.email,
          organizationId: decoded.organizationId,
          role: Role.ADMIN,
        },
      });

      const org = await this.prisma.organization.findFirst({
        where: {
          id: decoded.organizationId,
        },
      });

      if (!admin) {
        throw new NotFoundException('Admin not found');
      }

      const accessToken = await this.jwtService.signAsync(
        {
          sub: admin.id,
          email: admin.email,
          name: admin.name,
        },
        {
          secret: this.configService.get('auth.accessTokenKey.secret'),
          expiresIn: '7d',
        },
      );

      return {
        message: 'Admin verified successfully',
        accessToken,
        admin: {
          id: admin.id,
          email: admin.email,
        },
        org: {
          id: org.id,
          name: org.name,
          logo: org.logo,
        },
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  async addBulkUsers(data: AddUserDto[]) {
    const users = await this.addUsers(data);
    return { success: true, message: 'Users added successfully', users };
  }

  async addUser(userData: AddUserDto) {
    const existingUser = await this.prisma.user.findFirst({
      where: {
        email: userData.email,
      },
    });
    if (existingUser) {
      throw new BadRequestException('User already exists');
    }
    const user = await this.prisma.user.create({
      data: {
        email: userData.email,
        name: userData.name,
        password: await bcrypt.hash(userData.password, this.saltRounds),
        role: Role.USER,
        organizationId: userData.organizationId,
      },
    });
    return { success: true, message: 'User added successfully', user };
  }

  async updateUserRole(data: UpdateUserRoleDto) {
    const user = await this.prisma.user.findUnique({
      where: { id: data.userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id: data.userId },
      data: {
        role: data.role as Role,
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        organizationId: true,
      },
    });

    return {
      success: true,
      message: `User role updated to ${data.role} successfully`,
      user: updatedUser,
    };
  }

  async inviteUserByEmail(data: AddUserEmailDto) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: data.organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const existingUser = await this.prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      if (!existingUser.organizationId) {
        await this.prisma.user.update({
          where: { id: existingUser.id },
          data: { organizationId: data.organizationId },
        });

        return {
          success: true,
          message: 'User added to organization successfully',
          user: {
            id: existingUser.id,
            email: existingUser.email,
          },
        };
      } else if (existingUser.organizationId === data.organizationId) {
        return {
          success: true,
          message: 'User is already a member of this organization',
          user: {
            id: existingUser.id,
            email: existingUser.email,
          },
        };
      } else {
        throw new BadRequestException('User already belongs to another organization');
      }
    }

    const tempPassword = Math.random().toString(36).slice(-8);
    const hashedPassword = await bcrypt.hash(tempPassword, this.saltRounds);

    const newUser = await this.prisma.user.create({
      data: {
        email: data.email,
        password: hashedPassword,
        organizationId: data.organizationId,
      },
    });

    const token = await this.jwtService.signAsync(
      {
        email: newUser.email,
        userId: newUser.id,
        organizationId: data.organizationId,
        type: 'org-invitation',
      },
      {
        secret: this.configService.get('auth.accessTokenKey.secret'),
        expiresIn: '7d',
      },
    );

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    await this.prisma.invitation.create({
      data: {
        email: data.email,
        type: 'ORGANIZATION',
        token,
        expiresAt,
        invitedBy: data.invitedBy,
        organizationId: data.organizationId,
      },
    });

    // Send invitation email
    const invitationUrl = `${process.env.FRONTEND_URL}/onboarding?token=${token}`;
    await this.emailService.sendOrgInvitationEmail(
      data.email,
      organization.name,
      invitationUrl,
    );

    return {
      success: true,
      message: 'Invitation sent successfully',
      user: {
        id: newUser.id,
        email: newUser.email,
      },
    };
  }

  async inviteBulkUsersByEmail(data: BulkAddUserEmailDto) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: data.organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const results = [];

    for (const email of data.emails) {
      try {
        const existingUser = await this.prisma.user.findUnique({
          where: { email },
        });

        if (existingUser) {
          if (!existingUser.organizationId) {
            await this.prisma.user.update({
              where: { id: existingUser.id },
              data: { organizationId: data.organizationId },
            });

            results.push({
              email,
              status: 'success',
              message: 'User added to organization successfully',
            });
          } else if (existingUser.organizationId === data.organizationId) {
            results.push({
              email,
              status: 'success',
              message: 'User is already a member of this organization',
            });
          } else {
            results.push({
              email,
              status: 'error',
              message: 'User already belongs to another organization',
            });
          }
        } else {
          const tempPassword = Math.random().toString(36).slice(-8);
          const hashedPassword = await bcrypt.hash(tempPassword, this.saltRounds);

          const newUser = await this.prisma.user.create({
            data: {
              email,
              password: hashedPassword,
              organizationId: data.organizationId,
            },
          });

          const token = await this.jwtService.signAsync(
            {
              email: newUser.email,
              userId: newUser.id,
              organizationId: data.organizationId,
              type: 'org-invitation',
            },
            {
              secret: this.configService.get('auth.accessTokenKey.secret'),
              expiresIn: '7d',
            },
          );

          const expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + 7);

          await this.prisma.invitation.create({
            data: {
              email,
              type: 'ORGANIZATION',
              token,
              expiresAt,
              invitedBy: data.invitedBy,
              organizationId: data.organizationId,
            },
          });

          // Send invitation email
          const invitationUrl = `${process.env.FRONTEND_URL}/onboarding?token=${token}`;
          await this.emailService.sendOrgInvitationEmail(
            email,
            organization.name,
            invitationUrl,
          );

          results.push({
            email,
            status: 'success',
            message: 'Invitation sent successfully',
          });
        }
      } catch (error) {
        console.error(`Error processing email ${email}:`, error);
        results.push({
          email,
          status: 'error',
          message: `Error: ${error.message}`,
        });
      }
    }

    return {
      success: true,
      message: 'Bulk invitations processed',
      results,
    };
  }

  async verifyOrgInvitation(data: VerifyOrgInvitationDto) {
    try {
      const decoded = await this.jwtService.verifyAsync(data.token, {
        secret: this.configService.get('auth.accessTokenKey.secret'),
      });

      if (decoded.type !== 'org-invitation') {
        throw new BadRequestException('Invalid token type');
      }

      const user = await this.prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { id: true, email: true },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      const organization = await this.prisma.organization.findUnique({
        where: { id: decoded.organizationId },
        select: {
          id: true,
          name: true,
          logo: true,
        },
      });

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      return {
        success: true,
        message: 'Organization invitation token verified',
        user: {
          id: user.id,
          email: user.email,
        },
        organization,
      };
    } catch (error) {
      throw new BadRequestException(`Invalid or expired token: ${error.message}`);
    }
  }

  async verifyUnifiedToken(token: string) {
    try {
      const decoded = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('auth.accessTokenKey.secret'),
      });

      const tokenType = decoded.type;
      let isAdmin = false;
      let user = null;

      if (tokenType === 'org-invitation') {
        isAdmin = false;

        user = await this.prisma.user.findUnique({
          where: { id: decoded.userId },
          select: {
            id: true,
            email: true,
            name: true,
            role: true
          },
        });
      } else if (tokenType === 'org-admin-invite') {
        isAdmin = true;

        user = await this.prisma.user.findFirst({
          where: {
            email: decoded.email,
            organizationId: decoded.organizationId,
            role: Role.ADMIN,
          },
          select: {
            id: true,
            email: true,
            name: true,
            role: true
          },
        });
      } else {
        throw new BadRequestException(`Invalid token type: ${tokenType}`);
      }

      if (!user) {
        throw new NotFoundException('User not found');
      }

      const organization = await this.prisma.organization.findUnique({
        where: { id: decoded.organizationId },
        select: {
          id: true,
          name: true,
          logo: true,
        },
      });

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      return {
        success: true,
        message: 'Token verified successfully',
        tokenType,
        isAdmin,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        organization,
        nextStep: isAdmin ? 'update-admin' : 'complete-invitation',
        completionEndpoint: isAdmin ? '/v1/users/update-admin' : '/v1/users/invitation/complete'
      };
    } catch (error) {
      throw new BadRequestException(`Invalid or expired token: ${error.message}`);
    }
  }

  async completeOrgInvitation(data: CompleteOrgInvitationDto) {
    try {
      const decoded = await this.jwtService.verifyAsync(data.token, {
        secret: this.configService.get('auth.accessTokenKey.secret'),
      });

      if (decoded.type !== 'org-invitation') {
        throw new BadRequestException('Invalid token type');
      }

      const hashedPassword = await bcrypt.hash(data.password, this.saltRounds);

      const user = await this.prisma.user.update({
        where: { id: decoded.userId },
        data: {
          name: data.name,
          password: hashedPassword,
          emailVerified: true,
          hasOnboarded: true,
          onboardingCompleted: true,
        },
        select: {
          id: true,
          email: true,
          name: true,
          profileImageUrl: true,
          hasOnboarded: true,
          onboardingCompleted: true,
        },
      });

      const accessToken = await this.jwtService.signAsync(
        {
          sub: user.id,
          email: user.email,
          name: user.name,
          orgId: decoded.organizationId,
        },
        {
          secret: this.configService.get('auth.accessTokenKey.secret'),
          expiresIn: '7d',
        },
      );

      const refreshToken = await this.jwtService.signAsync(
        {
          sub: user.id,
          email: user.email,
          name: user.name,
          orgId: decoded.organizationId,
        },
        {
          secret: this.configService.get('auth.refreshTokenKey.secret'),
          expiresIn: '30d',
        },
      );

      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          refreshToken,
        },
      });

      await this.prisma.invitation.updateMany({
        where: {
          email: user.email,
          organizationId: decoded.organizationId,
          status: { in: ['PENDING', 'EXPIRED'] },
        },
        data: {
          status: 'ACCEPTED',
          acceptedAt: new Date(),
        },
      });

      return {
        success: true,
        message: 'Organization invitation completed successfully',
        user,
        tokens: {
          accessToken,
          refreshToken,
        },
      };
    } catch (error) {
      throw new BadRequestException(`Invalid or expired token: ${error.message}`);
    }
  }

  async getOrganization(organizationId: string, currentUserId: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
      select: {
        id: true,
        name: true,
        description: true,
        logo: true,
        slug: true,
        createdAt: true,
        updatedAt: true,
        users: {
          where: {
            id: currentUserId,
          },
          select: { id: true },
        },
        _count: {
          select: {
            users: true,
            teams: true,
          },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    if (organization.users?.length === 0) {
      throw new BadRequestException(
        'You must be a member of this organization to view it',
      );
    }

    const { users, ...organizationData } = organization;

    return {
      success: true,
      message: 'Organization details fetched successfully',
      organization: organizationData,
    };
  }

  async updateOrganization(
    organizationId: string,
    data: UpdateOrganizationDto,
    currentUserId: string,
  ) {
    // Verify the organization exists
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
      select: {
        id: true,
        name: true,
        description: true,
        logo: true,
        users: {
          where: {
            id: currentUserId,
            role: { in: ['ADMIN', 'SUPERADMIN'] },
          },
          select: { id: true, role: true },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Check if the current user is an admin of this organization
    if (organization.users.length === 0) {
      throw new BadRequestException(
        'You must be an admin of this organization to update it',
      );
    }

    // Validate that at least one field is being updated
    if (!data.description && !data.logo) {
      throw new BadRequestException(
        'At least one field (description or logo) must be provided',
      );
    }

    // Update the organization
    const updatedOrganization = await this.prisma.organization.update({
      where: { id: organizationId },
      data: {
        ...(data.description !== undefined && { description: data.description }),
        ...(data.logo !== undefined && { logo: data.logo }),
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        description: true,
        logo: true,
        slug: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      success: true,
      message: 'Organization updated successfully',
      organization: updatedOrganization,
    };
  }

  async getCurrentUserDetails(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        organizationId: true,
        profileImageUrl: true,
        hasOnboarded: true,
        onboardingCompleted: true,
        connectors: {
          include: {
            connector: {
              select: {
                id: true,
                name: true,
                type: true,
                logo: true,
                category: true,
                description: true,
                status: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const connectors = user.connectors.map((uc) => ({
      connectorId: uc.connector.id,
      name: uc.connector.name,
      type: uc.connector.type,
      logo: uc.connector.logo,
      category: uc.connector.category,
      description: uc.connector.description,
      status: uc.connector.status,
      connectedAt: uc.createdAt,
      updatedAt: uc.updatedAt,
      config: uc.config,
    }));

    const { connectors: _, ...userData } = user;

    return {
      success: true,
      message: 'User details fetched successfully',
      user: {
        ...userData,
        connectors,
        totalConnectors: connectors.length,
      },
    };
  }
}
