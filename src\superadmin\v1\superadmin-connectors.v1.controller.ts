import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { SuperAdminConnectorsService } from './superadmin-connectors.v1.service';
import { SuperAdminAuthGuard } from '../../auth/guards/superadmin-auth.guard';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CreateConnectorDto } from '../../connectors/dtos/addConnectors.dto';
import { UpdateConnectorDto } from '../dtos/update-connector.dto';
import { PaginationDto } from '../dtos/pagination.dto';

@ApiTags('SuperAdmin-Connectors')
@Controller({ version: '1', path: 'superadmin/connectors' })
@UseGuards(SuperAdminAuthGuard)
@ApiBearerAuth()
export class SuperAdminConnectorsController {
  constructor(
    private readonly superAdminConnectorsService: SuperAdminConnectorsService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all connectors (Admin)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'List of all connectors' })
  @HttpCode(HttpStatus.OK)
  async getAllConnectors(@Query() paginationDto: PaginationDto) {
    return this.superAdminConnectorsService.getAllConnectors(paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get connector by ID (Admin)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Connector details' })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Connector not found',
  })
  @HttpCode(HttpStatus.OK)
  async getConnectorById(@Param('id') id: string) {
    return this.superAdminConnectorsService.getConnectorById(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create new connector(s) (Admin)' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Connector(s) created successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async createConnector(@Body() createConnectorDto: CreateConnectorDto) {
    return this.superAdminConnectorsService.createConnector(createConnectorDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update connector (Admin)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Connector updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Connector not found',
  })
  @HttpCode(HttpStatus.OK)
  async updateConnector(
    @Param('id') id: string,
    @Body() updateConnectorDto: UpdateConnectorDto,
  ) {
    return this.superAdminConnectorsService.updateConnector(
      id,
      updateConnectorDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete connector (Admin)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Connector deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Connector not found',
  })
  @HttpCode(HttpStatus.OK)
  async deleteConnector(@Param('id') id: string) {
    return this.superAdminConnectorsService.deleteConnector(id);
  }

  @Get('stats/usage')
  @ApiOperation({ summary: 'Get connectors usage statistics (Admin)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Connectors usage statistics',
  })
  @HttpCode(HttpStatus.OK)
  async getConnectorsStats() {
    return this.superAdminConnectorsService.getConnectorsStats();
  }
}
