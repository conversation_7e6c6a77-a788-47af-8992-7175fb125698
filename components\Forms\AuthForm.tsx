"use client";

import { useState } from "react";
import { But<PERSON> } from "@steps-ai/ui";
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Input } from "@steps-ai/ui";
import { FcGoogle } from "react-icons/fc";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { toast } from "sonner";
import { useSearchParams } from "next/navigation";
import axios from "axios";
import { ArrowLeft } from 'lucide-react';


export function AuthForm() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const callbackUrl = '/';
    const search = searchParams.get("type");
    const [signup, setSignup] = useState(search === "register")
    const [loading, setLoading] = useState(false)
    const [mfaRequired, setMfaRequired] = useState(false)
    const [mfaCode, setMfaCode] = useState('')

    const googleAuth = () => {
        signIn("google", { callbackUrl });
    }

    const formik = useFormik({
        initialValues: {
            email: '',
            password: '',
            name: '',
        },
        validationSchema: Yup.object({
            email: Yup.string()
                .email('Invalid email address')
                .required('Email is required'),
            password: Yup.string()
                .required('No password provided.')
                .min(8, 'Password is too short - should be 8 chars minimum.')
                .matches(/(?=.*[0-9])/, 'Password must contain a number.'),

        }),
        onSubmit: async (values) => {
            setLoading(true)
            const { email, password, name } = values;
            if (signup) {
                const response = await signIn("register", { email, password, name, redirect: false });
                if (!response?.ok) {
                    toast.error(response?.error);
                    setLoading(false)
                } else {
                    router.push("/chat")
                }
            } else {
                if (!mfaRequired) {
                    await checkMfa(email);
                } else {
                    await submitWithMfa(email, password);
                }
            }
        },
    });

    const handleBackFromMfa = () => {
        setMfaRequired(false);
        setMfaCode('');
    };

    const checkMfa = async (email: string) => {
        try {
            const checkMfaResponse = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/auth/checktwofa/${email}`);
            if (checkMfaResponse.data.result) {
                setMfaRequired(true);
                setLoading(false);
            } else {
                await submitWithoutMfa(email, formik.values.password);
            }
        } catch (error) {
            // console.log(error);
            toast.error("Error checking MFA status");
            setLoading(false);
        }
    }

    const submitWithoutMfa = async (email: string, password: string) => {
        const response = await signIn("login", { email, password, redirect: false });
        if (!response?.ok) {
            toast.error(response?.error);
            setLoading(false);
        } else {
            router.push("/chat");
        }
    }

    const submitWithMfa = async (email: string, password: string) => {
        const response = await signIn("login", { email, password, mfaCode, redirect: false });
        if (!response?.ok) {
            toast.error(response?.error);
            setLoading(false);
        } else {
            router.push("/chat");
        }
    }

    const onToggle = () => {
        setLoading(false);
        setSignup(!signup)
        setMfaRequired(false)
        formik.resetForm()
    }

    return (
        <>
            <div className="flex justify-center">
                <div className="w-full max-w-md">
                    <div className="text-center">
                        <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-200">
                            {signup ? "Sign Up" : (mfaRequired ? "Enter MFA Code" : "Sign In")}
                        </h1>
                    </div>
                    <div className="mt-8">
                        <form onSubmit={formik.handleSubmit}>
                            {!mfaRequired && (
                                <>
                                    {signup && (
                                        <Input
                                            id="name"
                                            name="name"
                                            type="text"
                                            onChange={formik.handleChange}
                                            value={formik.values.name}
                                            placeholder="Name"
                                            className="mt-2"
                                            required
                                        />
                                    )}

                                    <Input
                                        id="email"
                                        name="email"
                                        type="email"
                                        onChange={formik.handleChange}
                                        value={formik.values.email}
                                        placeholder="Email"
                                        className="mt-2"
                                        required
                                    />
                                    {formik.touched.email && formik.errors.email ? (
                                        <div className="text-red-500 text-xs mt-1">{formik.errors.email}</div>
                                    ) : null}

                                    <Input
                                        id="password"
                                        name="password"
                                        type="password"
                                        onChange={formik.handleChange}
                                        value={formik.values.password}
                                        placeholder="Password"
                                        className="mt-2"
                                        required
                                    />
                                    {formik.touched.password && formik.errors.password ? (
                                        <div className="text-red-500 text-xs mt-1">{formik.errors.password}</div>
                                    ) : null}

                                    {/* {!signup && (
                                        <div className="mt-6">
                                            <div className="text-sm">
                                                <a
                                                    href="#"
                                                    className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-gray-300 dark:hover:text-white"
                                                >
                                                    Forgot your password?
                                                </a>
                                            </div>
                                        </div>
                                    )} */}
                                </>
                            )}

                            {mfaRequired && (
                                <>
                                    <Input
                                        id="mfaCode"
                                        name="mfaCode"
                                        type="text"
                                        onChange={(e) => setMfaCode(e.target.value)}
                                        value={mfaCode}
                                        placeholder="Enter MFA Code"
                                        className="mt-2"
                                        required
                                    />

                                    <Button
                                        type="button"
                                        variant="outline"
                                        className="mt-2 border-none "
                                        onClick={handleBackFromMfa}
                                    >
                                        <ArrowLeft /> Back to Login
                                    </Button>
                                </>
                            )}

                            <div className="mt-6">
                                <Button
                                    type="submit"
                                    className="w-full"
                                    disabled={loading}
                                >
                                    {loading ? "Loading..." : (signup ? "Sign Up" : (mfaRequired ? "Verify MFA" : "Sign In"))}
                                </Button>
                            </div>
                        </form>

                        {!mfaRequired && (
                            <>
                                <div className="mt-6 flex flex-col gap-2">
                                    <div className="text-md">
                                        <Button
                                            variant={"outline"}
                                            className="font-medium w-full flex gap-2"
                                            onClick={googleAuth}
                                        >
                                            <FcGoogle size={25} />
                                            {signup ? "Sign Up" : "Login"} with Google
                                        </Button>
                                    </div>
                                </div>

                                {signup ? (
                                    <div className="mt-6 flex items-end justify-center w-full">
                                        <div className="text-sm">
                                            <Button
                                                variant={"link"}
                                                onClick={onToggle}
                                                className="font-medium text-indigo-600 hover:text-indigo-500 w-full dark:text-gray-300 dark:hover:text-white"
                                            >
                                                Already have an account? Login
                                            </Button>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="mt-6 flex items-center justify-center w-full">
                                        <div className="text-sm">
                                            <Button
                                                onClick={onToggle}
                                                variant={"link"}
                                                className="font-medium text-indigo-600 hover:text-indigo-500 w-full dark:text-gray-300 dark:hover:text-white"
                                            >
                                                Don&apos;t have an account? Sign Up
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}

