"use client";

import Image from 'next/image';
import { UserMessage as UserMessageType, Message } from '@/types/chat';
import { useUser } from '@/contexts/UserContext';

interface UserMessageProps {
    message: UserMessageType | Message;
}

export default function UserMessage({ message }: UserMessageProps) {
    const { user } = useUser();

    return (
        <div className='flex items-center gap-2 justify-end max-w-4xl mx-auto mt-5'>
            <div className='rounded-lg inline-block'>
                <div className="flex items-center gap-2 justify-end w-full">
                    <span className='text-sm text-muted-foreground'>
                        {user?.name?.split(" ")[0] || "You"}
                    </span>
                    <Image src={user?.image || '/ai-logo.svg'} alt='/ai-logo.png' height={20} width={20} className="rounded-full" />
                </div>
                <div className="p-4 bg-accent flex-grow mb-2 mx-0 border-none pt-2 rounded-lg">
                    <div className="text-accent-foreground">{message.message}</div>
                </div>
            </div>
        </div>
    );
}