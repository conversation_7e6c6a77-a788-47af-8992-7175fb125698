import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class HubspotCallbackDto {
  @ApiProperty({
    description: 'The code from Notion',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'The state from Notion',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  state: string;
}

export class HubspotRefreshTokenDto {
  @ApiProperty({
    description: 'The user id',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
}
