import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUrl, MaxLength } from 'class-validator';
import { ConnectorsStatus, ConnectorsType } from '@prisma/client';

export class UpdateConnectorDto {
  @ApiProperty({
    description: 'Connector name',
    example: 'Notion',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @ApiProperty({
    description: 'Connector logo URL',
    example: 'https://example.com/notion-logo.svg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  logo?: string;

  @ApiProperty({
    description: 'Connector category',
    example: 'Productivity',
    required: false,
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({
    description: 'Connector description',
    example: 'Connect to your Notion workspace',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    description: 'Connector slug',
    example: 'notion',
    required: false,
  })
  @IsOptional()
  @IsString()
  slug?: string;

  @ApiProperty({
    description: 'Connector status',
    enum: ConnectorsStatus,
    example: 'AVAILABLE',
    required: false,
  })
  @IsOptional()
  @IsEnum(ConnectorsStatus)
  status?: ConnectorsStatus;

  @ApiProperty({
    description: 'Connector type',
    enum: ConnectorsType,
    example: 'FREE',
    required: false,
  })
  @IsOptional()
  @IsEnum(ConnectorsType)
  type?: ConnectorsType;
} 