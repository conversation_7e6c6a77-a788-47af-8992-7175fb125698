import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class TeamSpecificAdminGuard extends AuthGuard('jwt') {
  constructor(private prismaService: PrismaService) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isAuthenticated = await super.canActivate(context);
    if (!isAuthenticated) {
      return false;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.sub) {
      throw new UnauthorizedException('User authentication failed');
    }

    const userId = request.user.sub;
    
    // Get the team ID from the request parameters
    const teamId = request.params.id || request.body.teamId;
    
    if (!teamId) {
      throw new NotFoundException('Team ID not found in request');
    }

    // Get the user's role
    const dbUser = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    // SuperAdmin and Admin can manage any team
    if (dbUser && (dbUser.role === 'SUPERADMIN' || dbUser.role === 'ADMIN')) {
      return true;
    }

    // For TeamAdmin, check if they are a member of the team they're trying to manage
    if (dbUser && dbUser.role === 'TEAMADMIN') {
      // Check if the user is a member of the team
      const team = await this.prismaService.team.findUnique({
        where: { id: teamId },
        include: {
          members: {
            where: { id: userId },
            select: { id: true },
          },
        },
      });

      if (!team) {
        throw new NotFoundException('Team not found');
      }

      if (team.members.length === 0) {
        throw new ForbiddenException('You are not a member of this team');
      }

      return true;
    }

    throw new UnauthorizedException('Insufficient permissions to manage this team');
  }
}
