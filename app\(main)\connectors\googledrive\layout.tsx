import { Metadata } from "next"
import { ConnectorLayout } from "@/components/Layouts/ConnectorLayout"
import { assets } from "@/lib/assets"
export const metadata: Metadata = {
    title: "Google Drive",
}

interface GoogleDriveLayoutProps {
    children: React.ReactNode
}

export default function GoogleDriveLayout({ children }: GoogleDriveLayoutProps) {
    return (
        <ConnectorLayout
            title="Google Drive"
            description="Here would be the description for google drive"
            isLoading={false}
            logo={assets.Icons.Google}
        >
            {children}
        </ConnectorLayout>
    )
}