import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>2, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@steps-ai/ui"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@steps-ai/ui"

interface ProductUpdatesProps {
    className?: string
}

export function ProductUpdates({ className }: ProductUpdatesProps) {
    return (
        <Card className="border-none bg-gradient-to-br from-violet-500/20 via-purple-500/20 to-pink-500/20 shadow-sm mb-5">
            <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                    <CalendarCheck2 className="mr-1 h-4 w-4 text-violet-500" />
                    Product Updates
                </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
                <div className="mb-2">
                    <div className="flex items-center gap-1 text-xs font-semibold">
                        <Sparkles className="h-3 w-3 text-violet-500" />
                        What's New
                    </div>
                    <ul className="ml-5 mt-1 list-disc text-xs text-gray-800 dark:text-gray-200">
                        <li>New integration with Slack</li>
                        <li>Improved search performance</li>
                    </ul>
                </div>
                <div>
                    <div className="flex items-center gap-1 text-xs font-semibold">
                        <Clock className="h-3 w-3 text-purple-500" />
                        What's Upcoming
                    </div>
                    <ul className="ml-5 mt-1 list-disc text-xs text-gray-800 dark:text-gray-200">
                        <li>Team collaboration features</li>
                        <li>Customizable notifications</li>
                    </ul>
                </div>
            </CardContent>
            <CardFooter>
                <Button
                    asChild
                    className="w-full bg-gradient-to-r from-violet-600 to-purple-600 text-white hover:from-violet-700 hover:to-purple-700"
                >
                    <Link href="/changelog">View Changelog</Link>
                </Button>
            </CardFooter>
        </Card>
    )
}
