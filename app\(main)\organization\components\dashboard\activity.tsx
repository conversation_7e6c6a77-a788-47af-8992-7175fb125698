"use client"

import { useState, useEffect } from "react"
import { useUser } from "@/contexts/UserContext"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, UserPlus, Calendar, Users, Link, CheckCircle, Activity as ActivityIcon } from "lucide-react"
import { ActivityFeedItem, organizationApi } from "@/lib/api/organization"

const getActivityIcon = (type: ActivityFeedItem['type']) => {
    switch (type) {
        case 'USER_JOINED':
            return Users;
        case 'TEAM_CREATED':
            return UserPlus;
        case 'CONNECTOR_CONNECTED':
            return Link;
        case 'MEETING_SCHEDULED':
            return Calendar;
        case 'INVITATION_SENT':
            return UserPlus;
        case 'ONBOARDING_COMPLETED':
            return CheckCircle;
        default:
            return ActivityIcon;
    }
}

const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
}

const getInitials = (name: string) => {
    return name
        .split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .join('')
        .slice(0, 2)
}

export default function Activity() {
    const { user } = useUser()
    const [activities, setActivities] = useState<ActivityFeedItem[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const fetchActivityFeed = async () => {
            if (!user?.organizationId) return

            try {
                setLoading(true)
                const response = await organizationApi.getActivityFeed({
                    organizationId: user.organizationId,
                    limit: 3,
                    page: 1
                })
                
                if (response.result?.success) {
                    setActivities(response.result.activities)
                }
            } catch (err) {
                console.error('Error fetching activity feed:', err)
                setError('Failed to load activity feed')
            } finally {
                setLoading(false)
            }
        }

        fetchActivityFeed()
    }, [user?.organizationId])

    // Render loading state
    if (loading) {
        return (
            <Card className="w-full bg-transparent border-none mx-auto">
                <div className="text-xs font-semibold text-muted-foreground px-2 pt-2 pb-1">Recent Activity</div>
                <CardContent className="space-y-1 p-2">
                    {[...Array(3)].map((_, i) => (
                        <div key={i} className="flex items-center gap-2 px-1 py-1 animate-pulse">
                            <div className="h-6 w-6 bg-gray-200 rounded-full"></div>
                            <div className="h-3 bg-gray-200 rounded w-20"></div>
                            <div className="h-3 bg-gray-100 rounded w-16"></div>
                            <div className="h-3 w-3 bg-gray-200 rounded ml-1"></div>
                            <div className="h-2 bg-gray-100 rounded w-8 ml-auto"></div>
                        </div>
                    ))}
                </CardContent>
            </Card>
        )
    }

    // Render error state
    if (error) {
        return (
            <Card className="w-full bg-transparent border-none mx-auto">
                <div className="text-xs font-semibold text-muted-foreground px-2 pt-2 pb-1">Recent Activity</div>
                <CardContent className="space-y-1 p-2">                    <div className="text-center py-4">
                        <ActivityIcon className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                        <p className="text-xs text-muted-foreground">{error}</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    // Render empty state
    if (activities.length === 0) {
        return (
            <Card className="w-full bg-transparent border-none mx-auto">
                <div className="text-xs font-semibold text-muted-foreground px-2 pt-2 pb-1">Recent Activity</div>
                <CardContent className="space-y-1 p-2">                    <div className="text-center py-4">
                        <ActivityIcon className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                        <p className="text-xs text-muted-foreground">No recent activity</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card className="w-full bg-transparent border-none mx-auto">
            <div className="text-xs font-semibold text-muted-foreground px-2 pt-2 pb-1">Recent Activity</div>
            <CardContent className="space-y-1 p-2">
                {activities.map((activity) => {
                    const IconComponent = getActivityIcon(activity.type)
                    return (
                        <div
                            key={activity.id}
                            className="flex items-center gap-2 px-1 py-1"
                        >
                            <Avatar className="h-6 w-6">
                                <AvatarImage 
                                    src={activity.user.profileImageUrl || "/placeholder.svg"} 
                                    alt={activity.user.name} 
                                />
                                <AvatarFallback className="text-[10px] font-medium">
                                    {getInitials(activity.user.name)}
                                </AvatarFallback>
                            </Avatar>
                            <span className="text-xs text-foreground truncate font-medium">
                                {activity.user.name}
                            </span>
                            <span className="text-xs text-muted-foreground truncate">
                                {activity.description || activity.title}
                            </span>
                            <IconComponent className="h-3 w-3 text-muted-foreground ml-1" />
                            <span className="text-[10px] text-muted-foreground ml-auto">
                                {formatTimeAgo(activity.timestamp)}
                            </span>
                        </div>
                    )
                })}
            </CardContent>
        </Card>
    )
}
