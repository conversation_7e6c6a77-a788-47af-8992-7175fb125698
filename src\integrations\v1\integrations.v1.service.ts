import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateIntegrationDto } from '../dtos/createIntegration.dto';
import { PrismaService } from '@/prisma/prisma.service';



@Injectable()
export class IntegrationsService {
    constructor(private configService: ConfigService, private readonly prismaService: PrismaService,) { }

    async getIntegrations(userId: string) {
        const integrations = await this.prismaService.integrations.findMany();
        return integrations;
    }

    async integrationByslug(userId: string, slug: string) {
        const integration = await this.prismaService.integrations.findFirst({
            where: { slug: slug },
        });
        return integration;
    }

    async createIntegrations(data: CreateIntegrationDto) {
        try {
            const integrations = await this.prismaService.integrations.createMany({
                data: data.integrations,
            });
            return integrations;
        } catch (error) {
            throw new BadRequestException(error);
        }
    }
}   
