"use client";

import axios, { AxiosInstance } from 'axios';
import { getSession } from 'next-auth/react';
import { v4 as uuidv4 } from 'uuid';
import {
    ChatSession,
    ChatSessionResponse,
    ChatRequest,
    ChatResponse,
    Message,
    ChatUpdateRequest
} from '@/types/chat';

interface IChatOperations {
    getAllChats(): Promise<ChatSessionResponse[]>;
    createChat(chat: Partial<ChatSession>): Promise<ChatSessionResponse>;
    sendMessage(request: ChatRequest): Promise<ChatResponse>;
    getChat(chatId: string): Promise<ChatSessionResponse>;
    getMessages(chatId: string): Promise<Message[]>;
    getStarredChats(): Promise<ChatSessionResponse[]>;
    generateTitle(message: string): Promise<string>;
    updateChatTitle(chatId: string, title: string): Promise<ChatSessionResponse>;
    updateChat(request: ChatUpdateRequest): Promise<ChatSessionResponse>;
    checkGoogleDriveConnection(): Promise<boolean>;
    connectGoogleDrive(): Promise<{ authorization_url: string }>;
    disconnectGoogleDrive(): Promise<void>;
    checkOneDriveConnection(): Promise<boolean>;
    connectOneDrive(): Promise<{ auth_url: string }>;
    disconnectOneDrive(): Promise<void>;
    indexOneDriveFiles(): Promise<void>;
}

class ChatClient implements IChatOperations {
    private static instance: ChatClient;
    private baseUrl: string;
    private chatApiClient: AxiosInstance;
    private token: string;

    private constructor() {
        this.token = "";
        this.baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        this.chatApiClient = axios.create({
            baseURL: process.env.NEXT_PUBLIC_CHAT_API_URL,
            headers: {
                'accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            withCredentials: true,
        });

        // Add response interceptor for error handling
        this.chatApiClient.interceptors.response.use(
            response => response,
            error => {
                if (error.response) {
                    console.error('Server Error Response:', {
                        status: error.response.status,
                        data: error.response.detail,
                        headers: error.response.headers
                    });
                } else if (error.request) {
                    console.error('Network Error (possibly CORS):', {
                        message: error.message,
                        config: {
                            url: error.config?.url,
                            method: error.config?.method,
                            headers: error.config?.headers
                        }
                    });
                }
                return Promise.reject(error);
            }
        );
    }

    public static getInstance(): ChatClient {
        if (!ChatClient.instance) {
            ChatClient.instance = new ChatClient();
        }
        return ChatClient.instance;
    }

    async getAllChats(): Promise<ChatSessionResponse[]> {
        try {
            const response = await this.chatApiClient.get<ChatSessionResponse[]>('/v1/chats');
            const data = response.data;
            return data.map((chat: ChatSessionResponse) => ({
                chat_id: chat.chat_id,
                user_id: chat.user_id,
                name: chat.name,
                last_message: chat.last_message,
                updated_at: chat.updated_at,
                created_at: chat.created_at,
                starred: chat.starred
            }));
        } catch (error) {
            console.error('Error fetching chats:', error);
            throw error;
        }
    }

    async getStarredChats(): Promise<ChatSessionResponse[]> {
        try {
            const response = await this.chatApiClient.get<ChatSessionResponse[]>('/v1/chats');
            return response.data.filter(chat => chat.starred === true);
        } catch (error) {
            console.error('Error in getStarredChats:', error);
            throw error;
        }
    }

    async createChat(chat: Partial<ChatSession> = {}): Promise<ChatSessionResponse> {
        try {
            const chatData = {
                chat_id: uuidv4(),
                name: chat.name || "Untitled Chat"
            };
            const response = await this.chatApiClient.post<ChatSessionResponse>('/v1/chat/new', chatData);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async generateTitle(message: string): Promise<string> {
        try {
            const session = await getSession();
            if (!session) {
                return "New Chat";
            }

            const response = await fetch("/api/chat/title", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ input: message }),
                credentials: 'include'
            });

            if (!response.ok) {
                const error = await response.json();
                return "New Chat";
            }

            const data = await response.json();
            return data.title || "New Chat";
        } catch (error) {
            console.error('Error in generateTitle:', error);
            return "New Chat";
        }
    }

    async updateChatTitle(chatId: string, title: string): Promise<ChatSessionResponse> {
        try {
            const response = await this.chatApiClient.patch<ChatSessionResponse>(`/v1/chats/${chatId}`, {
                name: title,
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async updateChat(request: ChatUpdateRequest): Promise<ChatSessionResponse> {
        try {
            const updatePayload = {
                chat_id: request.chat_id,
                name: request.name || "Untitled Chat",
                starred: request.starred
            };

            const response = await this.chatApiClient.put<ChatSessionResponse>('/v1/chats', updatePayload);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async sendMessage(request: ChatRequest): Promise<ChatResponse> {
        try {
            const response = await this.chatApiClient.post<ChatResponse & { chat_id?: string }>('/v1/get_chat_response', {
                message: request.message,
                chat_id: request.chat_id,
                allowed_kids: request.allowed_kids || [],
                allowed_tools: request.allowed_tools || [],
                allowed_data_sources: request.allowed_data_sources || [],
                llm: "gpt-4o-mini"
            }, {
                responseType: 'json'
            });

            // if (request.message && !request.chat_id && response.data.chat_id) {
            //     const title = await this.generateTitle(request.message);
            //     // await this.updateChatTitle(response.data.chat_id, title);
            // }

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async getChat(chatId: string): Promise<ChatSessionResponse> {
        try {
            const response = await this.chatApiClient.get<ChatSessionResponse>(`/v1/chats/${chatId}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async getMessages(chatId: string): Promise<Message[]> {
        try {
            const response = await this.chatApiClient.get<Message[]>('/v1/messages', {
                params: {
                    chat_id: chatId
                }
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async checkGoogleDriveConnection(): Promise<boolean> {
        try {
            const response = await this.chatApiClient.get('/authorized/gdrive');
            return response.data !== null && response.data !== false;
        } catch (error) {
            return false;
        }
    }

    async connectGoogleDrive(): Promise<{ authorization_url: string }> {
        try {
            const response = await this.chatApiClient.get<{ authorization_url: string }>('/connect/gdrive');
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async disconnectGoogleDrive(): Promise<void> {
        try {
            await this.chatApiClient.post('/gdrive/forget');
        } catch (error) {
            throw error;
        }
    }

    async checkOneDriveConnection(): Promise<boolean> {
        try {
            const response = await this.chatApiClient.get('/onedrive/authorized');
            return response.data !== null && response.data !== false;
        } catch (error) {
            return false;
        }
    }

    async connectOneDrive(): Promise<{ auth_url: string }> {
        try {
            const response = await this.chatApiClient.get<{ auth_url: string }>('/onedrive/connect');
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async disconnectOneDrive(): Promise<void> {
        try {
            await this.chatApiClient.post('/onedrive/forget');
        } catch (error) {
            throw error;
        }
    }

    async indexOneDriveFiles(): Promise<void> {
        try {
            await this.chatApiClient.post('/onedrive/index', {});
        } catch (error) {
            throw error;
        }
    }
}

export const chatClient = ChatClient.getInstance();
