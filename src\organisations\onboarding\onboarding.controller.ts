import {
  Controller,
  Get,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { OnboardingService } from './onboarding.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { UpcomingMeetingsResponseDto } from './dtos/onboarding.dto';

@ApiTags('Onboarding-V1')
@Controller({ version: '1', path: 'onboarding' })
@UseGuards(JwtAuthGuard)
export class OnboardingController {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Get(':userId/meetings')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user onboarding status and final verdict',
    description: 'Retrieves the status of user onboarding steps and final verdict for accessing meetings.',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'b9c2e3a7-43d5-4ed5-89ad-86448e40143f',
  })
  @ApiResponse({
    status: 200,
    description: 'Onboarding status retrieved successfully',
    type: UpcomingMeetingsResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not authorized to view this user\'s status',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Not authorized to view this user\'s status' },
        error: { type: 'string', example: 'Forbidden' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'User not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  async getUpcomingMeetings(
    @Param('userId') userId: string,
    @GetCurrentUser() currentUser: JwtPayload,
  ): Promise<UpcomingMeetingsResponseDto> {
    return this.onboardingService.getUpcomingMeetings(userId, currentUser.sub);
  }
} 