export interface ChatMessage {
    id: string;
    role: 'system' | 'user' | 'assistant' | 'data';
    content: string;
    sources?: any[];
    createdAt?: Date;
    name?: string;
    data?: any;
    annotations?: any[];
    toolInvocations?: ToolInvocation[];
}

export interface ToolInvocation {
    state: 'partial-call' | 'call' | 'result';
    toolCallId: string;
    toolName: string;
    args: any;
    result?: any;
}

export interface FetchOptions {
    method: string;
    headers: Record<string, string>;
    body?: string;
}

export interface UseChatOptions {
    api?: string;
    keepLastMessageOnError?: boolean;
    id?: string;
    initialInput?: string;
    allowEmptySubmit?: boolean;
    initialMessages?: ChatMessage[];
    maxSteps?: number;
}
