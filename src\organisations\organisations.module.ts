import { PrismaModule } from '@/prisma/prisma.module';
import { Module } from '@nestjs/common';
import { OrgUsersService } from './users/v1/users.service';
import { OrgTeamsService } from './teams/v1/teams.service';
import { OrgUsersController } from './users/v1/users.controller';
import { OrgTeamsController } from './teams/v1/teams.controller';
import { OnboardingController } from './onboarding/onboarding.controller';
import { OnboardingService } from './onboarding/onboarding.service';

import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from 'src/auth/auth.module';
import { EmailModule } from 'src/email/email.module';
import { TeamSpecificAdminGuard } from 'src/auth/guards/team-specific-admin.guard';
import { InvitationsModule } from './invitations/invitations.module';
import { DynamoDBModule } from 'src/dynamodb/dynamodb.module';
import { ConnectorsModule } from 'src/connectors/connectors.module';
import { ActivitiesModule } from 'src/activities/activities.module';

@Module({
  imports: [
    PrismaModule,
    JwtModule,
    ConfigModule,
    AuthModule,
    EmailModule,
    InvitationsModule,
    DynamoDBModule,
    ConnectorsModule,
    ActivitiesModule,
  ],
  providers: [
    OrgUsersService,
    OrgTeamsService,
    OnboardingService,
    {
      provide: 'TeamSpecificAdminGuard',
      useClass: TeamSpecificAdminGuard,
    },
  ],
  controllers: [OrgUsersController, OrgTeamsController, OnboardingController],
  exports: [OrgUsersService, OnboardingService],
})
export class OrganisationsModule {}
