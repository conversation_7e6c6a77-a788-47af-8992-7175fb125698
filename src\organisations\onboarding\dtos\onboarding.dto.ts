import { ApiProperty } from '@nestjs/swagger';

export class MeetingDto {
  @ApiProperty({
    description: 'Meeting ID',
    example: 'abc123',
  })
  id: string;

  @ApiProperty({
    description: 'Meeting title',
    example: 'Team Standup',
  })
  summary: string;

  @ApiProperty({
    description: 'Meeting start time',
    example: '2024-03-20T10:00:00Z',
  })
  start: string;

  @ApiProperty({
    description: 'Meeting end time',
    example: '2024-03-20T11:00:00Z',
  })
  end: string;

  @ApiProperty({
    description: 'Meeting location',
    example: 'Conference Room A',
    nullable: true,
  })
  location?: string;

  @ApiProperty({
    description: 'Meeting description',
    example: 'Weekly team sync',
    nullable: true,
  })
  description?: string;
}

export class UpcomingMeetingsResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Status retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Whether the user has created a chat',
    example: true,
  })
  hasCreatedChat: boolean;

  @ApiProperty({
    description: 'Whether the user has completed onboarding',
    example: true,
  })
  hasCompletedOnboarding: boolean;

  @ApiProperty({
    description: 'Whether the user has connected Google Calendar',
    example: true,
  })
  hasConnectedCalendar: boolean;

  @ApiProperty({
    description: 'Final verdict indicating if all conditions are met',
    example: true,
  })
  finalVerdict: boolean;

  @ApiProperty({
    description: 'List of upcoming meetings',
    type: [MeetingDto],
    example: [
      {
        id: 'abc123',
        summary: 'Team Standup',
        start: '2024-03-20T10:00:00Z',
        end: '2024-03-20T11:00:00Z',
        location: 'Conference Room A',
        description: 'Weekly team sync',
      },
    ],
  })
  upcomingMeetings: MeetingDto[];
} 