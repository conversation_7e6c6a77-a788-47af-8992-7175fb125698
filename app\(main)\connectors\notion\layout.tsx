import { Metadata } from "next"
import { ConnectorLayout } from "@/components/Layouts/ConnectorLayout"
import { assets } from "@/lib/assets"
export const metadata: Metadata = {
    title: "Notion",
}

interface NotionLayoutProps {
    children: React.ReactNode
}

export default function NotionLayout({ children }: NotionLayoutProps) {
    return (
        <ConnectorLayout
            title="Notion"
            description="Connect your Notion workspace to access and manage your documents"
            isLoading={false}
            logo={assets.Icons.Notion}
        >
            {children}
        </ConnectorLayout>
    )
}