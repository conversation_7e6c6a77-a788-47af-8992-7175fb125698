
import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import ConnectorsClient from "./connectorsConfigClient";
import { Metadata } from "next";

export const metadata: Metadata = {
    title: "Connectors",
};


const ConnectorsPage = async ({ params }: { params: { slug: string } }) => {
    const session = await getSession();

    if (!session) {
        redirect("/auth");
    }

    return (
        <>
            <ConnectorsClient params={params} />
        </>
    );
};

export default ConnectorsPage;
