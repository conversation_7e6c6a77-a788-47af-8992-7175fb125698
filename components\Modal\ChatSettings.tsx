'use client'

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"


const models = [
    { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
    { value: "gpt-4", label: "GPT-4" },
    { value: "claude-v1", label: "Claude v1" },
]

const templates = [
    { value: "default", label: "Default" },
    { value: "academic", label: "Academic" },
    { value: "creative", label: "Creative" },
    { value: "professional", label: "Professional" },
]

const knowledgebases = [
    { value: "general", label: "General Knowledge" },
    { value: "science", label: "Scientific Papers" },
    { value: "legal", label: "Legal Documents" },
    { value: "medical", label: "Medical Research" },
    { value: "tech", label: "Technology Articles" },
]

export default function ChatSettingsModal() {
    const [open, setOpen] = React.useState(false)

    const [selectedKnowledgebase, setSelectedKnowledgebase] = React.useState("")

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <div className="text-gray-600">
                    <div className="w-5 h-5 grid grid-cols-2 gap-0.5">
                        <div className="bg-current rounded-sm"></div>
                        <div className="bg-current rounded-sm"></div>
                        <div className="bg-current rounded-sm"></div>
                        <div className="bg-current rounded-sm"></div>
                    </div>
                </div>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Chat Settings</DialogTitle>
                    <DialogDescription>
                        Customize your RAG chat experience. Changes will apply to new conversations.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="model" className="text-right">
                            Model
                        </Label>
                        <Select defaultValue="gpt-3.5-turbo">
                            <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select a model" />
                            </SelectTrigger>
                            <SelectContent>
                                {models.map((model) => (
                                    <SelectItem key={model.value} value={model.value}>
                                        {model.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="template" className="text-right">
                            Template
                        </Label>
                        <Select defaultValue="default">
                            <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select a template" />
                            </SelectTrigger>
                            <SelectContent>
                                {templates.map((template) => (
                                    <SelectItem key={template.value} value={template.value}>
                                        {template.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="flex flex-col  items-start gap-4">
                        <Label className="text-right">Knowledgebase</Label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="outline"
                                    role="combobox"
                                    className={cn(
                                        "col-span-3 justify-between",
                                        !selectedKnowledgebase && "text-muted-foreground"
                                    )}
                                >
                                    {selectedKnowledgebase
                                        ? knowledgebases.find((kb) => kb.value === selectedKnowledgebase)?.label
                                        : "Select knowledgebase..."}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[250px] p-0">
                                <Command>
                                    <CommandInput placeholder="Search knowledgebase..." />
                                    <CommandEmpty>No knowledgebase found.</CommandEmpty>
                                    <CommandGroup>
                                        {knowledgebases.map((kb) => (
                                            <CommandItem
                                                key={kb.value}
                                                onSelect={() => {
                                                    setSelectedKnowledgebase(kb.value === selectedKnowledgebase ? "" : kb.value)
                                                }}
                                            >
                                                <Check
                                                    className={cn(
                                                        "mr-2 h-4 w-4",
                                                        selectedKnowledgebase === kb.value ? "opacity-100" : "opacity-0"
                                                    )}
                                                />
                                                {kb.label}
                                            </CommandItem>
                                        ))}
                                    </CommandGroup>
                                </Command>
                            </PopoverContent>
                        </Popover>
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit">Save changes</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}