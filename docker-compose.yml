version: '3.8'

services:
  postgres:
    image: postgres:13.5
    container_name: stepsai_postgres
    restart: always
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: stepsai_pgadmin
    restart: always
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - '5050:80'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    healthcheck:
      test: ['C<PERSON>', 'curl', '-f', 'http://localhost:80/']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  dynamodb:
    image: amazon/dynamodb-local:latest
    container_name: stepsai_dynamodb
    restart: always
    ports:
      - '8000:8000'
    command: '-jar DynamoDBLocal.jar -sharedDb -dbPath /home/<USER>'
    volumes:
      - dynamodb_data:/home/<USER>
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8000/shell/']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    container_name: stepsai_dynamodb_admin
    restart: always
    ports:
      - '8001:8001'
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb:8000
      - AWS_REGION=us-west-2
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY}
      - AWS_SECRET_ACCESS_KEY=${AWS_KEY_SECRET}
    depends_on:
      - dynamodb
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8001/']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

volumes:
  postgres_data:
    name: stepsai_postgres_data
  pgadmin_data:
    name: stepsai_pgadmin_data
  dynamodb_data:
    name: stepsai_dynamodb_data

networks:
  default:
    name: stepsai_network
    driver: bridge
