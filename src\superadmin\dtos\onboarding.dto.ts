import { Is<PERSON>mail, <PERSON>NotEmpt<PERSON>, <PERSON><PERSON>ption<PERSON>, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOrganizationDto {
  @ApiProperty({
    description: 'Name of the organization',
    example: 'Acme Corporation',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the organization',
    example: 'A leading provider of cloud solutions',
    required: false,
  })
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Organization logo',
    example: 'https://example.com/logo.png',
    required: false,
  })
  @IsString()
  @IsOptional()
  logo?: string;
}

export class CreateOrganizationAdminDto {
  @ApiProperty({
    description: 'Email of the organization admin',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Organization ID of the organization admin',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  organizationId: string;
}
