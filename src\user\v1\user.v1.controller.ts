import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  HttpCode,
  UseGuards,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { UserService } from './user.v1.service';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { UpdatePasswordDto } from '../dtos/updatePassword.dto';
import { UpdateUserDto } from '../dtos/updateUser.dto';

@ApiTags('User-V1')
@Controller({ version: '1', path: 'user' })
export class UserController {
  constructor(private userService: UserService) { }

  @Get('/me')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get user details' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User details fetched successfully',
  })
  @HttpCode(HttpStatus.OK)
  async getUser(@GetCurrentUser() user: JwtPayload) {
    return this.userService.getUser(user.sub);
  }

  @Post('/send-email-verification')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Send email verification' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Email verification sent successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async sendEmailVerification(@GetCurrentUser() user: JwtPayload) {
    return this.userService.sendEmailVerification(user.sub);
  }

  @Post('/update-password')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update your password' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Password updated successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async updatePassword(
    @Body() data: UpdatePasswordDto,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.userService.updatePassword(user.sub, data.password);
  }

  @Post('/update-user')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update user data' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User updated successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async updateUser(
    @Body() data: UpdateUserDto,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.userService.updateUser(user.sub, data);
  }

  @Post('/enable-two-factor-auth')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Enable two factor authentication' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Two factor authentication enabled successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async enableTwoFactorAuth(@GetCurrentUser() user: JwtPayload) {
    return this.userService.enableTwoFactorAuth(user);
  }

  @Post('/verify-two-factor-auth')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Verify two factor authentication' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Two factor authentication verified successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async verifyTwoFactorAuth(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: { code: string },
  ) {
    return this.userService.verifyTwoFactorAuth(user, data.code);
  }

  @Post('/disable-two-factor-auth')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Disable two factor authentication' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Two factor authentication disabled successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async disableTwoFactorAuth(@GetCurrentUser() user: JwtPayload) {
    return this.userService.disableTwoFactorAuth(user);
  }

  @Post('/send-verification-email')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Send verification email' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Verification email sent successfully',
  })
  @HttpCode(HttpStatus.CREATED)
  async sendVerificationEmail(@GetCurrentUser() user: JwtPayload) {
    return this.userService.sendEmailVerification(user.sub);
  }

  @Post('/verify-email')
  @ApiOperation({ summary: 'Verify email' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Email verified successfully',
  })
  @HttpCode(HttpStatus.OK)
  async verifyEmail(@Body() data: { token: string }) {
    return this.userService.verifyEmail(data.token);
  }

  @Post('/finish-onboarding')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Finish onboarding' })
  @HttpCode(HttpStatus.OK)
  async finishOnboarding(@GetCurrentUser() user: JwtPayload) {
    return this.userService.finishOnboarding(user.sub);
  }

  @Get('/get-chat-configs')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get chat configurations' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chat configurations fetched successfully',
  })
  @HttpCode(HttpStatus.OK)
  async getChatConfigs(@GetCurrentUser() user: JwtPayload) {
    return this.userService.getChatConfigs(user.sub);
  }

  @Post('/complete-initial-onboarding')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Complete initial onboarding' })
  @HttpCode(HttpStatus.OK)
  async completeInitialOnboarding(@GetCurrentUser() user: JwtPayload, @Body() data: any) {
    return this.userService.completeIntialOnboarding(user.sub, data);
  }

  @Get('/check-initial-onboarding')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Check initial onboarding' })
  @HttpCode(HttpStatus.OK)
  async checkInitialOnboarding(@GetCurrentUser() user: JwtPayload) {
    return this.userService.checkInitialOnboardingCompleted(user.sub);
  }
}
