import { Controller, Post, Body, UseGuards, Get } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { SlackService } from './slack.v1.service';
import { SlackCallbackDto } from './dtos/slack.dto';

@ApiTags('Slack-V1')
@Controller({ version: '1', path: 'slack' })
export class SlackController {
  constructor(private slackService: SlackService) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate Slack Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated Slack Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.slackService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle Slack Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed Slack Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: SlackCallbackDto) {
    return this.slackService.handleCallback(body.code, body.state);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect Slack Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected Slack Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.slackService.disconnect(user.sub);
  }
}
