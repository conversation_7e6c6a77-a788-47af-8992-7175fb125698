"use client"

import { useRef, useEffect, useState } from 'react'
import useChatStore from '@/store/chatStore'
import UserMessage from '../Messages/UserMessage'
import AssistantMessage from '../Messages/AssistantMessage'
import AssistantNameMessage from '../Messages/AssistantNameMessage'
import { debounce } from 'lodash'

const ChatMessages = () => {
    const { messages } = useChatStore();
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [shouldAutoScroll, setShouldAutoScroll] = useState(true);

    const isNearBottom = () => {
        const container = containerRef.current;
        if (!container) return true;
        const threshold = 50;
        return container.scrollHeight - container.scrollTop - container.clientHeight < threshold;
    };

    const debouncedScrollToBottom = debounce(() => {
        if (shouldAutoScroll && messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: "smooth", block: "end" });
        }
    }, 50);

    const handleScroll = () => {
        setShouldAutoScroll(isNearBottom());
    };

    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
            return () => container.removeEventListener('scroll', handleScroll);
        }
    }, []);

    useEffect(() => {
        debouncedScrollToBottom();
        return () => debouncedScrollToBottom.cancel();
    }, [messages]);

    return (
        <div
            ref={containerRef}
            className='mx-auto px-2 py-2 h-[calc(100vh-200px)] overflow-y-auto scroll-smooth'
        >
            {messages.map((message, index) => (
                <div
                    key={index}
                    className="opacity-0 animate-fade-in"
                >
                    {message.role === "user" ?
                        <UserMessage key={index} message={message} />
                        :
                        <>
                            {message.role === "assistant_name" ?
                                <AssistantNameMessage key={index} message={message} />
                                :
                                <AssistantMessage key={index} id={index.toString()} message={message} />
                            }
                        </>
                    }
                </div>
            ))}
            <div ref={messagesEndRef} />
        </div>
    )
}

export default ChatMessages
