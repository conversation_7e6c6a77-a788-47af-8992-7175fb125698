"use client"

import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@steps-ai/ui"
import { ScrollArea } from "@steps-ai/ui"
import { Avatar, AvatarFallback } from "@steps-ai/ui"
import { Skeleton } from "@steps-ai/ui"
import { Button } from "@steps-ai/ui"
import { Separator } from "@steps-ai/ui"
import { Star, Search, RefreshCcw, Plus } from "lucide-react"
import Link from 'next/link'
import { chatClient } from '@/lib/chatClient'
import { ChatSessionResponse } from '@/types/chat'
import { Input } from "@steps-ai/ui"
import { ChatLoader } from "@/components/ui/chat-loader"
import { AnimatePresence } from "framer-motion"

const StarredChatsList = () => {
    const [chats, setChats] = useState<ChatSessionResponse[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<Error | null>(null)
    const [searchQuery, setSearchQuery] = useState("")

    const fetchStarredChats = async () => {
        try {
            setIsLoading(true)
            const response = await chatClient.getStarredChats()
            const sortedChats = [...response].sort((a, b) => {
                const dateA = new Date(a.updated_at || '0');
                const dateB = new Date(b.updated_at || '0');
                return dateB.getTime() - dateA.getTime();
            });
            setChats(sortedChats)
            setError(null)
        } catch (err) {
            setError(err instanceof Error ? err : new Error('Failed to fetch starred chats'))
        } finally {
            setIsLoading(false)
        }
    }

    useEffect(() => {
        fetchStarredChats()
        const interval = setInterval(fetchStarredChats, 30000)
        return () => clearInterval(interval)
    }, [])

    const formatDate = (dateString: string) => {
        const date = new Date(dateString)
        const now = new Date()
        const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

        if (diffInDays === 0) {
            return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true })
        } else if (diffInDays === 1) {
            return 'Yesterday'
        } else if (diffInDays < 7) {
            return date.toLocaleDateString('en-US', { weekday: 'long' })
        } else {
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        }
    }

    const formatTime = (dateString: string) => {
        const date = new Date(dateString)
        return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true })
    }

    const truncateMessage = (message: string | undefined, limit: number = 200) => {
        if (!message) return 'No messages yet';
        return message.length > limit ? `${message.slice(0, limit)}...` : message;
    };

    const filteredChats = chats.filter(chat => 
        chat.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.last_message?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const groupChatsByDate = (chats: ChatSessionResponse[]) => {
        const groups: { [key: string]: ChatSessionResponse[] } = {};
        
        chats.forEach(chat => {
            const date = new Date(chat.updated_at || '0');
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            
            let groupKey = '';
            if (date.toDateString() === today.toDateString()) {
                groupKey = 'Today';
            } else if (date.toDateString() === yesterday.toDateString()) {
                groupKey = 'Yesterday';
            } else if (date.getTime() > today.getTime() - 7 * 24 * 60 * 60 * 1000) {
                groupKey = 'This Week';
            } else if (date.getMonth() === today.getMonth()) {
                groupKey = 'This Month';
            } else {
                groupKey = 'Older';
            }
            
            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(chat);
        });
        
        return groups;
    };

    const groupedChats = groupChatsByDate(filteredChats);

    const handleStarChat = async (e: React.MouseEvent, chat: ChatSessionResponse) => {
        e.preventDefault();
        e.stopPropagation();
        try {
            // await chatClient.updateChat({
            //     chat_id: chat.chat_id,
            //     name: chat.name || "Untitled Chat",
            //     starred: !chat.starred
            // });
            await fetchStarredChats(); 
        } catch (error) {
            console.error('Error updating chat star status:', error);
        }
    };

    return (
        <div className="flex justify-center w-full h-full">
            <div className="w-full h-[90vh] max-w-[100vw] md:max-w-none px-2 sm:px-4 md:px  -6 lg:px-8">
                <Card className="h-full border-none shadow-none bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <CardHeader className="space-y-4 p-3 md:p-4">
                        <div className="flex flex-col space-y-2">
                            <div>
                                <CardTitle className="text-lg md:text-xl font-bold">Starred Chats</CardTitle>
                                <CardDescription className="text-sm text-muted-foreground">
                                    View and manage your starred conversations
                                </CardDescription>
                            </div>
                            <div className="flex items-center justify-between gap-2">
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => fetchStarredChats()}
                                    disabled={isLoading}
                                    className="shrink-0 h-9 w-9 md:h-10 md:w-10"
                                >
                                    <RefreshCcw className="h-4 w-4" />
                                </Button>
                                <Link href="/chat" className="shrink-0">
                                    <Button className="h-9 md:h-10">
                                        <Plus className="h-4 w-4 mr-2" />
                                        New Chat
                                    </Button>
                                </Link>
                            </div>
                            <div className="relative w-full">
                                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                <Input
                                    placeholder="Search conversations..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-full pl-9 pr-4 h-9 md:h-10"
                                />
                            </div>
                        </div>
                    </CardHeader>
                    <Separator />
                    <CardContent className="p-0 flex-1 h-[calc(90vh-180px)]">
                        <ScrollArea className="h-full w-full">
                            {error ? (
                                <div className="flex flex-col items-center justify-center p-4 text-center">
                                    <div className="text-red-500 mb-2 text-sm">Error loading starred chats</div>
                                    <p className="text-sm text-muted-foreground mb-2">{error.message}</p>
                                    <Button variant="outline" onClick={() => fetchStarredChats()}>
                                        Try Again
                                    </Button>
                                </div>
                            ) : isLoading ? (
                                <div className="space-y-4 p-4">
                                    {[...Array(5)].map((_, i) => (
                                        <div key={i} className="flex items-start space-x-4 animate-pulse">
                                            <div className="h-10 w-10 sm:h-12 sm:w-12 rounded-full bg-muted"></div>
                                            <div className="flex-1 space-y-2">
                                                <div className="h-4 bg-muted rounded w-1/3"></div>
                                                <div className="h-3 bg-muted rounded w-full"></div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : filteredChats.length > 0 ? (
                                <div className="grid grid-cols-1 gap-0">
                                    {Object.entries(groupedChats).map(([date, chats]) => (
                                        <div key={date}>
                                            <div className="sticky top-0 bg-muted/50 backdrop-blur-sm px-4 py-2 text-sm font-medium text-muted-foreground">
                                                {date}
                                            </div>
                                            {chats.map((chat: ChatSessionResponse) => (
                                                <Link key={chat.chat_id} href={`/chat/${chat.chat_id}`}>
                                                    <div className="group flex items-start space-x-3 md:space-x-4 p-3 md:p-4 hover:bg-muted/50 transition-all">
                                                        {/* <Avatar className="h-9 w-9 md:h-10 md:w-10 shrink-0">
                                                            <AvatarFallback className="text-sm bg-primary/10 transition-colors group-hover:bg-primary/20">
                                                                {(chat.name || 'UC').split(' ').map(n => n[0]).join('').toUpperCase()}
                                                            </AvatarFallback>
                                                        </Avatar> */}
                                                        <div className="flex-1 min-w-0">
                                                            <div className="flex items-center justify-between gap-2 flex-wrap">
                                                                <div className="flex items-center gap-2 min-w-0 flex-1">
                                                                    <h3 className="text-sm md:text-base font-semibold truncate">
                                                                        {chat.name || 'Untitled Chat'}
                                                                    </h3>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        onClick={(e) => handleStarChat(e, chat)}
                                                                        className="shrink-0 h-8 w-8 md:h-9 md:w-9"
                                                                    >
                                                                        <Star
                                                                            className={`h-4 w-4 ${chat.starred ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'}`}
                                                                        />
                                                                    </Button>
                                                                </div>
                                                                <div className="text-xs text-muted-foreground shrink-0">
                                                                    <span>{formatDate(chat.updated_at || '')}</span>
                                                                </div>
                                                            </div>
                                                            <p className="text-xs md:text-sm text-muted-foreground truncate mt-1">
                                                                {truncateMessage(chat.last_message)}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </Link>
                                            ))}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center p-4 text-center">
                                    <Star className="h-12 w-12 text-muted-foreground mb-4" />
                                    <p className="text-sm text-muted-foreground mb-4">No starred chats found</p>
                                    <Link href="/chat">
                                        <Button variant="outline">
                                            Start a New Chat
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </ScrollArea>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

export default function Page() {
    return <StarredChatsList />
}
