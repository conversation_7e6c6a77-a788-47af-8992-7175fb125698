import { Controller, Post, Body, UseGuards, Get } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { GoogleCalendarService } from './calendar.v1.service';
import {
  GoogleCalendarCallbackDto,
  GoogleCalendarRefreshTokenDto,
} from './dtos/gmail.dto';

@ApiTags('Google-Calendar-V1')
@Controller({ version: '1', path: 'google-calendar' })
export class GoogleCalendarController {
  constructor(private googleCalendarService: GoogleCalendarService) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate Google Calendar Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated Google Calendar Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.googleCalendarService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle Google Calendar Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed Google Calendar Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: GoogleCalendarCallbackDto) {
    return this.googleCalendarService.handleCallback(body.code, body.state);
  }

  @Post('refresh-token')
  @ApiOperation({ summary: 'Refresh Google Calendar Auth Token' })
  @ApiResponse({
    status: 200,
    description: 'Successfully refreshed Google Calendar Auth Token',
  })
  async refreshToken(@Body() body: GoogleCalendarRefreshTokenDto) {
    return this.googleCalendarService.refreshToken(
      body.refreshToken,
      body.userId,
    );
  }

  @Get('user-configs')
  @ApiOperation({ summary: 'Get User Configs' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user configs',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async getUserConfigs(@GetCurrentUser() user: JwtPayload) {
    return this.googleCalendarService.getGoogleCalendarUserConfigs(user.sub);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect Google Calendar Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected Google Calendar Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.googleCalendarService.disconnect(user.sub);
  }
}
