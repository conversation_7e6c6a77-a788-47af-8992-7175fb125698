'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>, Plus, MoreHorizontal, Users, Activity } from "lucide-react"
import { useUser } from "@/contexts/UserContext"
import { organizationApi } from "@/lib/api/organization"
import { useEffect, useState } from "react"

interface DashboardData {
    totalUsers: number
    totalConnectors: number
    totalTeams: number
}

export default function DashboardCards() {
    const { user } = useUser()
    const [data, setData] = useState<DashboardData>({
        totalUsers: 0,
        totalConnectors: 0,
        totalTeams: 0
    })
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const fetchDashboardData = async () => {
            if (!user.organizationId) return

            try {
                setLoading(true)
                
                const [users, connectors, teams] = await Promise.all([
                    organizationApi.getOrganizationUsers(),
                    organizationApi.getOrganizationConnectors(),
                    organizationApi.getTeams()
                ])

                setData({
                    totalUsers: users.length,
                    totalConnectors: connectors.totalConnections || 0,
                    totalTeams: teams.length
                })
            } catch (error) {
                console.error('Error fetching dashboard data:', error)
            } finally {
                setLoading(false)            }
        }

        fetchDashboardData()
    }, [user.organizationId])

    return (
        <div className="w-full mx-auto space-y-4">
            {/* Task Cards */}
            <div className="grid grid-cols-2 gap-4">
                {/* Total Users Card */}
                <div className="relative p-6 rounded-3xl bg-gradient-to-br from-purple-200 via-pink-200 to-orange-300 text-gray-800 dark:from-purple-900 dark:via-pink-900 dark:to-orange-900 dark:text-gray-100">
                    <div className="flex justify-between items-start mb-8">
                        <div>
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Total</h3>
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Users</h3>
                        </div>
                        <Users className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                    </div>
                    <div>
                        <div className="text-4xl font-bold text-gray-900 dark:text-white mb-1">
                            {loading ? "..." : data.totalUsers}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Organization Members</div>
                    </div>
                </div>

                {/* Number of Teams Card */}
                <div className="relative p-6 rounded-3xl bg-gradient-to-br from-blue-200 via-cyan-200 to-teal-300 text-gray-800 dark:from-blue-900 dark:via-cyan-900 dark:to-teal-900 dark:text-gray-100">
                    <div className="flex justify-between items-start mb-8">
                        <div>
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Total</h3>
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Teams</h3>
                        </div>
                        <Activity className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                    </div>
                    <div>
                        <div className="text-4xl font-bold text-gray-900 dark:text-white mb-1">
                            {loading ? "..." : data.totalTeams}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Active Teams</div>
                    </div>
                </div>
            </div>

            {/* Connectors Connected Section */}
            <div className="p-4 rounded-2xl bg-gray-100 dark:bg-gray-800">
                <div className="flex justify-between items-center">
                    <div>
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Connectors connected</h3>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                            {loading ? "Loading..." : `${data.totalConnectors} active connections`}
                        </p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 rounded-lg bg-red-500 dark:bg-red-700 flex items-center justify-center">
                            <div className="w-4 h-4 bg-white dark:bg-gray-900 rounded-sm"></div>
                        </div>
                        <div className="w-8 h-8 rounded-lg bg-blue-600 dark:bg-blue-800 flex items-center justify-center">
                            <Trello className="w-4 h-4 text-white dark:text-gray-200" />
                        </div>
                        <div className="w-8 h-8 rounded-lg bg-green-500 dark:bg-green-700 flex items-center justify-center">
                            <Plus className="w-4 h-4 text-white dark:text-gray-200" />
                        </div>
                        <MoreHorizontal className="w-5 h-5 text-gray-400 dark:text-gray-300" />
                    </div>
                </div>
            </div>
        </div>
    )
}
