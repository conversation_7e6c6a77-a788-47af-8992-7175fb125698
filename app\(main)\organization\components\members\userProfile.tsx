'use client';

import React, { useState, useEffect } from 'react';
import { organizationApi, type UserMeResponse } from '@/lib/api/organization';
import { useUser } from '@/contexts/UserContext';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  User,
  Crown,
  Shield,
  Users,
  Clock,
  Mail,
  Calendar,
  Building2,
  Link as LinkIcon,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import Image from 'next/image';

export default function UserProfile() {
  const [userProfile, setUserProfile] = useState<UserMeResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { user: currentUser } = useUser();

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      setIsLoading(true);
      const profile = await organizationApi.getUserMe();
      console.log('User profile loaded:', profile);
      setUserProfile(profile);
    } catch (error) {
      console.error('Failed to load user profile:', error);
      toast.error('Failed to load your profile');
    } finally {
      setIsLoading(false);
    }
  };
  const getRoleConfig = (role: UserMeResponse['role']) => {
    switch (role) {
      case 'ADMIN':
        return {
          color: 'bg-destructive/10 text-destructive border-destructive/20',
          icon: <Crown className="h-3 w-3" />,
          label: 'Admin'
        };
      case 'TEAMADMIN':
        return {
          color: 'bg-primary/10 text-primary border-primary/20',
          icon: <Shield className="h-3 w-3" />,
          label: 'Team Admin'
        };
      case 'USER':
        return {
          color: 'bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/20',
          icon: <User className="h-3 w-3" />,
          label: 'User'
        };
      default:
        return {
          color: 'bg-muted text-muted-foreground border-border',
          icon: <User className="h-3 w-3" />,
          label: 'Unknown'
        };
    }
  };

  const formatLastActive = (lastActive?: string) => {
    if (!lastActive) return 'Never';

    const date = new Date(lastActive);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <main className="container mx-auto px-4 py-8 max-w-7xl">
        <header className="mb-8">
          <Skeleton className="h-8 w-48" />
        </header>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <aside className="lg:col-span-1">
            <Card>
              <CardHeader className="text-center pb-4">
                <Skeleton className="h-20 w-20 rounded-full mx-auto mb-4" />
                <Skeleton className="h-6 w-32 mx-auto mb-2" />
                <Skeleton className="h-4 w-40 mx-auto" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          </aside>
          <section className="lg:col-span-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
    );
  }

  if (!userProfile) {
    return (      <main className="container mx-auto px-4 py-8 max-w-7xl">
        <header className="mb-8">
          <h1 className="text-2xl font-bold text-foreground">Your Profile</h1>
        </header>
        <div className="text-center py-12">
          <p className="text-muted-foreground text-lg">Profile not found</p>
        </div>
      </main>
    );
  }

  const roleConfig = getRoleConfig(userProfile.role);

  return (
    <main className="container mx-auto px-4 py-8 max-w-7xl">
      {/* <header className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Your Profile</h1>
        <p className="text-gray-600 mt-1">Manage your account settings and connected services</p>
      </header> */}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* User Profile - Left Side */}
        <aside className="lg:col-span-1 space-y-6">
          <Card className="overflow-hidden">            <CardHeader className="text-center pb-4 bg-gradient-to-b from-muted/50 to-background">
              <Avatar className="h-20 w-20 mx-auto mb-4 ring-4 ring-background shadow-lg">
                <AvatarImage src={userProfile.profileImageUrl} alt={userProfile.name} />
                <AvatarFallback className="bg-primary/10 text-primary font-semibold text-xl">
                  {userProfile.name?.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <h2 className="font-semibold text-xl text-foreground mb-2">{userProfile.name}</h2>
              <Badge variant="outline" className={`${roleConfig.color} font-medium`}>
                {roleConfig.icon}
                <span className="ml-1">{roleConfig.label}</span>
              </Badge>
            </CardHeader>
            
            <CardContent className="space-y-4">              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="text-foreground flex-1 truncate">{userProfile.email}</span>
                  {userProfile.emailVerified && (
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  )}
                </div>
                
                <div className="flex items-center gap-3 text-sm">
                  <Building2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="text-foreground flex-1 truncate">
                    {userProfile.organization?.name || 'Organization'}
                  </span>
                </div>
              </div>

              <Separator />              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Teams</span>
                  </div>
                  <span className="font-medium text-foreground">{userProfile.teams?.length || 0}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Member since</span>
                  </div>
                  <span className="font-medium text-foreground">{formatDate(userProfile.createdAt)}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Status</span>
                  {userProfile.hasOnboarded ? (
                    <div className="flex items-center gap-1">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-green-600 dark:text-green-400 font-medium">Active</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1">
                      <XCircle className="h-4 w-4 text-amber-500" />
                      <span className="text-amber-600 dark:text-amber-400 font-medium">Pending</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </aside>

        <section className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-xl">
                <LinkIcon className="h-5 w-5 text-gray-600" />
                Connected Data Sources
              </CardTitle>
              <p className="text-gray-600 text-sm">
                Manage your connected services and integrations
              </p>
            </CardHeader>
            
            <CardContent>
              {userProfile.connectors?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {userProfile.connectors?.map((connector) => (
                    <Card key={connector.id} className="border border-gray-200 hover:border-gray-300 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className="w-10 h-10 relative flex-shrink-0">
                            <Image
                              src={connector.logo}
                              alt={connector.name}
                              width={40}
                              height={40}
                              className="rounded-lg object-contain"
                            />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-gray-900 mb-1 truncate">
                              {connector.name}
                            </h3>
                            
                            <div className="flex items-center gap-2 mb-2">
                              {connector.isConnected ? (
                                <>
                                  <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                                  <span className="text-xs text-green-700 font-medium">Connected</span>
                                </>
                              ) : (
                                <>
                                  <XCircle className="h-3 w-3 text-red-500 flex-shrink-0" />
                                  <span className="text-xs text-red-700 font-medium">Disconnected</span>
                                </>
                              )}
                            </div>
                            
                            {connector.connectedAt && (
                              <div className="space-y-1 text-xs text-gray-500">
                                <p>Connected {formatDate(connector.connectedAt)}</p>
                                {connector.lastSyncAt && (
                                  <p>Last sync {formatDate(connector.lastSyncAt)}</p>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <LinkIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-2">No connections yet</h3>
                  <p className="text-gray-500 mb-1">
                    Connect data sources to enhance your AI experience
                  </p>
                  <p className="text-xs text-gray-400">
                    {userProfile.totalConnectors || 0} connectors available
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </section>
      </div>
    </main>
  );
}