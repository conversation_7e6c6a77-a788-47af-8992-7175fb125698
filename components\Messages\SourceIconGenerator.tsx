import React from 'react';
import { Database, FileText, Globe, Mail, File, FileImage, FileVideo, FileAudio } from 'lucide-react';
import { assets } from '@/lib/assets';
import Image from 'next/image';

interface SourceIconGeneratorProps {
  sources: any[];
  maxIcons?: number;
}

const getSourceIcon = (source: any, index: number) => {
  const type = source.type?.toLowerCase() || '';
  let icon;

  switch (type) {
    case 'gmail':
    case 'email':
      icon = (
        <Image 
          src={assets.Icons.Gmail} 
          alt="Gmail" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'web search':
    case 'webpage':
    case 'web':
      icon = (
        <Image 
          src={assets.Icons.Google} 
          alt="Google" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'google drive':
    case 'gdrive':
      if (source.mime_type === 'application/pdf') {
        icon = (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-red-600">
            <path fill="currentColor" d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2.5 8.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM13 17.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm4.5 4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2z"/>
          </svg>
        );
      } else if (source.mime_type === 'application/vnd.google-apps.document') {
        icon = (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-blue-600">
            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
          </svg>
        );
      } else if (source.mime_type === 'application/vnd.google-apps.spreadsheet') {
        icon = (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-green-600">
            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
          </svg>
        );
      } else if (source.mime_type?.startsWith('image/')) {
        icon = <FileImage className="w-4 h-4 text-green-500" />;
      } else if (source.mime_type?.startsWith('video/')) {
        icon = <FileVideo className="w-4 h-4 text-red-500" />;
      } else if (source.mime_type?.startsWith('audio/')) {
        icon = <FileAudio className="w-4 h-4 text-purple-500" />;
      } else if (source.mime_type?.includes('text') || source.mime_type?.includes('document')) {
        icon = <FileText className="w-4 h-4 text-blue-500" />;
      } else {
        icon = <File className="w-4 h-4 text-gray-500" />;
      }
      break;
    case 'slack':
      icon = (
        <Image 
          src={assets.Icons.Slack} 
          alt="Slack" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'notion':
      icon = (
        <Image 
          src={assets.Icons.Notion} 
          alt="Notion" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'confluence':
      icon = (
        <Image 
          src={assets.Icons.Confluence} 
          alt="Confluence" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'outlook':
      icon = (
        <Image 
          src={assets.Icons.Outlook} 
          alt="Outlook" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'github':
      icon = (
        <Image 
          src={assets.Icons.Github} 
          alt="Github" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'google calendar':
    case 'calendar':
      icon = (
        <Image 
          src={assets.Icons["Google Calendar"]} 
          alt="Google Calendar" 
          width={16} 
          height={16}
          className="w-full h-full object-contain"
        />
      );
      break;
    case 'knowledge base':
    case 'kb':
      icon = <Database className="w-4 h-4 text-purple-500" />;
      break;
    default:
      if (source.mime_type === 'application/pdf' || source.name?.toLowerCase().endsWith('.pdf')) {
        icon = (
          <svg viewBox="0 0 24 24" className="w-4 h-4 text-red-600">
            <path fill="currentColor" d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2.5 8.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM13 17.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm4.5 4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2z"/>
          </svg>
        );
      } else {
        icon = <FileText className="w-4 h-4 text-gray-500" />;
      }
  }

  return (
    <div
      key={index}
      className="w-4 h-4 flex items-center justify-center"
      style={{ transform: `translateX(-${index * 8}px)` }}
    >
      {icon}
    </div>
  );
};

export function SourceIconGenerator({ sources, maxIcons = 3 }: SourceIconGeneratorProps) {
  if (!sources || sources.length === 0) {
    return null;
  }

  const sourcesToShow = sources.slice(0, maxIcons);
  const icons: React.ReactElement[] = [];

  sourcesToShow.forEach((source, index) => {
    if (source) {
      icons.push(getSourceIcon(source, index));
    }
  });

  return (
    <div className="flex -space-x-2">
      {icons}
    </div>
  );
}

export { getSourceIcon };
