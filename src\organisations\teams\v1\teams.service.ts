import { PrismaService } from '@/prisma/prisma.service';
import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { Role } from '@prisma/client';
import {
  AddMemberToTeamDto,
  CreateTeamDto,
  UpdateTeamDto,
} from '../dtos/teams.dto';
import { AuthService } from 'src/auth/v1/auth.v1.service';
import { SwitchTeamContextDto, GetUserTeamsDto } from '../dtos/team-context.dto';
import { BulkAddMembersDto, BulkAddMembersResultDto } from '../dtos/bulk-add-members.dto';
import { EmailService } from 'src/email/email.service';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class OrgTeamsService {

  constructor(
    private prisma: PrismaService,
    private authService: AuthService,
    private emailService: EmailService,
    private configService: ConfigService,
  ) {}

  async createTeam(data: CreateTeamDto) {
    const teamData: any = {
      name: data.name,
      description: data.description,
      organizationId: data.organizationId,
    };

    if (data.members && data.members.length > 0) {
      teamData.members = {
        connect: data.members.map((member) => ({ id: member })),
      };
    }

    const team = await this.prisma.team.create({
      data: teamData,
    });

    return {
      success: true,
      message: 'Team created successfully',
      team,
    };
  }

  async getTeams(orgId: string) {
    const teams = await this.prisma.team.findMany({
      where: {
        organizationId: orgId,
      },
    });
    if (!teams) {
      throw new NotFoundException('Teams not found');
    }
    return {
      success: true,
      message: 'Teams fetched successfully',
      teams,
    };
  }

  async getTeam(teamId: string) {
    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId,
      },
      include: {
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            profileImageUrl: true,
            emailVerified: true,
            hasOnboarded: true,
            onboardingCompleted: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
      },
    });

    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const teamWithDetails = {
      ...team,
      memberCount: team.members.length,
      memberIds: team.members.map(member => member.id),
    };

    return {
      success: true,
      message: 'Team fetched successfully',
      team: teamWithDetails,
    };
  }

  async updateTeam(teamId: string, data: UpdateTeamDto) {
    const team = await this.prisma.team.findUnique({
      where: { id: teamId },
    });
    if (!team) {
      throw new NotFoundException('Team not found');
    }
    const updatedTeam = await this.prisma.team.update({
      where: { id: teamId },
      data: {
        name: data.name,
        description: data.description,
      },
    });
    return {
      success: true,
      message: 'Team updated successfully',
      team: updatedTeam,
    };
  }

  async deleteTeam(teamId: string) {
    const team = await this.prisma.team.findUnique({
      where: { id: teamId },
    });
    if (!team) {
      throw new NotFoundException('Team not found');
    }
    await this.prisma.team.delete({
      where: { id: teamId },
    });
    return {
      success: true,
      message: 'Team deleted successfully',
    };
  }

  async addMemberToTeam(teamId: string, data: AddMemberToTeamDto) {
    const team = await this.prisma.team.findUnique({
      where: { id: teamId },
      include: {
        organization: true,
      },
    });

    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const member = await this.prisma.user.findUnique({
      where: { id: data.memberId },
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    if (member.organizationId !== team.organizationId) {
      throw new ForbiddenException('Member must be part of the organization before being added to a team');
    }

    await this.prisma.team.update({
      where: { id: teamId },
      data: {
        members: { connect: { id: data.memberId } },
      },
    });

    if (member.name && member.email) {
      await this.emailService.sendTeamAdditionEmail(
        member.email,
        member.name,
        team.name,
        team.organization.name,
      );
    }

    return {
      success: true,
      message: 'Member added to team successfully',
    };
  }

  async removeMemberFromTeam(teamId: string, memberId: string) {
    const team = await this.prisma.team.findUnique({
      where: { id: teamId },
    });
    if (!team) {
      throw new NotFoundException('Team not found');
    }
    const member = await this.prisma.user.findUnique({
      where: { id: memberId },
    });
    if (!member) {
      throw new NotFoundException('Member not found');
    }
    await this.prisma.team.update({
      where: { id: teamId },
      data: {
        members: { disconnect: { id: memberId } },
      },
    });
    return {
      success: true,
      message: 'Member removed from team successfully',
    };
  }

  async getUserTeams(userId: string, query: GetUserTeamsDto) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const teams = await this.prisma.team.findMany({
      where: {
        members: {
          some: {
            id: userId,
          },
        },
        ...(query.organizationId ? { organizationId: query.organizationId } : {}),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
      },
    });

    const organization = user.organizationId
      ? await this.prisma.organization.findUnique({
          where: { id: user.organizationId },
          select: {
            id: true,
            name: true,
            logo: true,
          },
        })
      : null;

    return {
      success: true,
      message: 'User teams fetched successfully',
      teams,
      organization,
    };
  }

  async switchTeamContext(userId: string, data: SwitchTeamContextDto) {
    const team = await this.prisma.team.findUnique({
      where: { id: data.teamId },
      include: {
        members: {
          where: { id: userId },
          select: { id: true },
        },
        organization: true,
      },
    });

    if (!team) {
      throw new NotFoundException('Team not found');
    }

    if (team.members.length === 0) {
      throw new ForbiddenException('User is not a member of this team');
    }

    const organizationId = data.organizationId || team.organizationId;

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        profileImageUrl: true,
        hasOnboarded: true,
        onboardingCompleted: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const tokens = await this.authService.generateTokens(
      {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.profileImageUrl,
        hasOnboarded: user.hasOnboarded,
        initialOnboardingCompleted: user.onboardingCompleted,
      },
      organizationId,
    );

    return {
      success: true,
      message: 'Team context switched successfully',
      tokens,
      team,
      organization: team.organization,
    };
  }

  async bulkAddMembersToTeam(data: BulkAddMembersDto): Promise<{
    success: boolean;
    message: string;
    results: BulkAddMembersResultDto[];
  }> {
    const team = await this.prisma.team.findUnique({
      where: { id: data.teamId },
      include: {
        organization: true,
      },
    });

    if (!team) {
      throw new NotFoundException('Team not found');
    }

    const results: BulkAddMembersResultDto[] = [];

    for (const email of data.emails) {
      try {
        const existingUser = await this.prisma.user.findUnique({
          where: { email },
        });

        if (existingUser) {
          if (existingUser.organizationId !== team.organizationId) {
            results.push({
              email,
              status: 'error',
              message: 'User must be part of the organization before being added to a team',
            });
            continue;
          }

          await this.prisma.team.update({
            where: { id: data.teamId },
            data: {
              members: { connect: { id: existingUser.id } },
            },
          });

          await this.emailService.sendTeamAdditionEmail(
            email,
            existingUser.name || 'User',
            team.name,
            team.organization.name,
          );

          results.push({
            email,
            status: 'success',
            message: 'User added to team successfully',
          });
        } else {
          results.push({
            email,
            status: 'error',
            message: 'User must be invited to the organization first before being added to a team',
          });
        }
      } catch (error) {
        console.error(`Error processing email ${email}:`, error);
        results.push({
          email,
          status: 'error',
          message: `Error: ${error.message}`,
        });
      }
    }

    return {
      success: true,
      message: 'Bulk add members operation completed',
      results,
    };
  }

  async assignTeamLeader(teamId: string, data: { userId: string }) {
    const team = await this.prisma.team.findUnique({
      where: { id: teamId },
      include: {
        members: {
          where: { id: data.userId },
          select: { id: true },
        },
        organization: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
      },
    });

    if (!team) {
      throw new NotFoundException('Team not found');
    }

    if (team.members.length === 0) {
      throw new BadRequestException('User is not a member of this team');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: data.userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        profileImageUrl: true,
        organizationId: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.organizationId !== team.organizationId) {
      throw new BadRequestException('User must be part of the same organization as the team');
    }

    if (user.role === 'TEAMADMIN' || user.role === 'ADMIN' || user.role === 'SUPERADMIN') {
      throw new BadRequestException(`User is already a ${user.role} and cannot be promoted to TeamAdmin`);
    }

    const updatedUser = await this.prisma.user.update({
      where: { id: data.userId },
      data: { role: Role.TEAMADMIN },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        profileImageUrl: true,
      },
    });

    if (updatedUser.name && updatedUser.email) {
      await this.emailService.sendTeamLeaderAssignmentEmail(
        updatedUser.email,
        updatedUser.name,
        team.name,
        team.organization.name,
      );
    }

    return {
      success: true,
      message: 'Team leader assigned successfully',
      teamLeader: updatedUser,
      team: {
        id: team.id,
        name: team.name,
        description: team.description,
        organizationId: team.organizationId,
      },
    };
  }

}
