"use client";

import React from 'react';
import Image from 'next/image';
import { Message } from '@/types/chat';
import { useUser } from '@/contexts/UserContext';
import { MarkdownMessage } from '../Chat/MarkdownMessage';
import { Separator } from '@/components/ui/separator';
import { ThinkingVisualizer } from './ThinkingMessage';
import { Copy, Database, ThumbsDown, ThumbsUp, FileText, Globe, Mail, Chrome } from 'lucide-react';
import { Button } from '@steps-ai/ui';
import { useSourcesDrawer } from '@/store/sourcesDrawerStore';
import { FaGoogleDrive, FaFilePdf } from "react-icons/fa";
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import useChatStore from '@/store/chatStore';


interface AssistantMessageProps {
    id: string;
    message: Message;
}

export default function AssistantMessage({ id, message }: AssistantMessageProps) {
    const { setIsOpen, setCurrentMessage, setRawSources, messageSources } = useSourcesDrawer();
    const { messages } = useChatStore();
    const [sourceIcons, setSourceIcons] = useState<React.ReactElement[]>([]);
    const [copied, setCopied] = useState(false);
    const [feedback, setFeedback] = useState<'up' | 'down' | null>(null);
    const [isVisible, setIsVisible] = useState(false);

    const currentMessage = messages.find(m => m.timestamp === message.timestamp);
    const rawMessageSources = currentMessage?.sources || message.sources || [];
    const messageId = message.timestamp;

    const sourceCount = messageSources[messageId]?.count || 0;
    const structuredSources = messageSources[messageId]?.sources || [];

    useEffect(() => {
        console.log(`[AssistantMessage] Message ${messageId}:`);
        console.log(`[AssistantMessage] - Raw sources length: ${rawMessageSources.length}`);
        console.log(`[AssistantMessage] - Store source count: ${sourceCount}`);
        console.log(`[AssistantMessage] - Store has entry: ${!!messageSources[messageId]}`);
        console.log(`[AssistantMessage] - Structured sources:`, structuredSources);
        if (structuredSources.length > 0) {
            structuredSources.forEach((source, i) => {
                console.log(`[AssistantMessage] - Source ${i + 1}: type="${source.type}", name="${source.name || source.title}", mime_type="${source.mime_type || 'N/A'}"`);
            });
        }
    }, [messageId, rawMessageSources.length, sourceCount, structuredSources, messageSources]);

    useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), 50);
        return () => clearTimeout(timer);
    }, []);

    const getSourceIcon = (source: any, index: number) => {
        const type = source.type?.toLowerCase() || '';
        let icon;

        switch (type) {
            case 'google drive':
            case 'gdrive':
                icon = <FaGoogleDrive className="text-[#4285F4] drop-shadow-sm" />;
                break;
            case 'web search':
            case 'webpage':
            case 'web':
                icon = <Globe className="text-[#1a73e8] drop-shadow-sm" />;
                break;
            case 'gmail':
            case 'email':
                icon = <Mail className="text-[#EA4335] drop-shadow-sm" />;
                break;
            case 'pdf':
            case 'application/pdf':
                icon = <FaFilePdf className="text-[#DC2626] drop-shadow-sm" />;
                break;
            case 'knowledge base':
            case 'kb':
                icon = <Database className="text-[#7C3AED] drop-shadow-sm" />;
                break;
            default:
                // Check mime_type for PDFs
                if (source.mime_type === 'application/pdf' || source.name?.toLowerCase().endsWith('.pdf')) {
                    icon = <FaFilePdf className="text-[#DC2626] drop-shadow-sm" />;
                } else {
                    icon = <FileText className="text-[#6B7280] drop-shadow-sm" />;
                }
        }

        return (
            <div
                key={index}
                className="w-7 h-7 rounded-full bg-white/90 dark:bg-gray-800/90 border-2 border-white dark:border-gray-700 flex items-center justify-center shadow-sm hover:scale-105 transition-transform duration-200"
                style={{ transform: `translateX(-${index * 6}px)` }}
            >
                <div className="w-4 h-4">
                    {icon}
                </div>
            </div>
        );
    };

    useEffect(() => {
        if (sourceCount === 0 || structuredSources.length === 0) {
            setSourceIcons([]);
            return;
        }

        const icons: React.ReactElement[] = [];
        const sourcesToShow = structuredSources.slice(0, 3);

        sourcesToShow.forEach((source, index) => {
            if (source) {
                icons.push(getSourceIcon(source, index));
            }
        });

        setSourceIcons(icons);
    }, [sourceCount, structuredSources]);

    const handleSourcesClick = () => {
        console.log(`[AssistantMessage] Sources button clicked for message: ${message.timestamp}`);
        console.log(`[AssistantMessage] Message has ${rawMessageSources.length} raw sources`);
        console.log(`[AssistantMessage] Source count from store: ${sourceCount}`);
        console.log(`[AssistantMessage] Setting raw sources:`, rawMessageSources);

        setCurrentMessage(message);
        setRawSources(rawMessageSources);
        setIsOpen(true);
    };

    const handleCopy = async () => {
        await navigator.clipboard.writeText(message.message);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const handleFeedback = (type: 'up' | 'down') => {
        setFeedback(type);
    };

    const hasSources = sourceCount > 0;

    useEffect(() => {
        console.log(`[AssistantMessage] Sources button visibility for message ${message.timestamp}: ${hasSources}`);
        console.log(`[AssistantMessage] Source count: ${sourceCount}, Raw sources: ${rawMessageSources.length}`);
    }, [hasSources, sourceCount, rawMessageSources.length, message.timestamp]);

    return (
        <div className='flex flex-col gap-2 mb-5 mx-auto max-w-4xl'>
            <div className={cn(
                'rounded-lg inline-block transition-opacity duration-300',
                isVisible ? 'opacity-100' : 'opacity-0'
            )}>                <div className="flex items-center gap-2 w-fit justify-start">
                    <Image src='/ai-logo.svg' alt='/ai-logo.png' height={20} width={20} className="rounded-full" />
                    <span className='text-md font-bold bg-gradient-to-r from-[#DE4D4D] via-[#9C5876] to-[#3B6BB5] text-transparent bg-clip-text'>
                        AIDE
                    </span>
                </div>
                {message.thoughts && message.thoughts.length > 0 && (
                    <ThinkingVisualizer events={message.thoughts || []} isComplete={!message.loading} />
                )}<div className={cn(
                    "relative max-w-fit flex-grow mx-0 border-none px-4 pt-2 rounded-lg",
                    message.loading ? 'animate-pulse' : 'animate-fade-in'
                )}>
                    <MarkdownMessage content={message.formattedMessage || message.message} id={id} />
                </div>
                {hasSources && (
                    <div className={cn(
                        "flex items-center justify-between mt-2 mb-1 transition-opacity duration-300",
                        isVisible ? 'opacity-100' : 'opacity-0'
                    )}>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleSourcesClick}
                            className="flex items-center gap-2 text-xs rounded-full px-4 py-2 hover:bg-primary/10 bg-primary/5 border border-primary/20 hover:border-primary/30 transition-all duration-200 backdrop-blur-sm"
                        >
                            <div className="flex -space-x-1.5">
                                {sourceIcons}
                            </div>
                            <span className="font-medium text-primary/90">Sources</span>
                            <span className="ml-1 bg-primary/15 text-primary/80 w-5 h-5 rounded-full flex items-center justify-center text-[10px] font-semibold border border-primary/25">
                                {sourceCount}
                            </span>
                        </Button>

                        <div className="flex items-end justify-end gap-1 ml-auto">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCopy}
                                className="text-xs rounded-full px-2 h-7 hover:bg-secondary/80"
                            >
                                <Copy className="h-3.5 w-3.5 mr-1" />
                                {copied ? 'Copied!' : 'Copy'}
                            </Button>

                            <div className="flex items-center gap-1 ml-2">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleFeedback('up')}
                                    className={cn(
                                        "rounded-full w-7 h-7 hover:bg-secondary/80",
                                        feedback === 'up' && "bg-secondary text-primary"
                                    )}
                                >
                                    <ThumbsUp className="h-3.5 w-3.5" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleFeedback('down')}
                                    className={cn(
                                        "rounded-full w-7 h-7 hover:bg-secondary/80",
                                        feedback === 'down' && "bg-secondary text-primary"
                                    )}
                                >
                                    <ThumbsDown className="h-3.5 w-3.5" />
                                </Button>
                            </div>                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}