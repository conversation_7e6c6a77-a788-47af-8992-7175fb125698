"use client";

import React from 'react';
import { Message } from '@/types/chat';
import { useUser } from '@/contexts/UserContext';
import { MarkdownMessage } from '../Chat/MarkdownMessage';
import { Separator } from '@/components/ui/separator';
import { ThinkingVisualizer } from './ThinkingMessage';
import { Copy, Database, ThumbsDown, ThumbsUp, FileText, Globe, Mail, File, FileImage, FileVideo, FileAudio } from 'lucide-react';
import { Button } from '@steps-ai/ui';
import { useSourcesDrawer } from '@/store/sourcesDrawerStore';
import { assets } from '@/lib/assets';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import useChatStore from '@/store/chatStore';


interface AssistantMessageProps {
    id: string;
    message: Message;
}

export default function AssistantMessage({ id, message }: AssistantMessageProps) {
    const { setIsOpen, setCurrentMessage, setRawSources, messageSources } = useSourcesDrawer();
    const { messages } = useChatStore();
    const [sourceIcons, setSourceIcons] = useState<React.ReactElement[]>([]);
    const [copied, setCopied] = useState(false);
    const [feedback, setFeedback] = useState<'up' | 'down' | null>(null);
    const [isVisible, setIsVisible] = useState(false);

    const currentMessage = messages.find(m => m.timestamp === message.timestamp);
    const rawMessageSources = currentMessage?.sources || message.sources || [];
    const messageId = message.timestamp;

    const sourceCount = messageSources[messageId]?.count || 0;
    const structuredSources = messageSources[messageId]?.sources || [];

    useEffect(() => {
        console.log(`[AssistantMessage] Message ${messageId}:`);
        console.log(`[AssistantMessage] - Raw sources length: ${rawMessageSources.length}`);
        console.log(`[AssistantMessage] - Store source count: ${sourceCount}`);
        console.log(`[AssistantMessage] - Store has entry: ${!!messageSources[messageId]}`);
        console.log(`[AssistantMessage] - Structured sources:`, structuredSources);
        if (structuredSources.length > 0) {
            structuredSources.forEach((source, i) => {
                console.log(`[AssistantMessage] - Source ${i + 1}: type="${source.type}", name="${source.name || source.title}", mime_type="${source.mime_type || 'N/A'}"`);
            });
        }
    }, [messageId, rawMessageSources.length, sourceCount, structuredSources, messageSources]);

    useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), 50);
        return () => clearTimeout(timer);
    }, []);

    const getSourceIcon = (source: any, index: number) => {
        const type = source.type?.toLowerCase() || '';
        let icon;

        switch (type) {
            case 'gmail':
            case 'email':
                icon = (
                    <Image
                        src={assets.Icons.Gmail}
                        alt="Gmail"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'web search':
            case 'webpage':
            case 'web':
                icon = (
                    <Image
                        src={assets.Icons.Google}
                        alt="Google"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'google drive':
            case 'gdrive':
                if (source.mime_type === 'application/pdf') {
                    icon = (
                        <svg viewBox="0 0 24 24" className="w-4 h-4 text-red-600">
                            <path fill="currentColor" d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2.5 8.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM13 17.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm4.5 4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2z"/>
                        </svg>
                    );
                } else if (source.mime_type === 'application/vnd.google-apps.document') {
                    icon = (
                        <svg viewBox="0 0 24 24" className="w-4 h-4 text-blue-600">
                            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                    );
                } else if (source.mime_type === 'application/vnd.google-apps.spreadsheet') {
                    icon = (
                        <svg viewBox="0 0 24 24" className="w-4 h-4 text-green-600">
                            <path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    );
                } else if (source.mime_type?.startsWith('image/')) {
                    icon = <FileImage className="w-4 h-4 text-green-500" />;
                } else if (source.mime_type?.startsWith('video/')) {
                    icon = <FileVideo className="w-4 h-4 text-red-500" />;
                } else if (source.mime_type?.startsWith('audio/')) {
                    icon = <FileAudio className="w-4 h-4 text-purple-500" />;
                } else if (source.mime_type?.includes('text') || source.mime_type?.includes('document')) {
                    icon = <FileText className="w-4 h-4 text-blue-500" />;
                } else {
                    icon = <File className="w-4 h-4 text-gray-500" />;
                }
                break;
            case 'slack':
                icon = (
                    <Image
                        src={assets.Icons.Slack}
                        alt="Slack"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'notion':
                icon = (
                    <Image
                        src={assets.Icons.Notion}
                        alt="Notion"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'confluence':
                icon = (
                    <Image
                        src={assets.Icons.Confluence}
                        alt="Confluence"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'outlook':
                icon = (
                    <Image
                        src={assets.Icons.Outlook}
                        alt="Outlook"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'github':
                icon = (
                    <Image
                        src={assets.Icons.Github}
                        alt="Github"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'google calendar':
            case 'calendar':
                icon = (
                    <Image
                        src={assets.Icons["Google Calendar"]}
                        alt="Google Calendar"
                        width={16}
                        height={16}
                        className="w-full h-full object-contain"
                    />
                );
                break;
            case 'knowledge base':
            case 'kb':
                icon = <Database className="w-4 h-4 text-purple-500" />;
                break;
            default:
                if (source.mime_type === 'application/pdf' || source.name?.toLowerCase().endsWith('.pdf')) {
                    icon = (
                        <svg viewBox="0 0 24 24" className="w-4 h-4 text-red-600">
                            <path fill="currentColor" d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2.5 8.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM13 17.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2zm4.5 4.5h-2v-2h2v2zm0-4.5h-2v-2h2v2z"/>
                        </svg>
                    );
                } else {
                    icon = <FileText className="w-4 h-4 text-gray-500" />;
                }
        }

        return (
            <div
                key={index}
                className="w-4 h-4 flex items-center justify-center"
                style={{ transform: `translateX(-${index * 8}px)` }}
            >
                {icon}
            </div>
        );
    };

    useEffect(() => {
        console.log(`[AssistantMessage] Generating icons for message ${messageId}:`);
        console.log(`[AssistantMessage] - sourceCount: ${sourceCount}`);
        console.log(`[AssistantMessage] - structuredSources.length: ${structuredSources.length}`);

        if (sourceCount === 0 || structuredSources.length === 0) {
            console.log(`[AssistantMessage] - No sources, clearing icons`);
            setSourceIcons([]);
            return;
        }

        const icons: React.ReactElement[] = [];
        const sourcesToShow = structuredSources.slice(0, 3);
        console.log(`[AssistantMessage] - Will show ${sourcesToShow.length} icons`);

        sourcesToShow.forEach((source, index) => {
            if (source) {
                console.log(`[AssistantMessage] - Generating icon ${index + 1} for source:`, source);
                icons.push(getSourceIcon(source, index));
            }
        });

        console.log(`[AssistantMessage] - Generated ${icons.length} icons`);
        setSourceIcons(icons);
    }, [sourceCount, structuredSources, messageId]);

    const handleSourcesClick = () => {
        console.log(`[AssistantMessage] Sources button clicked for message: ${message.timestamp}`);
        console.log(`[AssistantMessage] Message has ${rawMessageSources.length} raw sources`);
        console.log(`[AssistantMessage] Source count from store: ${sourceCount}`);
        console.log(`[AssistantMessage] Setting raw sources:`, rawMessageSources);

        setCurrentMessage(message);
        setRawSources(rawMessageSources);
        setIsOpen(true);
    };

    const handleCopy = async () => {
        await navigator.clipboard.writeText(message.message);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const handleFeedback = (type: 'up' | 'down') => {
        setFeedback(type);
    };

    const hasSources = sourceCount > 0;

    useEffect(() => {
        console.log(`[AssistantMessage] Sources button visibility for message ${message.timestamp}: ${hasSources}`);
        console.log(`[AssistantMessage] Source count: ${sourceCount}, Raw sources: ${rawMessageSources.length}`);
    }, [hasSources, sourceCount, rawMessageSources.length, message.timestamp]);

    return (
        <div className='flex flex-col gap-2 mb-5 mx-auto max-w-4xl'>
            <div className={cn(
                'rounded-lg inline-block transition-opacity duration-300',
                isVisible ? 'opacity-100' : 'opacity-0'
            )}>                <div className="flex items-center gap-2 w-fit justify-start">
                    <Image src='/ai-logo.svg' alt='/ai-logo.png' height={20} width={20} className="rounded-full" />
                    <span className='text-md font-bold bg-gradient-to-r from-[#DE4D4D] via-[#9C5876] to-[#3B6BB5] text-transparent bg-clip-text'>
                        AIDE
                    </span>
                </div>
                {message.thoughts && message.thoughts.length > 0 && (
                    <ThinkingVisualizer events={message.thoughts || []} isComplete={!message.loading} />
                )}<div className={cn(
                    "relative max-w-fit flex-grow mx-0 border-none px-4 pt-2 rounded-lg",
                    message.loading ? 'animate-pulse' : 'animate-fade-in'
                )}>
                    <MarkdownMessage content={message.formattedMessage || message.message} id={id} />
                </div>
                {hasSources && (
                    <div className={cn(
                        "flex items-center justify-between mt-2 mb-1 transition-opacity duration-300",
                        isVisible ? 'opacity-100' : 'opacity-0'
                    )}>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleSourcesClick}
                            className="flex items-center gap-2 text-xs rounded-lg px-3 py-1.5 hover:bg-muted/50 transition-colors duration-200"
                        >
                            <div className="flex -space-x-2">
                                {sourceIcons}
                            </div>
                            <span className="font-medium text-muted-foreground">Sources</span>
                            <span className="ml-1 text-muted-foreground text-[10px] font-medium">
                                {sourceCount}
                            </span>
                        </Button>

                        <div className="flex items-end justify-end gap-1 ml-auto">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCopy}
                                className="text-xs rounded-full px-2 h-7 hover:bg-secondary/80"
                            >
                                <Copy className="h-3.5 w-3.5 mr-1" />
                                {copied ? 'Copied!' : 'Copy'}
                            </Button>

                            <div className="flex items-center gap-1 ml-2">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleFeedback('up')}
                                    className={cn(
                                        "rounded-full w-7 h-7 hover:bg-secondary/80",
                                        feedback === 'up' && "bg-secondary text-primary"
                                    )}
                                >
                                    <ThumbsUp className="h-3.5 w-3.5" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleFeedback('down')}
                                    className={cn(
                                        "rounded-full w-7 h-7 hover:bg-secondary/80",
                                        feedback === 'down' && "bg-secondary text-primary"
                                    )}
                                >
                                    <ThumbsDown className="h-3.5 w-3.5" />
                                </Button>
                            </div>                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}