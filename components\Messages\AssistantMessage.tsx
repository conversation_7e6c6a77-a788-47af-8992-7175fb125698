"use client";

import React from 'react';
import Image from 'next/image';
import { Message } from '@/types/chat';
import { useUser } from '@/contexts/UserContext';
import { MarkdownMessage } from '../Chat/MarkdownMessage';
import { Separator } from '@/components/ui/separator';
import { ThinkingVisualizer } from './ThinkingMessage';
import { Copy, Database, ThumbsDown, ThumbsUp } from 'lucide-react';
import { Button } from '@steps-ai/ui';
import { useSourcesDrawer } from '@/store/sourcesDrawerStore';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import useChatStore from '@/store/chatStore';


interface AssistantMessageProps {
    id: string;
    message: Message;
}

export default function AssistantMessage({ id, message }: AssistantMessageProps) {
    const { setIsOpen, setCurrentMessage, setRawSources, messageSources } = useSourcesDrawer();
    const { messages } = useChatStore();
    const [sourceIcons, setSourceIcons] = useState<React.ReactElement[]>([]);
    const [copied, setCopied] = useState(false);
    const [feedback, setFeedback] = useState<'up' | 'down' | null>(null);
    const [isVisible, setIsVisible] = useState(false);

    const currentMessage = messages.find(m => m.timestamp === message.timestamp);
    const rawMessageSources = currentMessage?.sources || message.sources || [];
    const messageId = message.timestamp;

    const sourceCount = messageSources[messageId]?.count || 0;

    useEffect(() => {
        console.log(`[AssistantMessage] Message ${messageId}:`);
        console.log(`[AssistantMessage] - Raw sources length: ${rawMessageSources.length}`);
        console.log(`[AssistantMessage] - Store source count: ${sourceCount}`);
        console.log(`[AssistantMessage] - Store has entry: ${!!messageSources[messageId]}`);
        console.log(`[AssistantMessage] - Store entry:`, messageSources[messageId]);
    }, [messageId, rawMessageSources.length, sourceCount, messageSources]);

    useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), 50);
        return () => clearTimeout(timer);
    }, []);

    useEffect(() => {
        if (sourceCount === 0) {
            setSourceIcons([]);
            return;
        }

        const icons: React.ReactElement[] = [];
        const iconsToShow = Math.min(sourceCount, 3);

        for (let i = 0; i < iconsToShow; i++) {
            icons.push(
                <div
                    key={i}
                    className="w-6 h-6 rounded-full bg-background border border-border flex items-center justify-center"
                    style={{ transform: `translateX(-${i * 4}px)` }}
                >
                    <div className="w-4 h-4">
                        <Database className="text-primary" />
                    </div>
                </div>
            );
        }

        setSourceIcons(icons);
    }, [sourceCount]);

    const handleSourcesClick = () => {
        console.log(`[AssistantMessage] Sources button clicked for message: ${message.timestamp}`);
        console.log(`[AssistantMessage] Message has ${rawMessageSources.length} raw sources`);
        console.log(`[AssistantMessage] Source count from store: ${sourceCount}`);
        console.log(`[AssistantMessage] Setting raw sources:`, rawMessageSources);

        setCurrentMessage(message);
        setRawSources(rawMessageSources);
        setIsOpen(true);
    };

    const handleCopy = async () => {
        await navigator.clipboard.writeText(message.message);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const handleFeedback = (type: 'up' | 'down') => {
        setFeedback(type);
    };

    const hasSources = sourceCount > 0;

    useEffect(() => {
        console.log(`[AssistantMessage] Sources button visibility for message ${message.timestamp}: ${hasSources}`);
        console.log(`[AssistantMessage] Source count: ${sourceCount}, Raw sources: ${rawMessageSources.length}`);
    }, [hasSources, sourceCount, rawMessageSources.length, message.timestamp]);

    return (
        <div className='flex flex-col gap-2 mb-5 mx-auto max-w-4xl'>
            <div className={cn(
                'rounded-lg inline-block transition-opacity duration-300',
                isVisible ? 'opacity-100' : 'opacity-0'
            )}>                <div className="flex items-center gap-2 w-fit justify-start">
                    <Image src='/ai-logo.svg' alt='/ai-logo.png' height={20} width={20} className="rounded-full" />
                    <span className='text-md font-bold bg-gradient-to-r from-[#DE4D4D] via-[#9C5876] to-[#3B6BB5] text-transparent bg-clip-text'>
                        AIDE
                    </span>
                </div>
                {message.thoughts && message.thoughts.length > 0 && (
                    <ThinkingVisualizer events={message.thoughts || []} isComplete={!message.loading} />
                )}<div className={cn(
                    "relative max-w-fit flex-grow mx-0 border-none px-4 pt-2 rounded-lg",
                    message.loading ? 'animate-pulse' : 'animate-fade-in'
                )}>
                    <MarkdownMessage content={message.formattedMessage || message.message} id={id} />
                </div>
                {hasSources && (
                    <div className={cn(
                        "flex items-center justify-between mt-2 mb-1 transition-opacity duration-300",
                        isVisible ? 'opacity-100' : 'opacity-0'
                    )}>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleSourcesClick}
                            className="flex items-center gap-1 text-xs rounded-full px-3 py-1 hover:bg-secondary/80 bg-secondary"
                        >
                            <div className="flex -space-x-1">
                                {sourceIcons}
                            </div>
                            <span className="font-medium ml-1">Sources</span>
                            <span className="ml-1.5 bg-white/20 w-5 h-5 rounded-full flex items-center justify-center text-[10px]">
                                {sourceCount}
                            </span>
                        </Button>

                        <div className="flex items-end justify-end gap-1 ml-auto">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCopy}
                                className="text-xs rounded-full px-2 h-7 hover:bg-secondary/80"
                            >
                                <Copy className="h-3.5 w-3.5 mr-1" />
                                {copied ? 'Copied!' : 'Copy'}
                            </Button>

                            <div className="flex items-center gap-1 ml-2">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleFeedback('up')}
                                    className={cn(
                                        "rounded-full w-7 h-7 hover:bg-secondary/80",
                                        feedback === 'up' && "bg-secondary text-primary"
                                    )}
                                >
                                    <ThumbsUp className="h-3.5 w-3.5" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleFeedback('down')}
                                    className={cn(
                                        "rounded-full w-7 h-7 hover:bg-secondary/80",
                                        feedback === 'down' && "bg-secondary text-primary"
                                    )}
                                >
                                    <ThumbsDown className="h-3.5 w-3.5" />
                                </Button>
                            </div>                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}