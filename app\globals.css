@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .text-gradient-primary {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500;
  }
  .scroll-smooth {
    scroll-behavior: smooth;
  }
}

@layer base {
  :root {
    --background: 220 10% 96%;
    --foreground: 222.2 40% 10%;
    --card: 220 10% 98%;
    --card-foreground: 222.2 40% 10%;
    --popover: 220 10% 98%;
    --popover-foreground: 222.2 40% 10%;
    --primary: 221.2 30% 40%;
    --primary-foreground: 210 20% 98%;
    --primary-gradient: linear-gradient(135deg, #a3a3a3 0%, #6366f1 100%);
    --secondary: 220 10% 92%;
    --secondary-foreground: 222.2 30% 20%;
    --muted: 220 10% 90%;
    --muted-foreground: 215.4 10% 40%;
    --accent: 220 10% 92%;
    --accent-foreground: 222.2 30% 20%;
    --destructive: 0 60% 60%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 10% 85%;
    --input: 220 10% 85%;
    --ring: 221.2 30% 40%;
    --radius: 0.75rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 220 10% 90%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 220 15% 20%;
    --sidebar-accent: 220 10% 85%;
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 220 10% 80%;
    --sidebar-ring: 221.2 30% 40%;
    --brand-gradient: linear-gradient(90deg, #a3a3a3 0%, #3b82f6 100%);
    --text-primary: 220 20% 15%;
    --text-secondary: 220 10% 35%;
    --text-tertiary: 220 8% 55%;
    --text-quaternary: 220 6% 70%;
    --soft-gradient: linear-gradient(90deg, #bdbdbd 0%, #6b8aff 100%);
    --title-gradient: linear-gradient(135deg, #a3a3a3 0%, #3b82f6 100%);
    --title-gradient-dark: linear-gradient(
      135deg,
      #9ca3af 0%,
      #e5e7eb 50%,
      #60a5fa 100%
    );
  }

  .dark {
    --background: 222.2 20% 8%;
    --foreground: 210 40% 98%;
    --card: 222.2 25% 11%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 25% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 12%;
    --muted-foreground: 215 20.2% 75.1%;
    --accent: 217.2 25% 15%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 60%;
    --sidebar-background: 222.2 22% 10%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-accent: 215 25% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.3% 60%;
    --brand-gradient: linear-gradient(90deg, #f87171 0%, #60a5fa 100%);
    --text-primary: 210 40% 98%;
    --text-secondary: 215 20.2% 75.1%;
    --text-tertiary: 215 16% 65%;
    --text-quaternary: 220 13% 55%;
    --soft-gradient: linear-gradient(90deg, #ff8a8a 0%, #8aa2ff 100%);
    --title-gradient: var(--title-gradient-dark);
  }
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar {
  width: 0;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground));
  border-radius: 4px;
  border: 2px solid transparent;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  }
}

.text-gradient {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-blue-500;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Resizing cursor styles */
.cursor-ew-resize {
  cursor: ew-resize;
}

body.resizing {
  cursor: ew-resize;
  user-select: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Smooth scrollbar styling */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

/* Custom animations for AddUserModal */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}

.hover\:scale-102:hover {
  transform: scale(1.02);
}

.border-3 {
  border-width: 3px;
}

/* Blob morphing animations for AddUserModal */
@keyframes morphBlob1 {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate(0, 0) rotate(12deg);
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    transform: translate(10px, -10px) rotate(22deg);
  }
}

@keyframes morphBlob2 {
  0%, 100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: translate(0, 0) rotate(-45deg);
  }
  50% {
    border-radius: 70% 30% 50% 50% / 60% 70% 40% 30%;
    transform: translate(-8px, 8px) rotate(-35deg);
  }
}

@keyframes morphBlob3 {
  0%, 100% {
    border-radius: 60% 40% 40% 60% / 60% 60% 40% 40%;
    transform: translate(0, 0) rotate(90deg);
  }
  33% {
    border-radius: 40% 60% 60% 40% / 40% 40% 60% 60%;
    transform: translate(5px, -5px) rotate(95deg);
  }
  66% {
    border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
    transform: translate(-5px, 5px) rotate(85deg);
  }
}
