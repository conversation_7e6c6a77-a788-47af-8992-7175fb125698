import { Separator } from "@/components/ui/separator"
import PasswordChangeForm from "./passwordForm"
import { <PERSON><PERSON><PERSON>ir<PERSON>, ShieldCheck } from "lucide-react"
import LayoutAnimation from "@/components/Animation/layoutAnimation"
import MultiFactorAuthForm from "./mfaForm"
import { getSession } from "@/lib/auth"

export default async function SettingsSecurityPage() {
    const session = await getSession();
    if (!session) {
        return null
    }
    return (
        <LayoutAnimation>
            <div className="space-y-6">
                <div>
                    <h3 className="text-lg font-medium">Security</h3>
                    <p className="text-sm text-muted-foreground">
                        Manage your account security settings. Set up two-factor authentication and update your password.
                    </p>
                </div>
                <Separator />
                <MultiFactorAuthForm email={session.user.email} />
                <h2 className="text-gray-500 font-bold text-md mb-2">Update password</h2>
                <PasswordChangeForm />
            </div>
        </LayoutAnimation>
    )
}