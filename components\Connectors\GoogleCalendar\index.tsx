"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ConnectorProps } from '../types';
import {
    Loader2,
    CheckCircle2,
    Calendar,
    Power,
    ArrowRight,
    Shield,
    LogIn,
    ChevronLeft
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { queryClient, createMutationFn, useApiMutation, useApiQuery } from "@/lib/apiClient";
import { useRouter } from "next/navigation";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";

interface GoogleCalendarUser {
    email: string
    name: string
    photoUrl: string
}

export default function GoogleCalendarConnector({ data }: ConnectorProps) {
    const router = useRouter()
    const [state, setState] = useState<"disconnected" | "connecting" | "connected">(
        data?.isConnected ? "connected" : "disconnected"
    )
    const [user, setUser] = useState<GoogleCalendarUser | null>(
        data?.config ? {
            email: data.config.email,
            name: data.config.name,
            photoUrl: data.config.picture
        } : null
    )
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [isDisconnecting, setIsDisconnecting] = useState(false)
    const isHandledRef = useRef(false)

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const state = urlParams.get("state");

        if (code && state && !isHandledRef.current) {
            isHandledRef.current = true;
            handleOAuthCallback(code, state);
        }
    }, []);

    const connectGoogleCalendar = useApiMutation(
        createMutationFn.post('/google-calendar/callback'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['google-calendar'] });
                    setUser({
                        email: response.result.data.email,
                        name: response.result.data.name,
                        photoUrl: response.result.data.picture
                    })
                    setState("connected")
                    setError(null)
                    window.history.replaceState({}, document.title, window.location.pathname);
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                setError(error.response?.data?.message || "Failed to connect to Google Calendar")
                setState("disconnected")
            }
        }
    );

    const handleOAuthCallback = async (code: string, state: string) => {
        setState("connecting")
        try {
            await connectGoogleCalendar.mutateAsync({ code, state })
        } finally {
            window.history.replaceState({}, document.title, window.location.pathname)
        }
    }

    const getAuthUrl = useApiMutation(
        createMutationFn.post('/google-calendar/auth-url'),
        {
            onSuccess: (response: any) => {
                console.log('Auth URL response:', response); // Debug log

                if (response?.status && typeof response.result === 'string') {
                    queryClient.invalidateQueries({ queryKey: ['google-calendar-auth-url'] });
                    try {
                        const url = new URL(response.result);
                        console.log('Redirecting to:', url.toString());

                        window.location.href = url.toString();
                        setError(null);
                    } catch (e: any) {
                        console.error('Invalid URL format:', e);
                        setError(`Invalid authorization URL: ${e.message}`);
                    }
                } else {
                    console.error('Unexpected response format:', response);
                    setError("Unexpected response from server");
                }
            },
            onError: (error: any) => {
                console.error('Auth URL error:', error);
                setError(error.response?.data?.message || "Failed to get auth URL");
            }
        }
    );

    const handleConnect = async () => {
        setIsLoading(true);
        try {
            await getAuthUrl.mutateAsync({});
        } catch (error) {
            console.error('Connection error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const disconnectGoogleCalendar = useApiMutation(
        createMutationFn.post('/google-calendar/disconnect'),
        {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['google-calendar'] });
                setState("disconnected")
                setUser(null)
                setError(null)
                setIsDisconnecting(false)
            },
            onError: (error: any) => {
                setError(error.response?.data?.message || "Failed to disconnect from Google Calendar")
                setIsDisconnecting(false)
            }
        }
    );

    const handleDisconnect = async () => {
        setIsDisconnecting(true)
        await disconnectGoogleCalendar.mutateAsync({})
    }

    return (
        <div className="space-y-6">
            <Card className="w-full">
                <CardContent className="space-y-6 my-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <div className="p-6 bg-secondary/30 rounded-lg space-y-4">
                            <Calendar className="h-8 w-8 text-primary" />
                            <h3 className="font-semibold text-lg">Calendar Integration</h3>
                            <p className="text-sm text-muted-foreground">
                                Manage your events and schedule meetings seamlessly
                            </p>
                        </div>
                        <div className="p-6 bg-secondary/30 rounded-lg space-y-4">
                            <Shield className="h-8 w-8 text-primary" />
                            <h3 className="font-semibold text-lg">Secure Access</h3>
                            <p className="text-sm text-muted-foreground">
                                Your calendar is securely accessed through Google's official API
                            </p>
                        </div>
                    </div>

                    <div className="flex justify-center pt-4">
                        {state === "connected" ? (
                            <Button
                                variant="destructive"
                                onClick={handleDisconnect}
                                disabled={isDisconnecting}
                                className="min-w-[200px]"
                            >
                                {/* {isDisconnecting ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Disconnecting...
                                    </>
                                ) : (
                                    <>
                                        <Power className="mr-2 h-4 w-4" />
                                        Disconnect
                                    </>
                                )} */}
                            </Button>
                        ) : (
                            <Button
                                size="lg"
                                onClick={handleConnect}
                                disabled={isLoading}
                                className="min-w-[200px]"
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Connecting...
                                    </>
                                ) : (
                                    <>
                                        <LogIn className="mr-2 h-4 w-4" />
                                        Connect Calendar
                                    </>
                                )}
                            </Button>
                        )}
                    </div>

                    {error && (
                        <div className="bg-destructive/10 text-destructive p-4 rounded-lg">
                            {error}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
} 