import { Module } from '@nestjs/common';
import { PrometheusService } from './prometheus.service';
import { PrometheusController } from './prometheus.controller';
import { MetricsInterceptor } from './prometheus.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';

@Module({
  controllers: [PrometheusController],
  providers: [
    PrometheusService,
    {
      provide: APP_INTERCEPTOR,
      useClass: MetricsInterceptor,
    },
  ],
})
export class PrometheusModule {}
