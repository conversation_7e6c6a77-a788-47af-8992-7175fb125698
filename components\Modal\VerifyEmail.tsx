"use client"

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ger,
    DialogClose
} from "@steps-ai/ui";
import {
    useApiQuery, useApiMutation,
    createMutationFn,
    queryClient
} from "@/lib/apiClient"
import { Button } from "@steps-ai/ui";
import { Mail } from "lucide-react";
import { signOut } from "next-auth/react";
import { toast } from "sonner";

export const VerifyEmail = ({ open }: { open: boolean }) => {


    const sendVerificationEmail = useApiMutation(
        createMutationFn.post('/user/send-verification-email'),
        {
            onMutate: () => {
                toast.loading('Sending verification email...');
            },
            onSuccess: (response: any) => {
                toast.dismiss();
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['user'] });
                    toast.success("Email sent successfully");
                } else {
                    toast.error("Something went wrong. Please try again.");
                }
            },
            onError: (error: any) => {
                toast.dismiss();
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again.";
                    toast.error(errorMessage);
                } else if (error.request) {
                    toast.error("No response received from server");
                } else {
                    toast.error("Error setting up the request");
                }
            }
        }
    );

    const handleSendVerificationEmail = () => {
        sendVerificationEmail.mutate({})
    }


    return (
        <Dialog open={open}>
            <DialogContent className="sm:max-w-[425px] bg-gray-50 flex items-center justify-center p-4 [&>button]:hidden">
                <div className="w-full h-full bg-white rounded-xl shadow-lg p-8 space-y-6 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-1/3 h-full bg-blue-500 opacity-10 rounded-l-full" />

                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Mail className="w-5 h-5 text-blue-600" />
                    </div>

                    <div className="space-y-4 relative">
                        <h1 className="text-2xl font-bold text-gray-900">Account activation</h1>
                        <p className="text-gray-600">
                            To proceed further, please verify your email address. We'll send you an activation link to get started.
                        </p>

                        <Button
                            onClick={handleSendVerificationEmail}
                            disabled={sendVerificationEmail.isPending}
                            className="w-full"
                        >
                            {sendVerificationEmail.isPending ? (
                                <span className="flex items-center gap-2">
                                    <span className="animate-spin">⏳</span>
                                    Sending...
                                </span>
                            ) : (
                                'Send verification email'
                            )}
                        </Button>

                        {sendVerificationEmail.isSuccess && (
                            <div className="mt-8 text-sm">
                                <p className="text-gray-500">Didn't receive an email? Check your spam folder!</p>
                                <button
                                    className="text-blue-600 hover:text-blue-700 mt-1"
                                    onClick={handleSendVerificationEmail}
                                    disabled={sendVerificationEmail.isPending}
                                >
                                    Resend email again
                                </button>
                            </div>
                        )}

                        <Button
                            variant={"destructive"}
                            onClick={() => signOut()}
                            className="mt-8 w-full"
                        >
                            Log out
                        </Button>
                    </div>
                </div>
            </DialogContent>
            <DialogClose></DialogClose>
        </Dialog>
    );
};