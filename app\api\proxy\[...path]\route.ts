import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { getServiceUrl, logServiceUsage } from '@/lib/services-config';

export async function GET(req: NextRequest) {
    try {
        const token = await getToken({
            req: req as any,
            secret: process.env.NEXT_AUTH_SECRET,
            cookieName: "stepsai_secure",
        });
        if (!token) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const path = req.nextUrl.pathname.split('/').slice(3).join('/');
        const serviceUrl = getServiceUrl(path);
        const apiUrl = `${serviceUrl}/${path}`;
        
        const searchParams = req.nextUrl.searchParams;
        const fullUrl = `${apiUrl}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
        
        logServiceUsage(path, 'GET');
        
        const externalRes = await fetch(fullUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token.accessToken}`,
            },
        });

        const data = await externalRes.json();
        return NextResponse.json(data, { status: externalRes.status });
    } catch (error) {
        console.error('Proxy GET error:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

export async function POST(req: NextRequest) {
    try {
        const token = await getToken({
            req: req as any,
            secret: process.env.NEXT_AUTH_SECRET,
            cookieName: "stepsai_secure",
        });

        if (!token?.accessToken) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const path = req.nextUrl.pathname.split('/').slice(3).join('/');
        const serviceUrl = getServiceUrl(path);
        const apiUrl = `${serviceUrl}/${path}`;
        const body = await req.json();
        
        const searchParams = req.nextUrl.searchParams;
        const fullUrl = `${apiUrl}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
        
        logServiceUsage(path, 'POST');

        const externalRes = await fetch(fullUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token.accessToken}`,
            },
            body: JSON.stringify(body)
        });
        const data = await externalRes.json();
        return NextResponse.json(data, { status: externalRes.status });
    } catch (error) {
        console.error('Proxy POST error:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

export async function PUT(req: NextRequest) {
    try {
        const token = await getToken({
            req: req as any,
            secret: process.env.NEXT_AUTH_SECRET,
            cookieName: "stepsai_secure",
        });

        if (!token?.accessToken) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const path = req.nextUrl.pathname.split('/').slice(3).join('/');
        const serviceUrl = getServiceUrl(path);
        const apiUrl = `${serviceUrl}/${path}`;
        const body = await req.json();
        
        const searchParams = req.nextUrl.searchParams;
        
        const fullUrl = `${apiUrl}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
        
        logServiceUsage(path, 'PUT');

        const externalRes = await fetch(fullUrl, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token.accessToken}`,
            },
            body: JSON.stringify(body)
        });
        const data = await externalRes.json();
        return NextResponse.json(data, { status: externalRes.status });
    } catch (error) {
        console.error('Proxy PUT error:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

export async function DELETE(req: NextRequest) {
    try {
        const token = await getToken({
            req: req as any,
            secret: process.env.NEXT_AUTH_SECRET,
            cookieName: "stepsai_secure",
        });

        if (!token?.accessToken) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const path = req.nextUrl.pathname.split('/').slice(3).join('/');
        const serviceUrl = getServiceUrl(path);
        const apiUrl = `${serviceUrl}/${path}`;
        
        const searchParams = req.nextUrl.searchParams;
        const fullUrl = `${apiUrl}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
        
        logServiceUsage(path, 'DELETE');

        const externalRes = await fetch(fullUrl, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token.accessToken}`,
            },
        });
        const data = await externalRes.json();
        return NextResponse.json(data, { status: externalRes.status });
    } catch (error) {
        console.error('Proxy DELETE error:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}