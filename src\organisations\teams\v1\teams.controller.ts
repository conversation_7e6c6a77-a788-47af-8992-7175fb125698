import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  HttpCode,
  HttpStatus,
  Put,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiResponse, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { OrgTeamsService } from './teams.service';
import {
  AddMemberToTeamDto,
  CreateTeamDto,
  UpdateTeamDto,
  AssignTeamLeaderDto,
} from '../dtos/teams.dto';
import { AdminAuthGuard } from '../../../auth/guards/admin-auth.guard';
import { TeamSpecificAdminGuard } from '../../../auth/guards/team-specific-admin.guard';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../../common/decorators/getUser.decorator';
import { JwtPayload } from '../../../common/types/jwt-payload';
import { SwitchTeamContextDto, GetUserTeamsDto } from '../dtos/team-context.dto';
import { BulkAddMembersDto } from '../dtos/bulk-add-members.dto';
// Team invitation endpoints have been moved to organization-level
@ApiTags('OrgTeams-V1')
@Controller({ version: '1', path: 'teams' })
export class OrgTeamsController {
  constructor(private readonly teamsService: OrgTeamsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new team' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Team created successfully',
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createTeam(@Body() data: CreateTeamDto, @GetCurrentUser() user: JwtPayload) {
    data.organizationId = user.orgId;
    return this.teamsService.createTeam(data);
  }

  @Get('org')
  @ApiOperation({ summary: 'Get all teams for the current user\'s organization' })
  @ApiResponse({
    status: 200,
    description: 'All teams for the organization',
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(200)
  async getTeams(@GetCurrentUser() user: JwtPayload) {
    return this.teamsService.getTeams(user.orgId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a team by id with complete member information',
    description: 'Retrieves detailed team information including all team members with their complete profiles and roles.'
  })
  @ApiResponse({
    status: 200,
    description: 'Team details with complete member information',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Team fetched successfully' },
        team: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'team-uuid' },
            name: { type: 'string', example: 'Development Team' },
            description: { type: 'string', example: 'Team responsible for product development' },
            organizationId: { type: 'string', example: 'org-uuid' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            memberCount: { type: 'number', example: 5 },
            memberIds: {
              type: 'array',
              items: { type: 'string' },
              example: ['user-1', 'user-2', 'user-3']
            },
            members: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'user-uuid' },
                  name: { type: 'string', example: 'John Doe' },
                  email: { type: 'string', example: '<EMAIL>' },
                  role: { type: 'string', example: 'USER', enum: ['SUPERADMIN', 'ADMIN', 'TEAMADMIN', 'USER'] },
                  profileImageUrl: { type: 'string', example: 'https://example.com/profile.jpg', nullable: true },
                  emailVerified: { type: 'boolean', example: true },
                  hasOnboarded: { type: 'boolean', example: true },
                  onboardingCompleted: { type: 'boolean', example: true },
                  createdAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' }
                }
              }
            },
            organization: {
              type: 'object',
              properties: {
                id: { type: 'string', example: 'org-uuid' },
                name: { type: 'string', example: 'Example Organization' },
                logo: { type: 'string', example: 'https://example.com/logo.png', nullable: true }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Team not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Team not found' },
        error: { type: 'string', example: 'Not Found' }
      }
    }
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(200)
  async getTeam(@Param('id') teamId: string) {
    return this.teamsService.getTeam(teamId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a team' })
  @ApiResponse({
    status: 200,
    description: 'Team updated successfully',
  })
  @ApiBearerAuth()
  @UseGuards(TeamSpecificAdminGuard)
  @HttpCode(200)
  async updateTeam(@Param('id') teamId: string, @Body() data: UpdateTeamDto) {
    return this.teamsService.updateTeam(teamId, data);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a team' })
  @ApiResponse({
    status: 200,
    description: 'Team deleted successfully',
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(200)
  async deleteTeam(@Param('id') teamId: string) {
    return this.teamsService.deleteTeam(teamId);
  }

  @Post(':id/members')
  @ApiOperation({ summary: 'Add a member to a team' })
  @ApiResponse({
    status: 200,
    description: 'Member added to team successfully',
  })
  @ApiBearerAuth()
  @UseGuards(TeamSpecificAdminGuard)
  @HttpCode(200)
  async addMemberToTeam(
    @Param('id') teamId: string,
    @Body() data: AddMemberToTeamDto,
  ) {
    return this.teamsService.addMemberToTeam(teamId, data);
  }

  @Delete(':id/members/:memberId')
  @ApiOperation({ summary: 'Remove a member from a team' })
  @ApiResponse({
    status: 200,
    description: 'Member removed from team successfully',
  })
  @ApiBearerAuth()
  @UseGuards(TeamSpecificAdminGuard)
  @HttpCode(200)
  async removeMemberFromTeam(
    @Param('id') teamId: string,
    @Param('memberId') memberId: string,
  ) {
    return this.teamsService.removeMemberFromTeam(teamId, memberId);
  }

  @Get('user/teams')
  @ApiOperation({ summary: 'Get all teams for the current user' })
  @ApiResponse({
    status: 200,
    description: 'All teams for the user',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async getUserTeams(
    @GetCurrentUser() user: JwtPayload,
    @Query() query: GetUserTeamsDto,
  ) {
    return this.teamsService.getUserTeams(user.sub, query);
  }

  @Post('context/switch')
  @ApiOperation({ summary: 'Switch team context' })
  @ApiResponse({
    status: 200,
    description: 'Team context switched successfully',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async switchTeamContext(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: SwitchTeamContextDto,
  ) {
    return this.teamsService.switchTeamContext(user.sub, data);
  }

  @Post('members/bulk-add')
  @ApiOperation({ summary: 'Add multiple members to a team by email' })
  @ApiResponse({
    status: 200,
    description: 'Members added to team successfully',
  })
  @ApiBearerAuth()
  @UseGuards(TeamSpecificAdminGuard)
  @HttpCode(200)
  async bulkAddMembersToTeam(@Body() data: BulkAddMembersDto) {
    return this.teamsService.bulkAddMembersToTeam(data);
  }

  @Post(':id/assign-leader')
  @ApiOperation({
    summary: 'Assign a team leader (TeamAdmin) to a team',
    description: 'Promotes an existing team member to TeamAdmin role. Only organization admins can perform this action.'
  })
  @ApiResponse({
    status: 200,
    description: 'Team leader assigned successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Team leader assigned successfully' },
        teamLeader: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'user-uuid' },
            name: { type: 'string', example: 'John Doe' },
            email: { type: 'string', example: '<EMAIL>' },
            role: { type: 'string', example: 'TEAMADMIN' },
            profileImageUrl: { type: 'string', example: 'https://example.com/profile.jpg', nullable: true }
          }
        },
        team: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'team-uuid' },
            name: { type: 'string', example: 'Development Team' },
            description: { type: 'string', example: 'Team responsible for product development' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - User is not a member of the team or already a TeamAdmin',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'User is not a member of this team' },
        error: { type: 'string', example: 'Bad Request' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Team or user not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Team not found' },
        error: { type: 'string', example: 'Not Found' }
      }
    }
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(200)
  async assignTeamLeader(
    @Param('id') teamId: string,
    @Body() data: AssignTeamLeaderDto,
  ) {
    return this.teamsService.assignTeamLeader(teamId, data);
  }

}
