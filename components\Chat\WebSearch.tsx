'use client'

import { cn } from '@/lib/utils'
import { Globe, Settings } from 'lucide-react'
import { Toggle } from '../ui/toggle'
import useChatStore from '@/store/chatStore'
import { useEffect } from 'react';
export function WebSearchToggle() {
    const { webSearch, setWebSearch } = useChatStore();

    const handleSearchModeChange = (pressed: boolean) => {
        setWebSearch(pressed);
    }


    return (
        <Toggle
            aria-label="Toggle search mode"
            pressed={webSearch}
            onPressedChange={handleSearchModeChange}
            // variant="outline"
            className={cn(
                'gap-1 px-3 border rounded-full',
                webSearch ? [
                    'bg-blue-500',
                    'text-white',
                    'border-blue-500',
                ] : [
                    'border-input',
                    'text-muted-foreground',
                    'bg-background',
                    'hover:bg-accent',
                    'hover:text-accent-foreground'
                ]
            )}
        >
            <Globe className="size-2 md:size-4" />
            {/* <span className="text-xs hidden md:block">Web Search</span> */}
        </Toggle>
    )
}
