export const teamLeaderAssignmentEmail = (userName: string, teamName: string, organizationName: string) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                margin: 0;
                padding: 0;
                background-color: #f6f9fc;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            }

            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 40px 20px;
            }

            .card {
                background-color: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .title {
                color: #1a1a1a;
                font-size: 24px;
                margin-bottom: 24px;
                text-align: center;
            }

            .text {
                color: #4a5568;
                font-size: 16px;
                line-height: 24px;
                margin-bottom: 32px;
            }

            .highlight {
                background-color: #e6fffa;
                border-left: 4px solid #38b2ac;
                padding: 16px;
                margin: 24px 0;
                border-radius: 4px;
            }

            .responsibilities {
                background-color: #f7fafc;
                padding: 20px;
                border-radius: 6px;
                margin: 20px 0;
            }

            .responsibilities h3 {
                color: #2d3748;
                margin-top: 0;
                margin-bottom: 12px;
                font-size: 18px;
            }

            .responsibilities ul {
                margin: 0;
                padding-left: 20px;
            }

            .responsibilities li {
                color: #4a5568;
                margin-bottom: 8px;
                line-height: 1.5;
            }

            .footer {
                margin-top: 32px;
                text-align: center;
                color: #718096;
                font-size: 14px;
            }

            .congratulations {
                text-align: center;
                font-size: 18px;
                color: #38b2ac;
                font-weight: 600;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="card">
                <h1 class="title">🎉 Congratulations! You're now a Team Leader</h1>
                
                <div class="congratulations">
                    Welcome to your new leadership role!
                </div>
                
                <div class="text">
                    <p>Hello ${userName},</p>
                    
                    <p>We're excited to inform you that you have been assigned as the <strong>Team Leader</strong> for the <strong>${teamName}</strong> team at <strong>${organizationName}</strong>.</p>
                </div>

                <div class="highlight">
                    <p><strong>Your new role:</strong> Team Admin</p>
                    <p>You now have enhanced permissions to manage your team effectively.</p>
                </div>

                <div class="responsibilities">
                    <h3>Your new responsibilities include:</h3>
                    <ul>
                        <li>Managing team members and their access</li>
                        <li>Overseeing team projects and knowledge bases</li>
                        <li>Coordinating team activities and collaboration</li>
                        <li>Acting as a liaison between your team and organization admins</li>
                        <li>Ensuring team productivity and goal achievement</li>
                    </ul>
                </div>

                <div class="text">
                    <p>Your enhanced permissions are now active. You can start managing your team immediately by logging into your account.</p>
                    
                    <p>If you have any questions about your new role or need assistance, please don't hesitate to reach out to your organization administrator.</p>
                    
                    <p>We're confident you'll do great in this leadership position!</p>
                </div>
                
                <div class="footer">
                    <p>Best regards,<br>The ${organizationName} Team</p>
                    <p style="margin-top: 20px; font-size: 12px;">This is an automated notification from your organization's admin panel.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
};
