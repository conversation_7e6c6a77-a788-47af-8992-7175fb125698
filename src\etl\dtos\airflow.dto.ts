import { ApiProperty } from '@nestjs/swagger';
import { EtlStatus } from '@prisma/client';

import {
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { ConnectorName } from '../etl.service';

export class AirflowPublishDto {
  @ApiProperty({
    description: 'The Dag ID',
    example: 'NOTION',
  })
  @IsString()
  @IsNotEmpty()
  dagId: ConnectorName;

  @ApiProperty({
    description: 'The Dag Run ID',
    example: 'manual__2025-04-17T18:26:55.379845+00:00',
  })
  @IsString()
  @IsNotEmpty()
  dagRunId: string;

  @ApiProperty({
    description: 'The code from',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  state: EtlStatus;

  @ApiProperty({
    description: 'The execution/start date of the DAG run',
    example: '2025-04-17T18:26:55.379845+00:00',
  })
  @IsDateString()
  @IsNotEmpty()
  executionDate: string;

  @ApiProperty({
    description: 'The end date of the DAG run',
    example: '2025-04-17T19:26:55.379845+00:00',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}
