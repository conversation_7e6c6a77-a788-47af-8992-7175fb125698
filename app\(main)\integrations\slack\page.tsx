import ComingSoon from '@/components/ComingSoon';
import { Bo<PERSON>, MessageSquare, Sparkles } from 'lucide-react';

const features = [
    {
        icon: <Bot />,
        title: "AI-Powered Answers",
        description: "Get instant, accurate responses from your document base using advanced RAG technology."
    },
    {
        icon: <MessageSquare />,
        title: "Seamless Slack Integration",
        description: "Access your knowledge base directly from your Slack workspace without switching context."
    },
    {
        icon: <Sparkles />,
        title: "Smart Context Understanding",
        description: "AIDE understands the context of your conversations and provides precise, relevant answers."
    }
];

export default function SlackIntegrationPage() {
    return (
        <main className="flex-1"> 
            <ComingSoon 
                title="Slack Integration Coming Soon"
                subtitle="We're bringing AIDE's powerful features directly to your Slack workspace. Get notified when we launch!"
                features={features}
            />
        </main>
    );
}