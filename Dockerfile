# Stage 1: Build Stage
FROM public.ecr.aws/docker/library/node:20.11-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json to install dependencies
COPY package*.json ./

# Install dependencies (only production dependencies will be copied later)

RUN npm install --legacy-peer-deps

# Copy the application source code
COPY . .

# Generate Prisma Client
RUN npx prisma generate

# Build the NestJS application
RUN npm run build

# Stage 2: Production Stage
FROM public.ecr.aws/docker/library/node:20.11-alpine AS production

# Set the working directory inside the container
WORKDIR /app

# Copy only required files from the builder stage
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/dist ./dist

# Expose application port
EXPOSE 3000

# Run the Prisma migrations before starting the app
CMD ["npm", "run", "start:migrate:prod"]
