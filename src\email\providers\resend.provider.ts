import { Resend } from 'resend';
import {
  EmailProviderStrategy,
  EmailOptions,
} from '../interface/email.interface';

export class ResendEmailProvider implements EmailProviderStrategy {
  private client: Resend;

  constructor(apiKey: string) {
    this.client = new Resend(apiKey);
  }

  async sendEmail(options: EmailOptions) {
    try {
      const result = await this.client.emails.send({
        from: options.from,
        to: options.to,
        cc: options.cc,
        bcc: options.bcc,
        subject: options.subject,
        html: options.html,
      });
      return result;
    } catch (error) {
      throw new Error(`Resend Email Error: ${error.message}`);
    }
  }
}
