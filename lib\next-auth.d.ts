import { Session, User as BaseUser, Account, JWT } from "next-auth";

export type UserRole = 'USER' | 'ADMIN' | 'SUPERADMIN' | 'TEAMADMIN';

export interface Organization {
    id: string;
    name: string;
    logo?: string | null;
}

export interface Team {
    id: string;
    name: string;
    description?: string;
    organizationId: string;
}

declare module "next-auth" {
    interface Session {
        user: {
            id: string;
            email: string;
            name: string;
            image: string | null;
            hasOnboarded?: boolean;
            initialOnboardingCompleted?: boolean;
            accessToken?: string;
            refreshToken?: string;
            role?: UserRole;
            organizationId?: string | null;
            organization?: Organization | null;
            teams?: Team[];
            currentTeam?: Team | null;
        };
        expires: number;
    }

    interface User extends BaseUser {
        id: string;
        email: string;
        name: string;
        image: string | null;
        hasOnboarded?: boolean;
        initialOnboardingCompleted?: boolean;
        accessToken?: string;
        refreshToken?: string;
        role?: UserRole;
        organizationId?: string | null;
        organization?: Organization | null;
        teams?: Team[];
        currentTeam?: Team | null;
    }
}

declare module "next-auth/jwt" {
    interface JWT {
        id: string;
        email: string;
        name: string;
        image: string | null;
        accessToken?: string;
        refreshToken?: string;
        hasOnboarded?: boolean;
        initialOnboardingCompleted?: boolean;
        role?: UserRole;
        organizationId?: string | null;
        organization?: Organization | null;
        teams?: Team[];
        currentTeam?: Team | null;
        expires: number;
    }
}

