"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme } from "next-themes";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, Menu, Sun, Moon, ChevronRight } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DocsLayoutProps {
  children: React.ReactNode;
  navigation: {
    title: string;
    slug: { current: string };
    sections?: { sectionTitle: string }[];
  }[];
  currentDoc?: {
    title: string;
    slug: { current: string };
  };
}

export function DocsLayout({ children, navigation, currentDoc }: DocsLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { setTheme, theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="flex min-h-screen bg-background">
      {/* Top Navigation */}
      <motion.header 
        className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] p-0">
                <MobileNavigation 
                  navigation={navigation} 
                  currentDoc={currentDoc} 
                  onClose={() => setIsMobileMenuOpen(false)} 
                />
              </SheetContent>
            </Sheet>
            
            <Link href="/" className="flex items-center gap-2">
              <Image
                src="/ai-logo.svg"
                alt="Logo"
                width={32}
                height={32}
                className="dark:invert"
              />
              <span className="font-semibold text-lg hidden sm:inline-block">
                AIDE Docs
              </span>
            </Link>
          </div>

          <nav className="hidden md:flex items-center gap-6">
            <Link 
              href="/"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Home
            </Link>
            <Link 
              href="/documentation"
              className="text-sm font-medium text-foreground"
            >
              Documentation
            </Link>
            <Link 
              href="/chat"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Chat
            </Link>
            <Link 
              href="/contact"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Contact
            </Link>
          </nav>

          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="hidden sm:inline-flex">
              <Search className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  {theme === "light" ? (
                    <Sun className="h-4 w-4" />
                  ) : (
                    <Moon className="h-4 w-4" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setTheme("light")}>
                  Light
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("dark")}>
                  Dark
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("system")}>
                  System
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </motion.header>

      {/* Desktop Sidebar */}
      <motion.aside
        className="hidden md:flex flex-col w-64 border-r bg-card fixed left-0 top-16 bottom-0"
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
      >
        <ScrollArea className="flex-1 py-6">
          <nav className="px-4 space-y-2">
            <AnimatePresence mode="popLayout">
              {navigation.map((item) => (
                <motion.div
                  key={item.slug.current}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link
                    href={`/documentation/${item.slug.current}`}
                    className={cn(
                      "flex items-center justify-between px-4 py-2 text-sm rounded-md transition-colors",
                      currentDoc?.slug.current === item.slug.current
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground hover:bg-accent"
                    )}
                  >
                    <span>{item.title}</span>
                    {item.sections && item.sections.length > 0 && (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Link>
                </motion.div>
              ))}
            </AnimatePresence>
          </nav>
        </ScrollArea>
      </motion.aside>

      {/* Main Content */}
      <main className="flex-1 md:pl-64 pt-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
        >
          {children}
        </motion.div>
      </main>
    </div>
  );
}

function MobileNavigation({ 
  navigation, 
  currentDoc, 
  onClose 
}: { 
  navigation: DocsLayoutProps["navigation"];
  currentDoc?: DocsLayoutProps["currentDoc"];
  onClose: () => void;
}) {
  return (
    <ScrollArea className="h-full py-6">
      <div className="px-4 pb-4 border-b">
        <Link href="/" className="flex items-center gap-2" onClick={onClose}>
          <Image
            src="/ai-logo.svg"
            alt="Logo"
            width={24}
            height={24}
            className="dark:invert"
          />
          <span className="font-semibold">AIDE Docs</span>
        </Link>
      </div>
      <nav className="px-4 py-6">
        <div className="space-y-4">
          <Link
            href="/"
            className="block text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            onClick={onClose}
          >
            Home
          </Link>
          <Link
            href="/documentation"
            className="block text-sm font-medium text-foreground"
            onClick={onClose}
          >
            Documentation
          </Link>
          <Link
            href="/chat"
            className="block text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            onClick={onClose}
          >
            Chat
          </Link>
          <Link
            href="/contact"
            className="block text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            onClick={onClose}
          >
            Contact
          </Link>
        </div>
        <div className="mt-6 space-y-2">
          {navigation.map((item) => (
            <Link
              key={item.slug.current}
              href={`/documentation/${item.slug.current}`}
              className={cn(
                "block px-4 py-2 text-sm rounded-md transition-colors",
                currentDoc?.slug.current === item.slug.current
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:text-foreground hover:bg-accent"
              )}
              onClick={onClose}
            >
              {item.title}
            </Link>
          ))}
        </div>
      </nav>
    </ScrollArea>
  );
}
