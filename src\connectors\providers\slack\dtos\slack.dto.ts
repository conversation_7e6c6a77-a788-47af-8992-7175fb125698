import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class SlackCallbackDto {
  @ApiProperty({
    description: 'The code from Slack',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'The state from Slack',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  state: string;
}