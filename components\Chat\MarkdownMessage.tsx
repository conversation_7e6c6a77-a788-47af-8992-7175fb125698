"use client"
import { marked } from 'marked';
import { memo, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import remarkBreaks from 'remark-breaks';
import rehypeRaw from 'rehype-raw';
import rehypeKatex from 'rehype-katex';
import rehypeMathjax from 'rehype-mathjax';
import 'katex/dist/katex.min.css';

const MarkdownBlock = memo(
  ({ content }: { content: string }) => {
    return <ReactMarkdown
      remarkPlugins={[remarkGfm, remarkMath, remarkBreaks]}
      rehypePlugins={[rehypeRaw, rehypeKatex, rehypeMathjax]}
      components={{
        p: ({ children }) => <p className="mb-4 text-gray-800 dark:text-gray-200 leading-relaxed">{children}</p>,
        h1: ({ children }) => <h1 className="text-2xl font-bold my-4 text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">{children}</h1>,
        h2: ({ children }) => <h2 className="text-xl font-bold my-4 text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-1">{children}</h2>,
        h3: ({ children }) => <h3 className="text-lg font-bold my-4 text-gray-900 dark:text-white">{children}</h3>,
        h4: ({ children }) => <h4 className="text-base font-bold my-4 text-gray-900 dark:text-white">{children}</h4>,
        h5: ({ children }) => <h5 className="text-sm font-bold my-4 text-gray-900 dark:text-white">{children}</h5>,
        ul: ({ children }) => <ul className="list-disc pl-6 mb-4">{children}</ul>,
        ol: ({ children }) => <ol className="list-decimal pl-6 mb-4">{children}</ol>,
        li: ({ children }) => <li className="mb-1">{children}</li>,
        blockquote: ({ children }) => <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 py-1 italic my-4 text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-200">{children}</blockquote>,
        a: ({ children, href }) => <a href={href} className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline" target="_blank" rel="noopener noreferrer">{children}</a>,
        img: ({ src, alt }) => <img src={src} alt={alt} className="max-w-full h-auto my-4 rounded shadow-sm" />,
        code: ({ children, className }) => {
          const match = /language-(\w+)/.exec(className || '');
          return match ? (
            <pre className="bg-gray-100 dark:bg-gray-800 rounded p-3 my-4 overflow-x-auto">
              <code className={className + " text-gray-800 dark:text-gray-200"}>{children}</code>
            </pre>
          ) : (
            <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm text-gray-800 dark:text-gray-200">{children}</code>
          );
        },
        pre: ({ children }) => <div className="bg-gray-100 dark:bg-gray-800 rounded my-4 overflow-hidden">{children}</div>,
        table: ({ children }) => <div className="overflow-x-auto my-4"><table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-700">{children}</table></div>,
        thead: ({ children }) => <thead className="bg-gray-50 dark:bg-gray-800">{children}</thead>,
        tbody: ({ children }) => <tbody className="divide-y divide-gray-200 dark:divide-gray-700">{children}</tbody>,
        tr: ({ children }) => <tr>{children}</tr>,
        th: ({ children }) => <th className="px-3 py-2 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider">{children}</th>,
        td: ({ children }) => <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{children}</td>,
        hr: () => <hr className="my-6 border-t border-gray-300 dark:border-gray-700" />,
        strong: ({ children }) => <strong className="font-bold">{children}</strong>,
        em: ({ children }) => <em className="italic">{children}</em>,
      }}
    >{content}</ReactMarkdown>;
  },
  (prevProps, nextProps) => {
    if (prevProps.content !== nextProps.content) return false;
    return true;
  },
);

MarkdownBlock.displayName = 'MarkdownBlock';

export const MarkdownMessage = memo(
  ({ content, id }: { content: string; id: string }) => {
    return <MarkdownBlock content={content} key={id} />;
  },
);

MarkdownMessage.displayName = 'MarkdownMessage';

