"use client"
import { useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@steps-ai/ui"
import Image from "next/image"
import { User, Camera, CheckCircle, AlertCircle } from "lucide-react"

import { Form,FormControl,FormDescription,FormField,FormItem,FormLabel,FormMessage } from "@/components/ui/form"
import { Input } from "@steps-ai/ui"
import {
  useApiQuery, useApiMutation,
  createMutationFn,
  queryClient
} from "@/lib/apiClient"
import { Skeleton } from "@steps-ai/ui"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import axios from "axios"
import { toast } from "sonner"
import { useState } from "react"

const accountFormSchema = z.object({
  name: z
    .string()
    .min(2, {
      message: "Name must be at least 2 characters.",
    })
    .max(30, {
      message: "Name must not be longer than 30 characters.",
    }),
  email: z
    .string()
    .email({
      message: "Please enter a valid email address.",
    })
    .min(2, {
      message: "Email must be at least 2 characters.",
    })
    .max(50, {
      message: "Email must not be longer than 50 characters.",
    })
})

type AccountFormValues = z.infer<typeof accountFormSchema>

export function AccountForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [profilePhoto, setProfilePhoto] = useState<string | null>()
  const [loading, setLoading] = useState(false)

  const { data, isLoading, refetch } = useApiQuery<{
    status: boolean;
    statusCode: number;
    result: {
      name: string;
      email: string;
      id: string;
      profileImageUrl: string | null;
      emailVerified: boolean;
    };
    version: string;
  }>(['User'], '/user/me');


  const updateName = useApiMutation(
    createMutationFn.post('/user/update-user'),
    {
      onSuccess: (response: any) => {
        if (response && response.status) {
          queryClient.invalidateQueries({ queryKey: ['user'] });
          toast.success("Name updated successfully");
        } else {
          toast.error("Something went wrong. Please try again.");
        }
      },
      onError: (error: any) => {
        if (error.response) {
          const errorMessage = error.response.data?.message ||
            "Something went wrong. Please try again.";
          toast.error(errorMessage);
        } else if (error.request) {
          toast.error("No response received from server");
        } else {
          toast.error("Error setting up the request");
        }
      }
    }
  );


  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      name: data?.result?.name || "",
      email: data?.result?.email || "",
    },
  })
  useEffect(() => {
    if (data?.result) {
      form.reset({
        name: data.result.name,
        email: data.result.email,
      })
      setProfilePhoto(data.result.profileImageUrl)
    }
  }, [data, form])

  async function onSubmit(values: AccountFormValues) {
    setIsSubmitting(true)
    await updateName.mutateAsync({
      name: values.name,
    })
    setIsSubmitting(false)
  }


  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setProfilePhoto(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }
  const updateProfilePicture = useApiMutation(
    createMutationFn.post('/user/update-user'),
    {
      onSuccess: (response: any) => {
        if (response && response.status) {
          queryClient.invalidateQueries({ queryKey: ['user'] });
          toast.success("Profile photo updated successfully");
        } else {
          toast.error("Something went wrong. Please try again.");
        }
      },
      onError: (error: any) => {
        if (error.response) {
          const errorMessage = error.response.data?.message ||
            "Something went wrong. Please try again.";
          toast.error(errorMessage);
        } else if (error.request) {
          toast.error("No response received from server");
        } else {
          toast.error("Error setting up the request");
        }
      }
    }
  );
  const handleSubmit = async () => {
    if (!profilePhoto || !data?.result?.id) return

    const response = await fetch(profilePhoto);
    const blob = await response.blob();
    const file = new File([blob], data?.result?.id, { type: blob.type });

    const formData = new FormData();
    formData.append('files', file);
    formData.append('folderName', `profile`);

    try {
      setLoading(true);
      const response = await axios.post(process.env.NEXT_PUBLIC_UPLOAD_URL as string, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });
      if (!response.data.status) {
        toast.error(response.data.message);
        return;
      }

      const profileData = {
        profileImageUrl: response.data.result.uploadResults[0].url
      }
      updateProfilePicture.mutate(profileData);
      setLoading(false);
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
      setLoading(false);
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Profile Header Skeleton */}
        <div className="flex flex-col sm:flex-row items-start gap-6 pb-6">
          <Skeleton className="h-20 w-20 rounded-full" />
          <div className="flex-1 space-y-3">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-5 w-72" />
            <div className="flex items-center gap-3">
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </div>

        <Separator />

        {/* Profile Photo Skeleton */}
        <div className="space-y-4 pb-6">
          <div className="space-y-1">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex items-center gap-6">
            <Skeleton className="h-24 w-24 rounded-full" />
            <div className="space-y-3">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
        </div>

        <Separator />

        {/* Form Skeleton */}
        <div className="space-y-6">
          <div className="space-y-1">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-80" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-11 w-full" />
              <Skeleton className="h-3 w-48" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-11 w-full" />
              <Skeleton className="h-3 w-52" />
            </div>
          </div>
          <div className="flex justify-end pt-6 border-t">
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Profile Header */}
      <div className="flex flex-col sm:flex-row items-start gap-6 pb-6">
        <Avatar className="h-20 w-20 border-2 border-border shadow-sm">
          <AvatarImage src={profilePhoto || data?.result?.profileImageUrl || undefined} alt={data?.result?.name} />
          <AvatarFallback className="text-2xl font-semibold bg-gradient-to-br from-primary/10 to-primary/5 text-primary">
            {data?.result?.name?.charAt(0).toUpperCase() || <User className="h-8 w-8" />}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 space-y-3">
          <h3 className="text-2xl font-bold tracking-tight">{data?.result?.name}</h3>
          <p className="text-muted-foreground text-lg leading-relaxed">
            {data?.result?.email}
          </p>
          
          <div className="flex items-center gap-3">
            <Badge 
              variant={data?.result?.emailVerified ? "default" : "secondary"} 
              className="px-3 py-1.5 font-medium border-0"
            >
              {data?.result?.emailVerified ? (
                <>
                  <CheckCircle className="h-3 w-3 mr-1.5" />
                  Verified
                </>
              ) : (
                <>
                  <AlertCircle className="h-3 w-3 mr-1.5" />
                  Unverified
                </>
              )}
            </Badge>
            <span className="text-sm text-muted-foreground">Email status</span>
          </div>
        </div>
      </div>

      <Separator className="my-6" />

      {/* Profile Photo Section */}
      <div className="space-y-4 pb-6">
        <div>
          <h3 className="text-lg font-semibold">Profile Photo</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Upload a professional photo for your profile
          </p>
        </div>
        
        <div className="flex items-center gap-6">
          <div className="relative">
            <Avatar className="h-24 w-24 border-2 border-border">
              <AvatarImage src={profilePhoto || data?.result?.profileImageUrl || undefined} alt="Profile" />
              <AvatarFallback className="text-xl">
                <User className="h-8 w-8 text-muted-foreground" />
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-primary rounded-full flex items-center justify-center">
              <Camera className="h-3 w-3 text-primary-foreground" />
            </div>
          </div>
          
          <div className="space-y-3">
            <Input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
              id="profile-photo-upload"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById('profile-photo-upload')?.click()}
              size="sm"
              className="px-6"
            >
              Choose Photo
            </Button>
            {profilePhoto && (
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={loading}
                size="sm"
                className="px-6"
              >
                {loading ? "Uploading..." : "Save Photo"}
              </Button>
            )}
          </div>
        </div>
      </div>

      <Separator className="my-6" />

      {/* Account Information */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold">Account Information</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Update your personal details and contact information
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Full Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your full name"
                        {...field}
                        disabled={isSubmitting}
                        className="h-11"
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      This name will be displayed on your profile
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Email Address</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        {...field}
                        disabled
                        className="bg-muted/50 h-11 border-dashed"
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      Your primary email address cannot be changed from this interface
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <Separator />
          <div className="flex items-center justify-between pt-2">
            <p className="text-sm text-muted-foreground">
              Changes will be applied to your profile immediately.
            </p>
            <Button
              type="submit"
              disabled={isSubmitting || !form.formState.isDirty}
              className="px-8 h-11"
            >
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </Form>
    </>
  )
}
