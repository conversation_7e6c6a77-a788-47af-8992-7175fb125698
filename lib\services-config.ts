export interface ServiceConfig {
  name: string;
  baseUrl: string;
  paths: string[];
  description: string;
}

export const microservices: ServiceConfig[] = [
  {
    name: 'main-api',
    baseUrl: process.env.API_URL || 'http://localhost:5000',
    paths: ['v1/users', 'v1/teams', 'v1/organizations', 'v1/invitations', 'v1/connectors', 'v1/onboarding', 'v1/activities'],
    description: 'Main API service for users, teams, organizations'
  },
  {
    name: 'chat-service',
    baseUrl: process.env.NEXT_PUBLIC_CHAT_API_URL || 'http://localhost:5000',
    paths: ['v1/chat', 'chat'],
    description: 'Chat and messaging service'
  },
//   {
//     name: 'files-service',
//     baseUrl: process.env.FILES_API_URL || 'http://localhost:5000',
//     paths: ['v1/files', 'files', 'v1/uploads', 'uploads'],
//     description: 'File upload and management service'
//   }
];

export const getServiceForPath = (path: string): ServiceConfig => {
  for (const service of microservices) {
    for (const servicePath of service.paths) {
      if (path.startsWith(servicePath)) {
        return service;
      }
    }
  }
  
  return microservices[0];
};

export const getServiceUrl = (path: string): string => {
  const service = getServiceForPath(path);
  return service.baseUrl;
};

export const logServiceUsage = (path: string, method: string) => {
  if (process.env.NODE_ENV === 'development') {
    const service = getServiceForPath(path);
    console.log(`🔗 [${method}] ${path} → ${service.name} (${service.baseUrl})`);
  }
};
