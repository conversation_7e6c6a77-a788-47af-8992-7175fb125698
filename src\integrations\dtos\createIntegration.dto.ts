import { ConnectorsStatus, ConnectorsType, IntegrationsStatus, IntegrationsType } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  ValidateNested,
  IsArray,
  ArrayNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';

export class IntegrationItem {
  @ApiProperty({
    description: 'The name of the integration',
    example: 'Slack Integration',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'URL or Base64 string of the integration logo',
    example: 'https://example.com/logo.png',
  })
  @IsString()
  @IsNotEmpty()
  logo: string;

  @ApiProperty({
    description: 'Detailed description of the integration',
    example: 'Integrates with Slack workspace for messaging',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Current status of the integration',
    enum: IntegrationsStatus,
    example: IntegrationsStatus.ACTIVE,
  })
  @IsEnum(IntegrationsStatus)
  @IsNotEmpty()
  status: IntegrationsStatus;

  @ApiProperty({
    description: 'Slug of the integration',
    example: 'slack',
  })
  @IsString()
  @IsNotEmpty()
  slug: string;

  @ApiProperty({
    description: 'Type of the integration',
    enum: IntegrationsType,
    example: IntegrationsType.FREE,
  })
  @IsEnum(IntegrationsType)
  @IsNotEmpty()
  type: IntegrationsType;
}

export class CreateIntegrationDto {
  @ApiProperty({
    description: 'Array of integrations',
    type: [IntegrationItem],
    isArray: true,
    example: [
      {
        name: 'Slack Integration',
        logo: 'https://example.com/logo.png',
        description: 'Integrates with Slack workspace for messaging',
        status: IntegrationsStatus.ACTIVE,
        slug: 'slack',
        type: IntegrationsType.FREE,
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => IntegrationItem)
  integrations: IntegrationItem[];
}
