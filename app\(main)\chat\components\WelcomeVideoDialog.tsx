"use client";

import { useEffect, useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { motion, AnimatePresence } from "framer-motion";
import { X, Video } from "lucide-react";

const WelcomeVideoDialog = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('hasSeenWelcomeVideo') !== 'true';
    }
    return true;
  });

  const handleClose = () => {
    setIsDialogOpen(false);
    localStorage.setItem('hasSeenWelcomeVideo', 'true');
  };

  const handleOpen = () => {
    setIsDialogOpen(true);
  };

  return (
    <>
      <AnimatePresence mode="wait">
        {isDialogOpen && (
          <Dialog open={isDialogOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-[90vw] md:max-w-[800px] lg:max-w-[1000px] p-0 overflow-hidden bg-background border-none">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="relative w-full"
              >
                {/* <button
                  onClick={handleClose}
                  className="absolute -top-2 -right-2 z-50 rounded-full bg-background/80 backdrop-blur-sm border border-border shadow-lg p-2 text-foreground hover:bg-muted transition-all duration-200"
                  aria-label="Close dialog"
                >
                  <X className="h-4 w-4" />
                </button> */}
                <div className="aspect-video w-full rounded-lg overflow-hidden shadow-xl">
                  <video
                    width="100%"
                    height="100%"
                    controls
                    className="w-full h-full"
                    playsInline
                  >
                    <source src="https://stepsai-assets.s3.us-west-2.amazonaws.com/videos/demoaide.mp4" type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </motion.div>
            </DialogContent>
          </Dialog>
        )}

        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.2,
            ease: "easeOut"
          }}
          whileHover={{
            scale: 1.05,
            transition: { duration: 0.2 }
          }}
          onClick={handleOpen}
          className="fixed bottom-6 right-6 z-50 rounded-full bg-primary/90 backdrop-blur-sm shadow-lg p-3 text-primary-foreground hover:bg-primary transition-all duration-300"
          aria-label="Open welcome video"
        >
          <Video className="h-5 w-5" />
        </motion.button>
      </AnimatePresence>
    </>
  );
};

export default WelcomeVideoDialog;
