# Activity Logging Integration Guide

This guide explains how to integrate activity logging into existing services without modifying the existing modules directly.

## Overview

The activity logging system provides a comprehensive audit trail for organization-wide activities. It tracks user actions, system events, and important business operations across the application.

## Quick Start

### 1. Import the ActivityLoggerService

In any service where you want to log activities, import the `ActivityLoggerService`:

```typescript
import { ActivityLoggerService } from 'src/activities/activity-logger.service';

@Injectable()
export class YourService {
  constructor(
    private activityLogger: ActivityLoggerService,
    // ... other dependencies
  ) {}
}
```

### 2. Add ActivitiesModule to Your Module

```typescript
import { ActivitiesModule } from 'src/activities/activities.module';

@Module({
  imports: [
    // ... other imports
    ActivitiesModule,
  ],
  // ... rest of module configuration
})
export class YourModule {}
```

### 3. Log Activities

```typescript
// Example: Log when a user joins an organization
await this.activityLogger.logUserJoinedOrganization(
  organizationId,
  userId,
  userName
);

// Example: Log when a team is created
await this.activityLogger.logTeamCreated(
  organizationId,
  userId,
  userName,
  teamName,
  teamId
);
```

## Available Activity Types

### User Management
- `logUserJoinedOrganization(orgId, userId, userName, metadata?)`
- `logUserLeftOrganization(orgId, userId, userName, metadata?)`
- `logUserRoleChanged(orgId, targetUserId, targetUserName, previousRole, newRole, changedByUserId?, changedByUserName?)`

### Team Management
- `logTeamCreated(orgId, userId, userName, teamName, teamId)`
- `logTeamDeleted(orgId, userId, userName, teamName, teamId)`
- `logTeamMemberAdded(orgId, addedByUserId, addedByUserName, memberName, memberId, teamName, teamId)`
- `logTeamMemberRemoved(orgId, removedByUserId, removedByUserName, memberName, memberId, teamName, teamId)`
- `logTeamAdminAssigned(orgId, assignedByUserId, assignedByUserName, adminName, adminId, teamName, teamId)`

### Integrations
- `logConnectorConnected(orgId, userId, userName, connectorName, metadata?)`
- `logConnectorDisconnected(orgId, userId, userName, connectorName, metadata?)`

### File Operations
- `logFileUploaded(orgId, userId, userName, fileName, fileId, metadata?)`
- `logKnowledgeBaseCreated(orgId, userId, userName, kbName, kbId)`
- `logKnowledgeBaseShared(orgId, userId, userName, kbName, kbId, sharedWithEmails)`

### Onboarding
- `logOnboardingStepCompleted(orgId, userId, userName, step, metadata?)`
- `logOnboardingCompleted(orgId, userId, userName)`
- `logCalendarConnected(orgId, userId, userName, calendarProvider?)`
- `logChatCreated(orgId, userId, userName, chatId?)`

### Generic Logging
- `logActivity(orgId, userId, activityType, description, metadata?, ipAddress?, userAgent?)`

## Integration Examples

### Example 1: User Service Integration

```typescript
// In user.service.ts
import { ActivityLoggerService } from 'src/activities/activity-logger.service';

@Injectable()
export class UserService {
  constructor(
    private prisma: PrismaService,
    private activityLogger: ActivityLoggerService,
  ) {}

  async updateUserRole(userId: string, newRole: string, changedByUserId: string) {
    // Get user details
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    const changedByUser = await this.prisma.user.findUnique({ where: { id: changedByUserId } });
    
    const previousRole = user.role;
    
    // Update the role
    await this.prisma.user.update({
      where: { id: userId },
      data: { role: newRole },
    });

    // Log the activity
    await this.activityLogger.logUserRoleChanged(
      user.organizationId,
      userId,
      user.name,
      previousRole,
      newRole,
      changedByUserId,
      changedByUser.name
    );
  }
}
```

### Example 2: Team Service Integration

```typescript
// In team.service.ts
import { ActivityLoggerService } from 'src/activities/activity-logger.service';

@Injectable()
export class TeamService {
  constructor(
    private prisma: PrismaService,
    private activityLogger: ActivityLoggerService,
  ) {}

  async createTeam(createTeamDto: CreateTeamDto, creatorId: string) {
    const creator = await this.prisma.user.findUnique({ where: { id: creatorId } });
    
    const team = await this.prisma.team.create({
      data: {
        ...createTeamDto,
        organizationId: creator.organizationId,
      },
    });

    // Log the activity
    await this.activityLogger.logTeamCreated(
      creator.organizationId,
      creatorId,
      creator.name,
      team.name,
      team.id
    );

    return team;
  }
}
```

### Example 3: Connector Service Integration

```typescript
// In connector.service.ts
import { ActivityLoggerService } from 'src/activities/activity-logger.service';

@Injectable()
export class ConnectorService {
  constructor(
    private dynamoDBService: DynamoDBService,
    private activityLogger: ActivityLoggerService,
  ) {}

  async connectGoogleDrive(userId: string, credentials: any) {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    
    // Save connector credentials
    await this.dynamoDBService.put('UserConnectors', {
      user_id: userId,
      name: 'Google Drive',
      credentials,
    });

    // Log the activity
    await this.activityLogger.logConnectorConnected(
      user.organizationId,
      userId,
      user.name,
      'Google Drive',
      { connectorType: 'google-drive' }
    );
  }
}
```

### Example 4: Onboarding Service Integration

```typescript
// In onboarding.service.ts
import { ActivityLoggerService } from 'src/activities/activity-logger.service';

@Injectable()
export class OnboardingService {
  constructor(
    private prisma: PrismaService,
    private activityLogger: ActivityLoggerService,
  ) {}

  async completeOnboardingStep(userId: string, step: string) {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    
    // Update onboarding status
    await this.prisma.onboardingStatus.update({
      where: { userId },
      data: { [step]: true },
    });

    // Log the step completion
    await this.activityLogger.logOnboardingStepCompleted(
      user.organizationId,
      userId,
      user.name,
      step
    );

    // Check if all steps are complete
    const status = await this.prisma.onboardingStatus.findUnique({ where: { userId } });
    if (status.hasCreatedChat && status.hasConnectedCalendar && status.hasCompletedOnboarding) {
      await this.activityLogger.logOnboardingCompleted(
        user.organizationId,
        userId,
        user.name
      );
    }
  }
}
```

## Best Practices

### 1. Error Handling
Activity logging is designed to be non-blocking. If logging fails, it won't affect the main operation:

```typescript
// The activity logger handles errors internally
await this.activityLogger.logUserJoinedOrganization(orgId, userId, userName);
// This will continue even if logging fails
```

### 2. Metadata Usage
Use metadata to store additional context:

```typescript
await this.activityLogger.logConnectorConnected(
  organizationId,
  userId,
  userName,
  'Google Drive',
  {
    connectorType: 'google-drive',
    permissions: ['read', 'write'],
    folderCount: 150,
  }
);
```

### 3. IP Address and User Agent
For web requests, include IP address and user agent:

```typescript
// In a controller
async someAction(@Req() req: Request) {
  // ... perform action
  
  await this.activityLogger.logActivity(
    organizationId,
    userId,
    ActivityType.SOME_ACTION,
    'User performed some action',
    metadata,
    req.ip,
    req.headers['user-agent']
  );
}
```

### 4. Batch Operations
For bulk operations, log a summary rather than individual items:

```typescript
// Instead of logging each user addition
await this.activityLogger.logActivity(
  organizationId,
  adminUserId,
  ActivityType.TEAM_MEMBER_ADDED,
  `${adminName} added ${userCount} members to team "${teamName}"`,
  { teamId, addedUserIds, userCount }
);
```

## API Endpoints

### Get Activities
```
GET /v1/activities/:organizationId
```

Query Parameters:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `activityType` (optional): Filter by activity type
- `userId` (optional): Filter by user who performed the action
- `fromDate` (optional): Filter from date (ISO string)
- `toDate` (optional): Filter to date (ISO string)
- `search` (optional): Search in descriptions

### Get Activity Statistics
```
GET /v1/activities/:organizationId/stats
```

Returns activity statistics and trends for the organization.

## Security

- Users can only view activities for organizations they belong to
- All endpoints require JWT authentication
- Activity logs are immutable (no update/delete operations)
- Sensitive data should not be stored in activity descriptions

## Performance Considerations

- Activity logging is asynchronous and non-blocking
- Database indexes are optimized for common query patterns
- Consider implementing activity log archiving for long-term storage
- Use pagination for large result sets

## Troubleshooting

### Common Issues

1. **Activity not appearing**: Check that the user belongs to the organization
2. **Permission denied**: Ensure the requesting user is in the same organization
3. **Missing activities**: Verify that the service is properly importing and using ActivityLoggerService

### Debugging

Enable debug logging to see activity creation:

```typescript
// Add this to see what activities are being created
console.log('Logging activity:', { organizationId, userId, activityType, description });
await this.activityLogger.logActivity(...);
```
