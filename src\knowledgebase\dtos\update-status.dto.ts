import { IsS<PERSON>, <PERSON><PERSON>ptional, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Status } from '@prisma/client';

export class UpdateStatusDto {
    @ApiProperty({ description: 'ID of the knowledge base' })
    @IsString()
    id: string;

    @ApiProperty({
        description: 'Status of the knowledge base',
        required: true,
    })
    @IsEnum(Status)
    status: Status;
}
