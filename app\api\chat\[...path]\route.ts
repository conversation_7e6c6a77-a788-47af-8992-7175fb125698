import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function GET(req: NextRequest) {
    try {
        const token = await getToken({
            req: req as any,
            secret: process.env.NEXT_AUTH_SECRET,
            cookieName: "stepsai_secure",
        });


        if (!token) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const path = req.nextUrl.pathname.split('/').slice(3).join('/');
        const apiUrl = `${process.env.NEXT_PUBLIC_CHAT_API_URL}/${path}`;


        const externalRes = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token.accessToken}`,
            },
        });


        const data = await externalRes.json();
        return NextResponse.json(data, { status: externalRes.status });
    } catch (error) {
        console.error('Proxy error:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}

export async function POST(req: NextRequest) {
    try {
        const token = await getToken({
            req: req as any,
            secret: process.env.NEXT_AUTH_SECRET,
            cookieName: "stepsai_secure",
        });

        if (!token?.accessToken) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const path = req.nextUrl.pathname.split('/').slice(3).join('/');
        const apiUrl = `${process.env.NEXT_PUBLIC_CHAT_API_URL}/${path}`;
        const body = await req.json();

        const externalRes = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token.accessToken}`,
            },
            body: JSON.stringify(body)
        });

        const data = await externalRes.json();
        return NextResponse.json(data, { status: externalRes.status });
    } catch (error) {
        console.error('Proxy error:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}
