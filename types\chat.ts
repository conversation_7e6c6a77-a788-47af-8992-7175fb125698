export interface ChatSession {
    chat_id?: string;
    name?: string;
    starred?: boolean;
}

export interface ChatSessionResponse {
    chat_id: string;
    name?: string;
    starred?: boolean;
    user_id: string;
    last_message?: string;
    updated_at?: string;
    created_at?: string;
}

export interface CurrentChat {
    chat_id: string | null;
    name?: string;
    starred?: boolean;
    user_id: string;
    isNewChat?: boolean;
}

export interface ChatRequest {
    message: string;
    chat_id: string;
    allowed_kids?: string[];
    allowed_tools?: any[];
    allowed_data_sources?: string[];
    llm?: string;
}

export interface ChatResponse {
    response_str: string;
    sources?: Array<
        RetrievalToolPostProcessed |
        TableRetrievalToolPostProcessed |
        AnswerToolPostProcessed |
        GDriveToolPostProcessed |
        OneDriveToolPostProcessed |
        SlackToolPostProcessed |
        GmailToolPostProcessed |
        GitHubToolPostProcessed
    >;
}

interface RetrievalToolPostProcessed {
    tool_name: "Knowledge Base Documents";
    sources: Array<{
        type: "Knowledge Base Document";
        dataset_uid: string;
        content: string;
        score: number;
        name: string;
    }>;
    type: "Retrieval";
}

interface TableRetrievalToolPostProcessed {
    tool_name: "Knowledge Base Tables";
    sources: Array<{
        type: "Knowledge Base Table";
        score: number;
        dataset_uid: string;
        name: string;
        sheet_name?: string | null;
    }>;
    type: "Retrieval";
}

interface AnswerToolPostProcessed {
    tool_name: "Knowledge Base Tables";
    sources: Array<{
        type: "Knowledge Base Table";
        dataset_uid: string;
        name: string;
        sheet_name?: string | null;
    }>;
    type: "Analysis";
}

interface GDriveToolPostProcessed {
    tool_name: "Google Drive";
    sources: Array<{
        type: "GDrive";
        name: string;
        score: number;
        content: string;
        id: string;
        url: string;
    }>;
    type: "Retrieval";
}

interface OneDriveToolPostProcessed {
    tool_name: "OneDrive";
    sources: Array<{
        type: "OneDrive";
        name: string;
        score: number;
        content: string;
        id: string;
        url: string;
    }>;
    type: "Retrieval";
}

interface SlackToolPostProcessed {
    tool_name: "Slack";
    sources: Array<{
        type: "Slack";
        score: number;
        content: string;
        thread_created_at: string;
        channel_name: string;
        creator_username: string;
    }>;
    type: "Retrieval";
}

interface GmailToolPostProcessed {
    tool_name: "Gmail";
    sources: Array<{
        type: "Gmail";
        subject: string;
        from_email: string;
        date: string;
        body: string;
        score: number;
    }>;
    type: "Retrieval";
}

interface GitHubToolPostProcessed {
    tool_name: "GitHub";
    sources: Array<{
        type: "GitHub";
        score: number;
        content: string;
        created_at: string;
        repository: string;
        author: string;
        item_type: "Commit" | "PR";
        item_url: string;
    }>;
    type: "Retrieval";
}

export interface AnswerSourceTable {
    type: string;
    dataset_uid: string;
    name: string;
    sheet_name?: string | null;
}

export interface AssistantMessage {
    role: string;
    message: string;
    chat_id: string;
    timestamp: string;
    sources?: Array<object>;
    llm?: string;
}

export interface AssistantSource {
    source: object | object | object;
    timestamp: string;
}

export interface MessageSource {
    source: {
        tool_name: string;
        sources: {
            name: string;
            type: string;
            dataset_uid: string;
            content: string;
            score: number;
            subject?: string;
            from_email?: string;
            date?: string;
            url?: string;
            body?: string;

        }[];
        type: string;
    };
    timestamp: string;
}

export interface Thought {
    id: string;
    type: string;
    text: string;
    data: any[]
}

export interface Step {
    description: string;
    timestamp: string;
}

export interface Sources {
    id: string;
    name: string;
    icon?: string;
    logo?: string;
    type: 'connector' | 'knowledge_base';
    status: string;
    etlStatus?: string;
    connectorId?: string;
}

export interface Message {
    role: 'user' | 'assistant' | 'assistant_name';
    message: string;
    chat_id: string;
    loading?: boolean;
    timestamp: string;
    sources?: MessageSource[];
    llm?: string;
    thinking?: boolean;
    thoughts?: Thought[];
    formattedMessage?: string;
}

export interface UserSources {
    allowed_tools?: Array<any>;
    allowed_data_sources?: Array<string>;
    allowed_kids?: Array<string>;
}

export interface UserMessage {
    role: string;
    message: string;
    chat_id: string;
    timestamp: string;
    sources?: UserSources;
    user_id: string;
}

export interface ChatState {
    chats: ChatSession[];
    currentChat?: ChatSession;
    messages: { [key: string]: (UserMessage | AssistantMessage)[] };
    loading: boolean;
    error: string | null;
}

export interface StreamingResponse {
    message: string;
    chat_id: string;
}

export interface FileStatusResponse {
    kid: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress?: number;
    error?: string;
}

export interface ChatUpdateRequest {
    chat_id: string;
    name?: string;
    starred?: boolean;
}

type SourceType = "Knowledge Base Table" | "Knowledge Base Document" | "GDrive" | "OneDrive" | "Slack" | "Gmail";

interface Source {
    type: SourceType;
    name: string;
    content?: string;
    dataset_uid?: string;
    sheet_name?: string;
    id?: string;
    url?: string;
    thread_created_at?: string;
    channel_name?: string;
    creator_username?: string;
    subject?: string;
    from_email?: string;
    date?: string;
    body?: string;
    score?: number;
}