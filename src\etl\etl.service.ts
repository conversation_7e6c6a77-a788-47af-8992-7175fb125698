import { Inject, Injectable } from '@nestjs/common';
import {
  EtlProviderStrategy,
  KnowledgeBasePublishArgs,
} from './interface/etl.interface';


// Typescript enum for connector name
export enum ConnectorName {
  KNOWLEDGE_BASE = 'KN<PERSON>LEDGE_BASE',
  NOTION = 'NOTION',
  SLACK = 'SLACK',
  GOOGLE_CALENDAR = 'GOOGLE_CALENDAR',
  GOOGLE_DRIVE = 'GOOGLE_DRIVE',
  HUBSPOT = 'HUBSPOT',
  GITHUB = 'GITHUB',
  ONEDRIVE = 'ONEDRIVE',
}

@Injectable()
export class EtlService {
  constructor(
    @Inject('ETL_PROVIDER')
    private readonly etlProvider: EtlProviderStrategy,
  ) { }

  async publish(
    user_id: string,
    connector_name: ConnectorName,
    kid: string,
    connector_id: string,
    organization_id: string,
    knowledge_base_data?: KnowledgeBasePublishArgs,
  ) {
    return this.etlProvider.publish(
      user_id,
      connector_name,
      kid,
      connector_id,
      organization_id = "stepsai",
      knowledge_base_data,
    );
  }

  async getStatus(dagRunId: string) {
    return this.etlProvider.getStatus(dagRunId);
  }
}
