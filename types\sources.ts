export interface KnowledgeBase {
    id: string
    name: string
    description: string | null
    status: 'SUCCESS' | 'PROCESSING' | 'FAILED'
    type: 'knowledgebase'
}

export interface Connector {
    id: string
    name: string
    slug: string
    logo: string
    status: 'connected' | 'available'
    type: 'connector'
}

export type Source = KnowledgeBase | Connector

export interface SourcesResponse {
    status: boolean
    statusCode: number
    result: {
        knowledgebases: {
            Ready: KnowledgeBase[]
            Processing: KnowledgeBase[]
        }
        connectors: {
            Connected: Connector[]
            Available: Connector[]
        }
    }
    version: string
}

export function transformSources(response: SourcesResponse): Source[] {
    console.log(response)
    const sources: Source[] = [
        // Transform knowledgebases
        ...response.result.knowledgebases.Ready.map(kb => ({
            ...kb,
            type: 'knowledgebase' as const
        })),
        ...response.result.knowledgebases.Processing.map(kb => ({
            ...kb,
            type: 'knowledgebase' as const
        })),
        // Transform connectors
        ...response.result.connectors.Connected.map(conn => ({
            ...conn,
            status: 'connected' as const,
            type: 'connector' as const
        })),
        ...response.result.connectors.Available.map(conn => ({
            ...conn,
            status: 'available' as const,
            type: 'connector' as const
        }))
    ]

    return sources
}

// Helper function to filter sources
export function filterSources(sources: Source[], searchTerm: string): Source[] {
    const lowercaseSearch = searchTerm.toLowerCase()
    return sources.filter(source => {
        if (source.type === 'knowledgebase') {
            return (
                source.name.toLowerCase().includes(lowercaseSearch) ||
                (source.description?.toLowerCase().includes(lowercaseSearch))
            )
        } else {
            return source.name.toLowerCase().includes(lowercaseSearch)
        }
    })
}

// Helper function to group sources by type and status
export function groupSources(sources: Source[]) {
    return {
        knowledgebases: sources.filter((source): source is KnowledgeBase =>
            source.type === 'knowledgebase'
        ),
        connectors: {
            connected: sources.filter((source): source is Connector =>
                source.type === 'connector' && source.status === 'connected'
            ),
            available: sources.filter((source): source is Connector =>
                source.type === 'connector' && source.status === 'available'
            )
        }
    }
} 