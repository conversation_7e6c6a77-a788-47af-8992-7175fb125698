import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { ConnectorsService } from '../../v1/connectors.v1.service';
import { BasicAuthConnectorService } from '../../interfaces/base.interface';
import { CrawlZendeskDto } from './dtos/zendesk.dto';
import { EtlStatus } from '@prisma/client';
import { EtlService } from '@/etl/etl.service';

@Injectable()
export class ZendeskService extends BasicAuthConnectorService {
  constructor(
    private prisma: PrismaService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
    private etlService: EtlService,
  ) {
    super();
  }
  async connect(
    userId: string,
    token: string,
    email: string,
    subdomain: string,
  ) {
    try {
      if (!token || !email || !subdomain) {
        throw new BadRequestException('Empty credentials');
      }
      const response = await fetch(
        `https://${subdomain}.zendesk.com/api/v2/users/me.json`,
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${Buffer.from(`${email}/token:${token}`).toString('base64')}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new BadRequestException('Invalid credentials');
      }
      const userData = await response.json();
      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Zendesk',
        },
      });

      await this.prisma.userConnectors.create({
        data: {
          userId: userId,
          connectorId: connector.id,
          config: {
            name: userData.user.name,
            email: userData.user.email,
            image: userData.user.content_url,
            metadata: JSON.stringify(userData.user),
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(token);

      await this.dynamoDBService.put(this.dynamoDBTableName, {
        user_id: userId,
        name: 'Zendesk',
        connector_id: connector.id,
        connector_type: connector.type,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          email: email,
          subdomain: subdomain,
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
        },
      });

      return {
        success: true,
        message: 'Zendesk connected successfully',
        data: {
          name: userData.user.name,
          email: userData.user.email,
          image: userData.user.content_url,
          metadata: JSON.stringify(userData.user),
        },
      };
    } catch (e) {
      throw new BadRequestException(
        `Failed to connect to Zendesk: ${e.message}`,
      );
    }
  }

  async crawlZendesk(userId: string, data: CrawlZendeskDto) {
    const { userConnectorId } = data;
    const etlRun = await this.prisma.etl.findFirst({
      where: {
        userConnectorId: userConnectorId,
      },
    });

    if (etlRun && etlRun.status !== EtlStatus.FAILED) {
      throw new BadRequestException('ETL in progress!');
    }

    if (etlRun && etlRun.status === EtlStatus.FAILED) {
      await this.prisma.etl.delete({
        where: {
          id: etlRun.id,
        },
      });
    }

    // const etlResponse = await this.etlService.publish(
    //   userId,
    //   ConnectorName.ZENDESK,
    //   userConnectorId,
    // );

    // await this.prisma.etl.create({
    //   data: {
    //     dagId: etlResponse.dagId,
    //     dagRunId: etlResponse.dagRunId,
    //     status: etlResponse.state,
    //     startDate: etlResponse.executionDate,
    //     userConnectorId: userConnectorId,
    //   },
    // });

    return {
      success: true,
      message: 'Zendesk crawl started successfully',
    };
  }

  async disconnect(userId: string) {
    try {
      const userConnector = await this.dynamoDBService.get(
        this.dynamoDBTableName,
        {
          user_id: userId,
          name: 'Zendesk',
        },
      );

      if (!userConnector) {
        throw new NotFoundException('Zendesk connector not found');
      }

      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'Zendesk',
      });

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Zendesk',
        },
        select: {
          id: true,
        },
      });

      if (connector) {
        await this.prisma.userConnectors.deleteMany({
          where: {
            userId,
            connectorId: connector.id,
          },
        });
      }
      return {
        success: true,
        message: 'Zendesk integration disconnected successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to disconnect Zendesk: ${error.message}`,
      );
    }
  }
}
