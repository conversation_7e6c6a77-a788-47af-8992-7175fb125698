import "server-only";
import Redis from "ioredis";

class RedisConnection {
    private static instance: Redis | null = null;

    public static getInstance(): Redis {
        if (!RedisConnection.instance) {
            RedisConnection.instance = new Redis({
                username: process.env.REDIS_USERNAME,
                password: process.env.REDIS_PASSWORD,
                host: process.env.REDIS_HOST,
                port: parseInt(process.env.REDIS_PORT || '19828')
            });
        }
        return RedisConnection.instance;
    }
}

export const redis = RedisConnection.getInstance();