import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  HttpCode,
  UseGuards,
  HttpStatus,
  ForbiddenException,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { IntegrationsService } from './integrations.v1.service';
import { CreateIntegrationDto } from '../dtos/createIntegration.dto';
import { SuperAdminAuthGuard } from '../../auth/guards/superadmin-auth.guard';
import { PrismaService } from '../../../src/prisma/prisma.service';

@ApiTags('Integrations-V1')
@Controller({ version: '1', path: 'integrations' })
export class IntegrationsController {
  constructor(
    private integrationsService: IntegrationsService,
    private prismaService: PrismaService,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all integrations' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Integrations fetched successfully',
  })
  @HttpCode(HttpStatus.OK)
  async getIntegrations(@GetCurrentUser() user: JwtPayload) {
    return this.integrationsService.getIntegrations(user.sub);
  }

  @Get(':slug')
  @ApiOperation({ summary: 'Get integration by slug' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Integration fetched successfully',
  })
  @HttpCode(HttpStatus.OK)
  async getIntegrationById(
    @GetCurrentUser() user: JwtPayload,
    @Param('slug') integrationslug: string,
  ) {
    return this.integrationsService.integrationByslug(
      user.sub,
      integrationslug,
    );
  }

  @Post()
  @UseGuards(SuperAdminAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create integrations' })
  @HttpCode(HttpStatus.OK)
  async createIntegrations(
    // @GetCurrentUser() user: JwtPayload,
    @Body() data: CreateIntegrationDto,
  ) {
    // Check if user is admin
    // const dbUser = await this.prismaService.user.findUnique({
    //     where: { id: user.sub },
    //     select: { role: true }
    // });

    // if (!dbUser || dbUser.role !== 'ADMIN') {
    //     throw new ForbiddenException('Admin access required');
    // }

    return this.integrationsService.createIntegrations(data);
  }
}
