import { create } from 'zustand';
import { ChatSession, ChatSessionResponse, CurrentChat, Message, Sources } from '@/types/chat';

interface ChatState {
  chats: ChatSessionResponse[];
  currentChat: CurrentChat;
  messages: Message[];
  loading: boolean;
  error: string | null;
  webSearch: boolean;
  auto: boolean;
  input: string;
  sourcesViewId: string;
  isSourcesDrawerOpen: boolean;
  savedInput: string | null;
  savedSources: Map<string, Sources>;
  selectedSources: Map<string, Sources>;
  addChat: (chat: ChatSessionResponse) => void;
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updatedMessage: Partial<Message>) => void;
  startLoading: () => void;
  setInput: (input: string) => void;
  setCurrentChat: (chat: CurrentChat) => void;
  setMessages: (messages: Message[]) => void;
  setLoading: (loading: boolean) => void;
  stopLoading: () => void;
  setSourcesViewId: (sourcesViewId: string) => void;
  setSavedSources: (sources: Map<string, Sources>) => void;
  setWebSearch: (webSearch: boolean) => void;
  setSavedInput: (input: string) => void;
  setError: (error: string | null) => void;
  clearMessages: () => void;
  clearCurrentChat: () => void;
  setSelectedSources: (sources: Map<string, Sources>) => void;
  setAuto: (auto: boolean) => void;
  addCurrentChat: (chat: CurrentChat) => void;
  advanceSearch: boolean;
  setAdvanceSearch: (advanceSearch: boolean) => void;
  setIsSourcesDrawerOpen: (isSourcesDrawerOpen: boolean) => void;
}

const useChatStore = create<ChatState>((set) => ({
  chats: [],
  currentChat: {
    chat_id: null,
    name: "",
    user_id: "",
    isNewChat: false,
    loading: false
  },
  sourcesViewId: "",
  isSourcesDrawerOpen: false,
  auto: true,
  webSearch: false,
  selectedSources: new Map(),
  messages: [],
  loading: false,
  savedSources: new Map(),
  error: null,
  input: "",
  savedInput: null,
  advanceSearch: false,
  addChat: (chat: ChatSessionResponse) =>
    set((state) => ({
      chats: [...state.chats, chat],
      currentChat: { ...chat, loading: false },
      messages: { ...state.messages, [chat.chat_id]: [] }

    })),

  addMessage: (message: Message) =>
    set((state) => ({
      messages: [...state.messages, message]
    })),

  updateMessage: (messageId: string, updatedMessage: Partial<Message>) =>
    set((state) => ({
      messages: state.messages.map(msg =>
        msg.timestamp === messageId
          ? { ...msg, ...updatedMessage }
          : msg
      )
    })),

  addCurrentChat: (chat: CurrentChat) =>
    set((state) => ({
      currentChat: chat
    })),

  setInput: (input: string) =>
    set((state) => ({
      input: input
    })),

  setSourcesViewId: (sourcesViewId: string) =>
    set((state) => ({
      sourcesViewId: sourcesViewId
    })),

  setCurrentChat: (chat: CurrentChat) =>
    set((state) => ({
      currentChat: chat
    })),

  setIsSourcesDrawerOpen: (isSourcesDrawerOpen: boolean) =>
    set((state) => ({
      isSourcesDrawerOpen: isSourcesDrawerOpen
    })),

  setMessages: (messages: Message[]) =>
    set((state) => ({
      messages: messages
    })),

  setSelectedSources: (sources: Map<string, Sources>) =>
    set((state) => ({
      selectedSources: sources
    })),

  setWebSearch: (webSearch: boolean) =>
    set((state) => ({
      webSearch: webSearch
    })),

  clearMessages: () => set((state) => ({ messages: [] })),
  clearCurrentChat: () => set((state) => ({ currentChat: { chat_id: null, name: "", user_id: "", loading: false }, messages: [], selectedSources: new Map() })),
  startLoading: () => set({ loading: true }),
  stopLoading: () => set({ loading: false }),
  setError: (error: string | null) => set({ error }),
  setLoading: (loading: boolean) => set({ loading }),
  setSavedInput: (input: string) => set({ savedInput: input }),
  setAdvanceSearch: (advanceSearch: boolean) =>
    set((state) => ({
      advanceSearch: advanceSearch
    })),
  setSavedSources: (sources: Map<string, Sources>) =>
    set((state) => ({
      savedSources: sources
    })),
  setAuto: (auto: boolean) =>
    set((state) => ({
      auto: auto,
      selectedSources: auto ? state.savedSources : new Map()
    }))
}));

export default useChatStore;
