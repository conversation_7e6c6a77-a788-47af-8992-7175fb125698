"use client";

import { cn } from "@/lib/utils";
import { ArrowUp, Database, MessageCirclePlus, Square, X, Sparkles, HandCoins, Settings, Brain, Users, FileText, Boxes, UploadCloud } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useRef, useState, useEffect } from "react";
import Textarea from "react-textarea-autosize";
import { AnimatePresence, motion } from "framer-motion";
import { WebSearchToggle } from "./WebSearch";
import { But<PERSON>, Card, Separator } from "@steps-ai/ui";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { apiClient } from "@/lib/aiClient";
import { Sources } from "./Sources";
import useChatStore from "@/store/chatStore";
import { v4 as uuidv4 } from "uuid";
import { Toggle } from '../ui/toggle'
import { getQuota, increment } from "@/lib/Cache";
import { assets } from "@/lib/assets";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@steps-ai/ui";
import { Message } from "@/types/chat";
import { useUser } from "@/contexts/UserContext";
import { ShineBorder } from "../ui/shine-border";
import Image from "next/image";
import axios from "axios";
import { useSourcesDrawer } from "@/store/sourcesDrawerStore";

interface SuggestionBadge {
    id: string
    text: string
    icon: React.ReactNode
    color: string
    action?: (e: React.MouseEvent<HTMLButtonElement>) => void
}
const agents = {
    "Gmail": "gmail",
    "Knowledge Base": "kb",
    "Web Search": "web_search",
    "Google Calendar": "calendar",
    "DataOps Agent": "dataops",
    "Google Drive": "gdrive",
}

// Helper function to format UTC time as 'YYYY-MM-DD HH:mm:ss.SSSSSS+00:00'
function getFormattedUTCTimestamp() {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hours = String(now.getUTCHours()).padStart(2, '0');
    const minutes = String(now.getUTCMinutes()).padStart(2, '0');
    const seconds = String(now.getUTCSeconds()).padStart(2, '0');
    const milliseconds = String(now.getUTCMilliseconds()).padStart(3, '0');
    // Pad to microseconds (6 digits)
    const microseconds = milliseconds + '000';
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${microseconds}+00:00`;
}

export function ChatPanel() {
    const [input, setInput] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const { user } = useUser();
    const {
        clearMessages,
        currentChat,
        addMessage,
        messages,
        updateMessage,
        clearCurrentChat,
        savedInput,
        setSavedInput,
        selectedSources,
        webSearch,
        advanceSearch,
        addCurrentChat,
        savedSources,
        setIsSourcesDrawerOpen,
        setSavedSources,
        auto,
        setAuto,
        setCurrentChat
    } = useChatStore();
    const abortControllerRef = useRef<AbortController | null>(null);
    const router = useRouter();
    const [isFileUploading, setIsFileUploading] = useState(false);
    const [uploadedFile, setUploadedFile] = useState<File | null>(null);
    const [uploadStatus, setUploadStatus] = useState<string>("");
    const [uploadProgress, setUploadProgress] = useState(0);
    const [uploadedFileLink, setUploadedFileLink] = useState<string>("");
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    const suggestions: SuggestionBadge[] = [
        {
            id: "emails",
            text: "Latest emails",
            icon: <Image src={assets.Icons.Gmail} alt="Gmail" width={20} height={20} />,
            color: "text-blue-600 bg-blue-50 hover:bg-blue-100",
            action: (e: React.MouseEvent<HTMLButtonElement>) => {
                const value = "Get my latest emails";
                setInput(value);
                submitInput(value);
            }
        },
        {
            id: "find",
            text: "Search for documents",
            icon: <FileText className="w-4 h-4" />,
            color: "text-purple-600 bg-purple-50 hover:bg-purple-100",
            action: (e: React.MouseEvent<HTMLButtonElement>) => {
                const value = "Search for documents with ";
                setInput(value);
                // Optionally call submitInput(value) if you want to auto-submit
            }
        },
        {
            id: "search",
            text: "My today's meetings",
            icon: <Image src={assets.Icons["Google Calendar"]} alt="Google Calendar" width={20} height={20} />,
            color: "text-emerald-600 bg-emerald-50 hover:bg-emerald-100",
            action: (e: React.MouseEvent<HTMLButtonElement>) => {
                const value = "My today's meetings";
                setInput(value);
                submitInput(value);
            }
        },
        {
            id: "contacts",
            text: "Find contacts",
            icon: <Image src={assets.Icons.Hubspot} alt="Hubspot" width={20} height={20} />,
            color: "text-orange-600 bg-orange-50 hover:bg-orange-100",
            action: (e: React.MouseEvent<HTMLButtonElement>) => {
                const value = "Find contacts related to ";
                setInput(value);
                // Optionally call submitInput(value) if you want to auto-submit
            }
        },
        {
            id: "analyze",
            text: "Analyze data",
            icon: <Brain className="w-3 h-3" />,
            color: "text-indigo-600 bg-indigo-50 hover:bg-indigo-100",
            action: (e: React.MouseEvent<HTMLButtonElement>) => {
                const value = "Analyze data regarding ";
                setInput(value);
                // Optionally call submitInput(value) if you want to auto-submit
            }
        },
        {
            id: "knowledgebase",
            text: "Knowledge Base",
            icon: <Database className="w-3 h-3" />,
            color: "text-green-600 bg-green-50 hover:bg-green-100",
            action: (e: React.MouseEvent<HTMLButtonElement>) => {
                router.push("/knowledgebase");
            }
        },
        {
            id: "connector",
            text: "Connectors",
            icon: <Boxes className="w-3 h-3" />,
            color: "text-green-600 bg-green-50 hover:bg-green-100",
            action: (e: React.MouseEvent<HTMLButtonElement>) => {
                router.push("/connectors");
            }
        }
    ]

    useEffect(() => {
        if (currentChat && currentChat.chat_id && savedInput) {
            sendAPIMessage();
            setSavedInput("");
        }
    }, [currentChat, savedInput]);

    const inputRef = useRef<HTMLTextAreaElement>(null);

    const handleNewChat = () => {
        clearCurrentChat();
        clearMessages();
        router.push("/chat");
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(e.target.value);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === "Enter" && !e.shiftKey && !e.ctrlKey && !e.altKey) {
            e.preventDefault();
            if (input.trim().length > 0) {
                handleSubmit(e as unknown as React.FormEvent<HTMLFormElement>);
            }
        }
    };

    // New function for main submit logic, now accepts a value
    const submitInput = async (value: string) => {
        if (value.trim().length === 0) {
            return;
        }
        setInput("");

        try {
            if (!user?.email) {
                return;
            }

            if (!currentChat.chat_id) {
                setSavedInput(value);
                setInput(value);
                setSavedSources(selectedSources);
                const chatId = uuidv4();
                const userMessage: Message = {
                    role: "user",
                    message: value,
                    chat_id: chatId,
                    timestamp: new Date().toISOString(),
                }
                addMessage(userMessage);
                router.push(`/chat/${chatId}?new=true`);
            } else {
                if (messages.find((message) => message.timestamp === new Date().toISOString())) {
                    return;
                }

                const userMessage: Message = {
                    role: "user",
                    message: value,
                    chat_id: currentChat.chat_id || "",
                    timestamp: new Date().toISOString(),
                };

                addMessage(userMessage);
                await sendAPIMessage();
            }

        } catch (error) {
            console.error("Error in message submission:", error);
            alert("There was an error sending your message. Please try again.");
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        await submitInput(input);
    };

    async function sendAPIMessage() {
        const maxRetries = 3;
        let currentRetry = 0;

        while (currentRetry < maxRetries) {
            try {
                abortControllerRef.current = new AbortController();
                const message = savedInput ? savedInput + " " + input : input;
                const messageId = uuidv4();

                const assistantMessage: Message = {
                    role: "assistant",
                    message: "",
                    chat_id: currentChat.chat_id || "",
                    timestamp: messageId,
                    loading: true,
                    thinking: true,
                    thoughts: [{
                        id: uuidv4(),
                        type: "thinking",
                        text: "AIDE is thinking about the question...",
                        data: []
                    }],
                };

                addMessage(assistantMessage);

                const initialSources = Array.from(selectedSources.values()).map(source => ({
                    name: source.name,
                    type: source.type,
                    id: source.id,
                    etlStatus: source?.etlStatus,
                    connectorId: source?.connectorId
                }));
                const kids = initialSources.filter((source: any) => source.type === "knowledge_base").map((source: any) => source.id);
                let data_sources: any = initialSources
                    .filter((source: any) => source.type === "connector")
                    .map((source: any) => agents[source.name as keyof typeof agents]);
                const etl_ids = initialSources.filter((source: any) => source.etlStatus !== "none").map((source: any) => source.connectorId);
                let finalKids = [...kids, ...etl_ids];

                finalKids = finalKids.filter((kid: any) => kid != null);

                if (finalKids.length >= 1) {
                    data_sources = [...data_sources, "kb"];
                }

                if (webSearch) {
                    data_sources = [...data_sources, "web_search"];
                }

                const reqBody = {
                    message: message,
                    chatId: currentChat.chat_id || "",
                    orgId: "stepsai",
                    kids: finalKids,
                    agents: data_sources,
                    userId: user?.id,
                    isNewChat: false,
                    time: getFormattedUTCTimestamp(),
                    user_name: user?.name,
                    file_link: isDataOpsSelected && uploadedFileLink ? uploadedFileLink : undefined
                }
                if (currentChat.isNewChat) {
                    reqBody.isNewChat = true;
                    currentChat.isNewChat = false;
                }

                console.log("reqBody", reqBody);

                const response = await fetch(`${process.env.NEXT_PUBLIC_CHAT_API_URL}/v1/chat-completion/stream`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(reqBody),
                    signal: abortControllerRef.current.signal,
                });


                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body?.getReader();
                if (!reader) {
                    throw new Error("No response body reader available");
                }

                const decoder = new TextDecoder();
                let buffer = "";
                let fullMessage = "";

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const eventRegex = /event: ([^\n]+)\ndata: ([^\n]+)\n\n/g;
                    let match;
                    let processedUpTo = 0;

                    while ((match = eventRegex.exec(buffer)) !== null) {
                        const eventType = match[1];
                        const eventData = match[2];
                        processedUpTo = match.index + match[0].length;
                        updateMessage(messageId, {
                            loading: false,
                        });

                        if (eventType === "chat_message") {

                            fullMessage += eventData;
                            const unescaped = fullMessage.replace(/\\n/g, "\n");
                            const formattedMessage = unescaped.replace(/\n/g, "  \n");
                            updateMessage(messageId, {
                                loading: false,
                                message: fullMessage,
                                formattedMessage: formattedMessage,
                            });
                        } else if (eventType === "source") {
                            try {
                                const sourcesData = JSON.parse(eventData);
                                console.log("sourcesData", sourcesData);
                                const currentMsg = useChatStore.getState().messages.find(msg => msg.timestamp === messageId);
                                const currentThoughts = currentMsg?.thoughts || [];

                                updateMessage(messageId, {
                                    message: fullMessage,
                                    sources: sourcesData,
                                    thoughts: [...currentThoughts, {
                                        id: uuidv4(),
                                        type: "DONE",
                                        text: "AIDE has finished thinking",
                                        data: []
                                    }]
                                });
                                const { setCurrentMessage, setRawSources } = useSourcesDrawer.getState();
                                if (currentMsg) {
                                    setCurrentMessage({
                                        ...currentMsg,
                                        sources: sourcesData 
                                    });
                                    setRawSources(sourcesData);
                                }
                            } catch (e) {
                                console.error("Error parsing sources:", e);
                            }
                        }
                        else if (eventType === "tool_message") {
                            const payload = JSON.parse(eventData);
                            const thoughts = {
                                type: payload.tool,
                                text: payload.text,
                                data: payload.data
                            }

                            const currentMsg = useChatStore.getState().messages.find(msg => msg.timestamp === messageId);
                            const currentThoughts = currentMsg?.thoughts || [];

                            updateMessage(messageId, {
                                thoughts: [...currentThoughts, { ...thoughts, id: uuidv4() }],
                            });


                        } else if (eventType === "chat_title") {
                            console.log("eventData", eventData);
                            setCurrentChat({ ...currentChat, name: eventData });
                        }
                        else {
                            console.log("eventType", eventType);
                        }
                    }

                    if (processedUpTo > 0) {
                        buffer = buffer.slice(processedUpTo);
                    }
                }

                break;
            } catch (error) {
                currentRetry++;
                console.log(`API call attempt ${currentRetry} failed:`, error);

                if (currentRetry === maxRetries) {
                    throw new Error("Failed to send message after multiple attempts");
                }

                await new Promise(resolve => setTimeout(resolve, Math.pow(2, currentRetry) * 1000));
            }
        }

        try {
            if (user?.email) {
                // await increment(user.email);
            }
        } catch (error) {
            console.error("Failed to increment quota:", error);
        } finally {
            setIsLoading(false);
        }
    }

    const stop = () => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
            abortControllerRef.current = null;
        }
        setIsLoading(false);
    };

    // Dummy file upload function
    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0] && user?.id) {
            const file = e.target.files[0];
            // 10MB limit
            const maxSize = 10 * 1024 * 1024;
            // Only allow CSV
            const isCSV = file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv');
            if (file.size > maxSize) {
                setUploadStatus("File too large. Max 10MB allowed.");
                setUploadedFile(null);
                setUploadProgress(0);
                setUploadedFileLink("");
                if (fileInputRef.current) fileInputRef.current.value = "";
                return;
            }
            if (!isCSV) {
                setUploadStatus("Only CSV files are allowed.");
                setUploadedFile(null);
                setUploadProgress(0);
                setUploadedFileLink("");
                if (fileInputRef.current) fileInputRef.current.value = "";
                return;
            }
            setIsFileUploading(true);
            setUploadedFile(file);
            setUploadStatus("Uploading...");
            setUploadProgress(0);
            setUploadedFileLink("");
            const formData = new FormData();
            formData.append('files', file);
            formData.append('folderName', `user/${user.id}`);
            try {
                const response = await axios.post(process.env.NEXT_PUBLIC_UPLOAD_URL as string, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                    onUploadProgress: (progressEvent) => {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
                        setUploadProgress(percentCompleted);
                    }
                });
                if (response.data.status && response.data.result && response.data.result.uploadResults && response.data.result.uploadResults[0]?.url) {
                    setUploadStatus("Uploaded");
                    setUploadedFileLink(response.data.result.uploadResults[0].url);
                } else {
                    setUploadStatus("Failed");
                }
            } catch (err: any) {
                setUploadStatus("Failed");
            } finally {
                setIsFileUploading(false);
            }
        }
    };

    // Check if DataOps Agent is selected
    const isDataOpsSelected = Array.from(selectedSources.values()).some((source: any) => source.id === 'dataops');

    const handleFileButtonClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.value = ""; // allow re-uploading same file
            fileInputRef.current.click();
        }
    };

    return (
        <motion.div
            className="w-full bg-background "
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
        >
            <form
                onSubmit={handleSubmit}
                className="max-w-4xl w-full mx-auto px-2 py-2"
            >

                <Card className=" dark:bg-[#14171F] bg-[#FAFAFA] dark:text-gray-400 text-black w-full rounded-3xl relative">
                    {(!currentChat || !currentChat.chat_id) && <ShineBorder shineColor={["#A07CFE", "#FE8FB5", "#FFBE7B"]} duration={50} />}
                    <div className={`relative flex flex-col w-full gap-2  rounded-3xl border-1 border-primary hover:border-primary transition-all shadow-sm dark:shadow-primary/5 ${currentChat && currentChat.chat_id ? "border-primary" : ""}`}>
                        <div className="flex flex-row items-center justify-between px-3 pt-3">
                            <div className="w-5 h-5 rounded-lg grid grid-cols-2 gap-0.5 bg-transparent">
                                {[...Array(4)].map((_, i) => (
                                    <div key={i} className="bg-blue-500 rounded-lg" />
                                ))}
                            </div>
                            <Textarea
                                ref={inputRef}
                                name="input"
                                itemType="submit"
                                disabled={isLoading}
                                rows={1}
                                maxRows={1}
                                tabIndex={0}
                                placeholder="Ask me anything..."
                                spellCheck={false}
                                value={input}
                                className="resize-none w-full min-h-12 bg-transparent border-0 px-4 pt-3 text-sm placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                onKeyDown={handleKeyDown}
                                onChange={handleInputChange}
                            />
                        </div>
                        {isDataOpsSelected && uploadedFile && (
                            <div className="px-3 pb-2 flex flex-col gap-1">
                                <div className="flex items-center gap-2 text-xs text-primary font-medium">
                                    File: {uploadedFile.name}
                                    <button
                                        type="button"
                                        className="ml-1 text-muted-foreground hover:text-destructive"
                                        onClick={() => {
                                            setUploadedFile(null);
                                            setUploadStatus("");
                                            setUploadProgress(0);
                                            setUploadedFileLink("");
                                            if (fileInputRef.current) fileInputRef.current.value = "";
                                        }}
                                        aria-label="Remove file"
                                    >
                                        <X className="w-3 h-3" />
                                    </button>
                                </div>
                                {uploadStatus && (
                                    <div className="text-xs text-muted-foreground">
                                        Status: {uploadStatus}
                                    </div>
                                )}
                                {isFileUploading && (
                                    <div className="text-xs text-muted-foreground">
                                        Progress: {uploadProgress}%
                                    </div>
                                )}
                            </div>
                        )}
                        <div className="flex items-center justify-between px-3 pb-2">
                            <div className="flex items-center gap-2">
                                <Toggle
                                    aria-label="Toggle advanced search mode"
                                    pressed={auto}
                                    onPressedChange={setAuto}
                                    variant="outline"
                                    className={cn(
                                        'gap-1 px-3 border rounded-md',
                                        auto ? [
                                            'bg-green-600',
                                            'text-white',
                                            'border-green-600',
                                        ] : [
                                            'border-input',
                                            'text-muted-foreground',
                                            'bg-background',
                                            'hover:bg-accent',
                                            'hover:text-accent-foreground'
                                        ]
                                    )}
                                >
                                    <Sparkles className="size-2 md:size-4" />
                                    <span className="text-xs hidden md:block">Auto</span>
                                </Toggle>
                                <Sources />
                                <WebSearchToggle />
                                {/* <AdvanceSearchToggle /> */}
                                {isDataOpsSelected ? (
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            className="rounded-full cursor-pointer"
                                            onClick={handleFileButtonClick}
                                            disabled={!!uploadedFile || isFileUploading}
                                        >
                                            <UploadCloud className="w-4 h-4" />
                                        </Button>
                                        <input
                                            type="file"
                                            id="file-upload"
                                            ref={fileInputRef}
                                            style={{ display: 'none' }}
                                            onChange={handleFileUpload}
                                            disabled={!!uploadedFile || isFileUploading}
                                        />
                                    </div>
                                ) : (
                                    selectedSources.size > 0 && (
                                        <AnimatePresence>
                                            <motion.div
                                                initial={{ opacity: 0, y: 10 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, y: -10 }}
                                                className="flex items-center gap-1.5"
                                            >
                                                <Separator orientation="vertical" className="h-4" />
                                                <div className="flex -space-x-2">
                                                    {Array.from(selectedSources.values())
                                                        .slice(0, 3)
                                                        .map((source, i) => (
                                                            <motion.div
                                                                key={source.id}
                                                                initial={{ opacity: 0, scale: 0.8 }}
                                                                animate={{ opacity: 1, scale: 1 }}
                                                                transition={{
                                                                    delay: i * 0.1,
                                                                    type: "spring",
                                                                    stiffness: 200,
                                                                    damping: 15,
                                                                }}
                                                                className="relative group"
                                                            >
                                                                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:scale-110 transition-transform duration-200 shadow-sm">
                                                                    {source.logo ? (
                                                                        <img
                                                                            src={source.logo}
                                                                            alt={source.name}
                                                                            className="w-3.5 h-3.5 object-contain rounded-sm"
                                                                        />
                                                                    ) : (
                                                                        <Database className="w-3 h-3 text-primary/80" />
                                                                    )}
                                                                </div>
                                                                <motion.div
                                                                    initial={{ opacity: 0, y: 5 }}
                                                                    whileHover={{ opacity: 1, y: 0 }}
                                                                    className="absolute -top-8 left-1/2 -translate-x-1/2 px-2 py-1 bg-black/80 text-xs text-white rounded-md whitespace-nowrap pointer-events-none"
                                                                >
                                                                    {source.name}
                                                                </motion.div>
                                                            </motion.div>
                                                        ))}

                                                    {selectedSources.size > 3 && (
                                                        <motion.div
                                                            initial={{ opacity: 0, scale: 0.8 }}
                                                            animate={{ opacity: 1, scale: 1 }}
                                                            transition={{
                                                                delay: 0.3,
                                                                type: "spring",
                                                                stiffness: 200,
                                                                damping: 15,
                                                            }}
                                                            className="relative group"
                                                        >
                                                            <div className="flex items-center justify-center min-w-[24px] h-6 px-2 rounded-full bg-primary/10 backdrop-blur-sm border border-primary/20 hover:bg-primary/15 transition-all duration-200 cursor-pointer">
                                                                <span className="text-xs text-primary font-medium">
                                                                    +{selectedSources.size - 3}
                                                                </span>
                                                            </div>
                                                            <motion.div
                                                                initial={{ opacity: 0, y: 5 }}
                                                                whileHover={{ opacity: 1, y: 0 }}
                                                                className="absolute -top-8 left-1/2 -translate-x-1/2 px-2 py-1 bg-black/80 text-xs text-white rounded-md whitespace-nowrap pointer-events-none"
                                                            >
                                                                {Array.from(selectedSources.values())
                                                                    .slice(3)
                                                                    .map((s) => s.name)
                                                                    .join(", ")}
                                                            </motion.div>
                                                        </motion.div>
                                                    )}
                                                </div>
                                            </motion.div>
                                        </AnimatePresence>
                                    )
                                )}
                            </div>
                            <div className="flex items-center gap-2">
                                {currentChat && currentChat.chat_id && <div className="relative group">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    onClick={handleNewChat}
                                                    className="shrink-0 rounded-full group"
                                                    type="button"
                                                    disabled={isLoading || !currentChat}
                                                >
                                                    <MessageCirclePlus className="size-4 group-hover:rotate-12 transition-all" />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>New Chat</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>}
                                <Button
                                    type={isLoading ? "button" : "submit"}
                                    size={"icon"}
                                    variant={"outline"}
                                    className={cn(isLoading && "animate-pulse", "rounded-full")}
                                    disabled={input.length === 0 && !isLoading}
                                    onClick={isLoading ? stop : undefined}
                                >
                                    {isLoading ? <Square size={20} /> : <ArrowUp size={20} />}
                                </Button>
                            </div>
                        </div>
                    </div>


                </Card>
                {/* {response} */}

            </form>
            {(!currentChat || !currentChat.chat_id) && (
                <div className="space-y-3 max-w-4xl w-full mx-auto px-2 py-2 mt-4">
                    <p className="text-xs font-medium text-gray-400 uppercase tracking-wide">Quick Actions</p>
                    <div className="flex flex-wrap gap-2">
                        {suggestions.map((suggestion) => (
                            <button
                                key={suggestion.id}
                                onClick={suggestion.action}
                                className="inline-flex items-center gap-1.5 px-2.5 py-1.5 rounded-md text-sm font-medium transition-all duration-150 bg-primary/10"

                            >
                                {suggestion.icon}
                                <span>{suggestion.text}</span>
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </motion.div>
    );
}


