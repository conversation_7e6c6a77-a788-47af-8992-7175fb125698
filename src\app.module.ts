import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { UserModule } from './user/user.module';
import { CommonModule } from './common/common.module';
import { KnowledgebaseModule } from './knowledgebase/knowledgebase.module';
import { EmailModule } from './email/email.module';
import { EmailProviderType } from './email/email.module';
import { FilesModule } from './files/files.module';
import config from './common/configs/config';
import { PrometheusModule } from './prometheus/prometheus.module';
import { SupportModule } from './support/support.module';
import { FeedbackModule } from './feedback/feedback.module';
import { UsageModule } from './usage/usage.module';
import { ConnectorsModule } from './connectors/connectors.module';
import { DynamoDBModule } from './dynamodb/dynamodb.module';
import { SuperAdminModule } from './superadmin/superadmin.module';
import { EtlModule, EtlProviderType } from './etl/etl.module';
// import { PlansModule } from './plans/plans.module';
import { IntegrationsModule } from './integrations/integrations.module';
import { OrganisationsModule } from './organisations/organisations.module';
import { MilvusModule } from './milvus/milvus.module';
import { ActivitiesModule } from './activities/activities.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [config],
    }),
    EmailModule.register({
      type: EmailProviderType.RESEND,
    }),
    EtlModule.register({
      type: EtlProviderType.CELERY,
    }),
    PrometheusModule,
    PrismaModule,
    AuthModule,
    UserModule,
    CommonModule,
    KnowledgebaseModule,
    EmailModule,
    EtlModule,
    FilesModule,
    SupportModule,
    FeedbackModule,
    UsageModule,
    ConnectorsModule,
    DynamoDBModule,
    SuperAdminModule,
    OrganisationsModule,
    MilvusModule,
    ActivitiesModule,
    // IntegrationsModule,
    // PlansModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
