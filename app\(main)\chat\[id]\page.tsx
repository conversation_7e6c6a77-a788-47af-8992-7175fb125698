"use client";
import React, { useEffect, useState, useRef } from "react";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import useChatStore from "@/store/chatStore";
import { useUser } from "@/contexts/UserContext";
import { chatClient } from "@/lib/chatClient";
import { ChatPanel } from "@/components/Chat/Panel";
import ChatMessages from "@/components/Chat/ChatMessages";
import { createChat, getChat } from "@/lib/aiClient";
import { cn } from "@/lib/utils";

interface Props {
    params: {
        id: string;
    };
}

const ChatPage = ({ params }: Props) => {
    const router = useRouter();
    const pathname = usePathname();
    const { addCurrentChat, input, loading, setLoading, setMessages, isSourcesDrawerOpen, setIsSourcesDrawerOpen } =
        useChatStore();
    const { user } = useUser();
    const initializedRef = useRef(false);
    const searchParams = useSearchParams();

    useEffect(() => {
        if (initializedRef.current) return;
        initializedRef.current = true;

        console.log("ChatPage rendered");
        const isNewChat = searchParams.get("new");
        if (isNewChat === "true") {
            const newChat = {
                chat_id: params.id,
                name: "",
                isNewChat: true,
                user_id: user.id || "",
            };
            router.replace(pathname);

            async function initializeChat() {
                setLoading(true);
                addCurrentChat(newChat);
                setLoading(false);
            }
            initializeChat();
        } else {
            async function getChatData() {
                setLoading(true);
                const chat = await getChat(params.id);

                function transformApiResponseToMessages(apiResponse: any, chatId: string) {
                    if (!apiResponse.messages) return [];
                    return apiResponse.messages.map((msg: any) => ({
                        message: msg.message,
                        role: msg.role,
                        updated_at: msg.updated_at,
                        message_id: msg.message_id,
                        chat_id: msg.chat_id || chatId,
                        sources: msg.sources,
                        created_at: msg.created_at,
                    }));
                }

                const metadata = {
                    chat_id: params.id,
                    name: chat.metadata?.title || chat.title || '',
                    user_id: user.id || "",
                };
                addCurrentChat(metadata);
                setMessages(transformApiResponseToMessages(chat, params.id));
                setLoading(false);
            }
            getChatData();
        }
    }, []);

    const renderChatContent = () => (
        <div className="h-[92vh] flex flex-col">
            {loading ? (
                <div className="flex flex-col justify-center items-center h-full space-y-4">
                    <div className="relative">
                        <div className="w-12 h-12 rounded-full border-t-2 border-b-2 border-gray-300 animate-spin"></div>
                        <div className="w-12 h-12 rounded-full border-r-2 border-l-2 border-primary animate-spin absolute top-0 animate-ping opacity-20"></div>
                    </div>
                    <p className="text-sm text-muted-foreground animate-pulse">
                        Loading conversation...
                    </p>
                </div>
            ) : (
                <>
                    <div className="flex-1 relative overflow-hidden">
                        <div className="absolute inset-0">
                            <ChatMessages />
                        </div>
                    </div>
                    <ChatPanel />
                </>
            )}
        </div>
    );

    return (
        <div className="h-[92vh] w-full">
            {renderChatContent()}
        </div>
    );
};

export default ChatPage;
