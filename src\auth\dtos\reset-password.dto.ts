import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RequestPasswordResetDto {
    @ApiProperty({
        description: 'Email address for password reset request',
        example: '<EMAIL>'
    })
    @IsString()
    @IsNotEmpty()
    email: string;
}

export class VerifyResetTokenDto {
    @ApiProperty({
        description: 'Password reset verification token',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @IsString()
    @IsNotEmpty()
    token: string;
}

export class ResetPasswordDto {
    @ApiProperty({
        description: 'Password reset verification token',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @IsString()
    @IsNotEmpty()
    token: string;

    @ApiProperty({
        description: 'New password to set',
        example: 'newSecurePassword123',
        minLength: 8
    })
    @IsString()
    @IsNotEmpty()
    @MinLength(8)
    newPassword: string;
} 