'use client'
import { useUser } from '@/contexts/UserContext'
import React from 'react'
import ProfileCard from './components/dashboard/profileCard'
import Meetings from './components/dashboard/meetings'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import Analytics from './components/dashboard/analytics'
import Activity from './components/dashboard/activity'
import Link from 'next/link'
import { Sparkles } from 'lucide-react'
import BlobBackground from '@/components/Organization/BlobBackground'
import DashboardCards from './components/dashboard/dashboardCards'
import { Check, UserPlus } from 'lucide-react'

export default function OrganizationPage() {
    const { user } = useUser()
    const hour = new Date().getHours();
    let greeting = "Hello";
    if (hour >= 5 && hour < 12) {
        greeting = "Good morning";
    } else if (hour >= 12 && hour < 17) {
        greeting = "Good afternoon";
    } else if (hour >= 17 && hour < 21) {
        greeting = "Good evening";
    } else if (hour >= 21 || hour < 2) {
        greeting = "Good night";
    } else if (hour >= 2 && hour < 5) {
        greeting = "Good midnight";
    }
    return (
        <div className='flex flex-col gap-4 mt-10'>
            <div className="flex flex-row justify-between items-center gap-2 w-full">
                <h1 className="text-2xl sm:text-3xl md:text-5xl font-semibold  bg-gradient-to-r from-[#C95158] to-[#4C68A8] bg-clip-text text-transparent whitespace-nowrap">
                    {greeting}, {user.name?.split(" ")[0]}
                </h1>
                <Link href="/chat" passHref legacyBehavior>
                    <Button
                        variant="outline"
                        className="flex items-center gap-2 px-4 py-2 rounded-full border-0 dark:bg-gray-800 dark:text-white"
                        aria-label="Go to Chat"
                    >
                        <Sparkles className="h-5 w-5 mr-1 text-blue-500" />
                        <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#C95158] to-[#4C68A8] font-bold">Go to Chat</span>
                    </Button>
                </Link>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="col-span-2 rounded-lg h-full w-full flex flex-col gap-4">
                    <div className=" grid grid-cols-2 gap-4">
                        <BlobBackground colorScheme="red-blue" className="rounded-lg w-full">
                            <div className="text-center space-y-6">
                                <div className="space-y-4">
                                    <div className="inline-flex items-center justify-center w-12 h-12 dark:bg-white/10 backdrop-blur-sm rounded-lg border dark:border-white/20">
                                        <UserPlus className="h-6 w-6 dark:text-white text-black" />
                                    </div>

                                    <div className="space-y-3">
                                        <blockquote className="dark:text-white/90 text-black/90 text-sm leading-relaxed max-w-xs mx-auto">
                                            <span className="text-2xl dark:text-white/40 text-black/40 leading-none">"</span>
                                            <span className="italic">Invite people to your org and experience the wonders of AIDE</span>
                                            <span className="text-2xl dark:text-white/40 text-black/40 leading-none">"</span>
                                        </blockquote>
                                    </div>
                                </div>
                            </div>
                        </BlobBackground>
                        <DashboardCards />
                    </div>
                    <Analytics />
                </div>
                <div className="col-span-1 flex flex-col gap-2 h-full rounded-lg w-full">
                    <Meetings />
                    <Activity />
                </div>


            </div>
        </div>
    )
}

