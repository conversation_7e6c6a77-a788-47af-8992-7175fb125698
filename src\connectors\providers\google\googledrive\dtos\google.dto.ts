import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class GoogleCallbackDto {
  @ApiProperty({
    description: 'The code from Google',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'The state from Google',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  state: string;
}

export class GoogleRefreshTokenDto {
  @ApiProperty({
    description: 'The user id',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'The refresh token',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;
}

export class CrawlGoogleDriveDto {
  @ApiProperty({
    description: 'user connector id',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  userConnectorId: string;
}
