import {
  IsString,
  IsArray,
  Validate<PERSON>ested,
  IsNumber,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class FileDto {
  @ApiProperty({ description: 'File name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'File size in bytes' })
  @IsNumber()
  size: number;

  @ApiProperty({ description: 'MIME type of the file' })
  @IsString()
  mimeType: string;

  @ApiProperty({ description: 'URL of the file' })
  @IsString()
  url: string;
}

export class AddFilesDto {
  @ApiProperty({ description: 'ID of the knowledge base' })
  @IsString()
  kid: string;

  @ApiProperty({ type: [FileDto], description: 'Array of file metadata' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}
