import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { google } from 'googleapis';
import { ConfigService } from '@nestjs/config';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { ConnectorsService } from '../../../v1/connectors.v1.service';
import { OAuthConnectorService } from 'src/connectors/interfaces/oauth.interface';

@Injectable()
export class GoogleCalendarService extends OAuthConnectorService {
  private oauth2Client: any;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
  ) {
    super();
    this.oauth2Client = new google.auth.OAuth2(
      this.configService.get<string>('auth.google.clientId'),
      this.configService.get<string>('auth.google.clientSecret'),
      this.configService.get<string>('auth.google.redirectUri') + '/googlecalendar',
    );
  }

  async generateRedirectUrl(userId: string) {
    const scopes = [
      'https://www.googleapis.com/auth/calendar.readonly',
      'https://www.googleapis.com/auth/calendar.events.readonly',
      'https://www.googleapis.com/auth/calendar.settings.readonly',
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
      state: userId,
    });
  }

  async handleCallback(code: string, state: string) {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      const { access_token, refresh_token, expiry_date } = tokens;
      const userId = state;

      this.oauth2Client.setCredentials(tokens);
      const oauth2 = google.oauth2('v2');
      const userInfo = await oauth2.userinfo.get({ auth: this.oauth2Client });

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Google Calendar',
        },
        select: {
          id: true,
          type: true,
        },
      });

      await this.prisma.userConnectors.create({
        data: {
          userId,
          connectorId: connector.id,
          config: {
            email: userInfo.data.email,
            name: userInfo.data.name,
            picture: userInfo.data.picture,
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(access_token);
      const { encryptedData: refreshTokenEncrypted, iv: refreshTokenIv } =
        this.connectorsService.encrypt(refresh_token);

      const userConnectorDetails = {
        user_id: userId,
        connector_id: connector.id,
        connector_type: connector.type,
        name: 'Google Calendar',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
          refresh_token: refreshTokenEncrypted,
          refresh_token_iv: refreshTokenIv,
          expiry_date: expiry_date,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        userConnectorDetails,
      );

      return {
        success: true,
        message: 'Google Calendar integration connected successfully',
        data: {
          email: userInfo.data.email,
          name: userInfo.data.name,
          picture: userInfo.data.picture,
        },
      };
    } catch (error) {
      console.log(error);
      throw new BadRequestException(
        'Failed to process Google Calendar callback',
      );
    }
  }

  async refreshToken(refreshToken: string, userId: string) {
    try {
      this.oauth2Client.setCredentials({
        refresh_token: refreshToken,
      });

      const { credentials } = await this.oauth2Client.refreshAccessToken();
      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'Google Calendar',
          },
        },
      );

      if (!userConnectors?.[0]) {
        throw new NotFoundException('User connector not found');
      }

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(credentials.access_token);

      const updatedUserConnector = {
        ...userConnectors[0],
        updated_at: new Date().toISOString(),
        credentials: {
          ...userConnectors[0].credentials,
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
          expiry_date: credentials.expiry_date,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        updatedUserConnector,
      );

      return {
        accessToken: credentials.access_token,
        expiresAt: credentials.expiry_date,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      if (error.response?.data?.error === 'invalid_grant') {
        throw new BadRequestException(
          'Refresh token is invalid or has expired. User needs to re-authenticate.',
        );
      }

      throw new BadRequestException(
        `Failed to refresh access token: ${error.message}`,
      );
    }
  }
  async getGoogleCalendarUserConfigs(userId: string) {
    const connector = await this.prisma.connectors.findFirst({
      where: {
        slug: 'googlecalendar',
      },
      include: {
        UserConnectors: {
          where: {
            userId: userId,
          },
          select: {
            config: true,
            id: true,
            etls: true,
          },
        },
      },
    });
    if (!connector.UserConnectors.length) {
      return { config: null, id: null, etls: [] };
    }

    return {
      config: connector.UserConnectors[0].config,
      id: connector.UserConnectors[0].id,
      etls: connector.UserConnectors[0].etls,
    };
  }

  async disconnect(userId: string) {
    try {
      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'Google Calendar',
        },
        select: {
          id: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('Google Calendar connector not found');
      }

      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'Google Calendar',
          },
        },
      );

      const userConnector = userConnectors?.[0];
      if (!userConnector) {
        throw new NotFoundException(
          'No active Google Calendar connection found for this user',
        );
      }

      const decryptedAccessToken = this.connectorsService.decrypt(
        userConnector.credentials.access_token,
        userConnector.credentials.access_token_iv,
      );

      if (decryptedAccessToken) {
        try {
          this.oauth2Client.setCredentials({
            access_token: decryptedAccessToken,
          });
          await this.oauth2Client.revokeToken(decryptedAccessToken);
        } catch (error) {
          const errorMessage = error.response?.data?.error || error.message;
        }
      }

      await this.prisma.userConnectors.deleteMany({
        where: {
          userId,
          connectorId: connector.id,
        },
      });

      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'Google Calendar',
      });

      return {
        success: true,
        message: 'Successfully disconnected Google Calendar integration',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to disconnect Google Calendar integration: ${error.message}`,
      );
    }
  }

  async getUpcomingMeetings(userId: string, maxResults: number = 5) {
    try {
      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression: 'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: { '#connectorName': 'name' },
          expressionAttributeValues: { ':userId': userId, ':name': 'Google Calendar' },
        },
      );

      if (!userConnectors?.[0]) {
        throw new NotFoundException('No active Google Calendar connection found for this user');
      }

      const userConnector = userConnectors[0];
      const { accessToken } = await this.refreshToken(
        userConnector.credentials.refresh_token,
        userId
      );

      this.oauth2Client.setCredentials({ access_token: accessToken });
      const calendar = google.calendar({ version: 'v3' });
      const response = await calendar.events.list({
        auth: this.oauth2Client,
        calendarId: 'primary',
        timeMin: new Date().toISOString(),
        maxResults,
        singleEvents: true,
        orderBy: 'startTime',
      });

      return response.data.items.map(event => ({
        id: event.id,
        summary: event.summary,
        start: event.start.dateTime || event.start.date,
        end: event.end.dateTime || event.end.date,
        location: event.location,
        description: event.description,
      }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to fetch upcoming meetings: ${error.message}`);
    }
  }
}
