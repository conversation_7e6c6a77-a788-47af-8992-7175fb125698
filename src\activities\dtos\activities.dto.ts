import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ActivityType } from '@prisma/client';
import { IsOptional } from 'class-validator';

export class ActivityUserDto {
  @ApiProperty({
    description: 'User ID',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'User name',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiPropertyOptional({
    description: 'User profile image URL',
    example: 'https://example.com/avatar.jpg',
  })
  profileImageUrl?: string;

  @ApiProperty({
    description: 'User role',
    example: 'ADMIN',
  })
  role: string;
}

export class ActivityLogDto {
  @ApiProperty({
    description: 'Activity log ID',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'Organization ID',
    example: 'uuid-string',
  })
  organizationId: string;

  @ApiPropertyOptional({
    description: 'User who performed the action',
    type: ActivityUserDto,
  })
  user?: ActivityUserDto;

  @ApiProperty({
    description: 'Type of activity',
    enum: ActivityType,
    example: ActivityType.USER_JOINED_ORGANIZATION,
  })
  activityType: ActivityType;

  @ApiProperty({
    description: 'Human-readable description of the activity',
    example: 'John Doe joined the organization',
  })
  description: string;

  @ApiPropertyOptional({
    description: 'Additional metadata about the activity',
    example: { teamId: 'uuid-string', previousRole: 'USER', newRole: 'ADMIN' },
  })
  metadata?: any;

  @ApiPropertyOptional({
    description: 'IP address of the actor',
    example: '***********',
  })
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'User agent string',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  userAgent?: string;

  @ApiProperty({
    description: 'Timestamp when the activity occurred',
    example: '2024-01-01T12:00:00.000Z',
  })
  createdAt: Date;
}

export class GetActivitiesResponseDto {
  @ApiProperty({
    description: 'List of recent activity logs (top 10)',
    type: [ActivityLogDto],
  })
  activities: ActivityLogDto[];

  @ApiProperty({
    description: 'Total number of activities in the organization',
    example: 150,
  })
  totalActivities: number;
}

export class CreateActivityLogDto {
  @ApiProperty({
    description: 'Organization ID',
    example: 'uuid-string',
    required: false,
  })
  @IsOptional()
  organizationId?: string;

  @ApiPropertyOptional({
    description: 'User ID who performed the action',
    example: 'uuid-string',
  })
  userId?: string;

  @ApiProperty({
    description: 'Type of activity',
    enum: ActivityType,
    example: ActivityType.USER_JOINED_ORGANIZATION,
  })
  activityType: ActivityType;

  @ApiProperty({
    description: 'Human-readable description of the activity',
    example: 'John Doe joined the organization',
  })
  description: string;

  @ApiPropertyOptional({
    description: 'Additional metadata about the activity',
    example: { teamId: 'uuid-string', connectorName: 'Google Drive' },
  })
  metadata?: any;

  @ApiPropertyOptional({
    description: 'IP address of the actor',
    example: '***********',
  })
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'User agent string',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  userAgent?: string;
}
