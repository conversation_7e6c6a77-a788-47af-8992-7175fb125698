export const teamAdditionEmail = (userName: string, teamName: string, organizationName: string) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                margin: 0;
                padding: 0;
                background-color: #f6f9fc;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            }

            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 40px 20px;
            }

            .card {
                background-color: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .title {
                color: #1a1a1a;
                font-size: 24px;
                margin-bottom: 24px;
                text-align: center;
            }

            .text {
                color: #4a5568;
                font-size: 16px;
                line-height: 24px;
                margin-bottom: 32px;
            }

            .footer {
                margin-top: 32px;
                text-align: center;
                color: #718096;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="card">
                <h1 class="title">You've been added to a team!</h1>
                
                <div class="text">
                    <p>Hello ${userName},</p>
                    
                    <p>You have been added to the <strong>${teamName}</strong> team at <strong>${organizationName}</strong>.</p>
                    
                    <p>You can now collaborate with your team members and access team resources.</p>
                </div>
                
                <div class="footer">
                    <p>This is an automated notification. No action is required.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
};
