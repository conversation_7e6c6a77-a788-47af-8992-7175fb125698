import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ConnectedConnectorDto {
  @ApiProperty({
    description: 'Connector ID',
    example: 'uuid-string',
  })
  connectorId: string;

  @ApiProperty({
    description: 'Connector name',
    example: 'Google Drive',
  })
  name: string;

  @ApiProperty({
    description: 'Connector type',
    example: 'GOOGLE_DRIVE',
  })
  type: string;

  @ApiProperty({
    description: 'Connector logo URL',
    example: 'https://example.com/google-drive-logo.png',
  })
  logo: string;

  @ApiProperty({
    description: 'Connector category',
    example: 'Cloud Storage',
  })
  category?: string;

  @ApiProperty({
    description: 'Connector description',
    example: 'Connect your Google Drive to sync files',
  })
  description: string;

  @ApiProperty({
    description: 'Connection status',
    example: 'ACTIVE',
  })
  status: string;

  @ApiProperty({
    description: 'Date when connector was connected',
    example: '2024-01-01T12:00:00.000Z',
  })
  connectedAt: Date;

  @ApiProperty({
    description: 'Last updated date',
    example: '2024-01-01T12:00:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Additional configuration data',
    example: { name: 'John Doe', email: '<EMAIL>' },
  })
  config?: any;
}

export class UserConnectedConnectorsDto {
  @ApiProperty({
    description: 'User ID',
    example: 'uuid-string',
  })
  userId: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe',
  })
  userName: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  userEmail: string;

  @ApiProperty({
    description: 'User role',
    example: 'ADMIN',
  })
  userRole: string;

  @ApiProperty({
    description: 'List of connected connectors',
    type: [ConnectedConnectorDto],
  })
  connectors: ConnectedConnectorDto[];

  @ApiProperty({
    description: 'Total number of connected connectors',
    example: 3,
  })
  totalConnectors: number;
}

export class OrganizationConnectedConnectorsResponseDto {
  @ApiProperty({
    description: 'Organization ID',
    example: 'uuid-string',
  })
  organizationId: string;

  @ApiProperty({
    description: 'Organization name',
    example: 'Acme Corp',
  })
  organizationName: string;

  @ApiProperty({
    description: 'List of users and their connected connectors',
    type: [UserConnectedConnectorsDto],
  })
  users: UserConnectedConnectorsDto[];

  @ApiProperty({
    description: 'Total number of users in organization',
    example: 25,
  })
  totalUsers: number;

  @ApiProperty({
    description: 'Total number of connector connections across all users',
    example: 75,
  })
  totalConnections: number;

  @ApiProperty({
    description: 'Summary of connector usage',
    example: {
      'Google Drive': 15,
      'Slack': 20,
      'GitHub': 10,
      'Notion': 8,
    },
  })
  connectorSummary: Record<string, number>;
}

export class UserConnectedConnectorsResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 'uuid-string',
  })
  userId: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe',
  })
  userName: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  userEmail: string;

  @ApiProperty({
    description: 'Organization ID',
    example: 'uuid-string',
  })
  organizationId: string;

  @ApiProperty({
    description: 'List of connected connectors',
    type: [ConnectedConnectorDto],
  })
  connectors: ConnectedConnectorDto[];

  @ApiProperty({
    description: 'Total number of connected connectors',
    example: 3,
  })
  totalConnectors: number;
}
