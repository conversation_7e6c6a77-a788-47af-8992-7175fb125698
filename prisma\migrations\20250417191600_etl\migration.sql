-- CreateEnum
CREATE TYPE "EtlStatus" AS ENUM ('QUEUED', 'RUNNING', 'SUCCESS', 'FAILED');

-- CreateEnum
CREATE TYPE "ConnectorName" AS ENUM ('NOTION', 'SLACK', 'GOO<PERSON><PERSON>_CALENDAR', 'GOOGLE_DRIVE', 'HUBSPOT', 'GITHUB', 'ONEDRIVE');

-- CreateTable
CREATE TABLE "Etl" (
    "id" TEXT NOT NULL,
    "userConnectorId" TEXT NOT NULL,
    "dagId" "ConnectorName" NOT NULL,
    "dagRunId" TEXT NOT NULL,
    "status" "EtlStatus" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Etl_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Etl" ADD CONSTRAINT "Etl_userConnectorId_fkey" FOREIGN KEY ("userConnectorId") REFERENCES "UserConnectors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
