import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@steps-ai/ui'
import React from 'react'
import { assets } from '@/lib/assets'

const capabilities = [
    'Search for messages by keyword, sender, or date',
    'List and filter your Gmail messages',
    'Summarize and analyze email threads',
    'Extract insights and trends from your inbox',
    'No create/send operations — only getting and analyzing',
]

const Agent = () => {
    return (
        <div className="w-full bg-background">
            <Card className="bg-background border shadow-xl w-full  overflow-hidden">
                <CardHeader className="flex flex-col items-center gap-2 pb-2">
                    <div className="flex items-center justify-center w-16 h-16 rounded-full bg-muted shadow mb-2">
                        <img src={assets.Icons.Gmail} alt="Gmail" className="w-10 h-10" />
                    </div>
                    <CardTitle className="text-3xl font-extrabold text-foreground tracking-tight">Gmail Agent is Live!</CardTitle>
             
                </CardHeader>
                <CardContent>
                    <div className="flex flex-col items-center gap-6 py-4">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full max-w-2xl">
                            {capabilities.map((cap, i) => (
                                <div key={i} className="flex items-center gap-3 bg-muted rounded-lg px-4 py-3 shadow border border-border">
                                    <span className="inline-block w-2 h-2 rounded-full bg-green-400 animate-pulse" />
                                    <span className="text-foreground font-medium text-base">{cap}</span>
                                </div>
                            ))}
                        </div>
                        <div className="mt-6 text-center text-muted-foreground text-sm">
                            <span className="font-semibold">Tip:</span> Try asking things like <span className="italic">"Find all emails from Alice last week"</span> or <span className="italic">"Summarize my unread messages"</span>.
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default Agent
