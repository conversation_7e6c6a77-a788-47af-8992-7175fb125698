import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { FeedbackDto } from '../dto/feedback.dto';

@Injectable()
export class FeedbackService {
    constructor(private prisma: PrismaService) { }

    async createFeedback(feedback: FeedbackDto, userId: string) {
        return this.prisma.feedback.create({
            data: {
                ...feedback,
                userId,
            },
        });
    }
}
