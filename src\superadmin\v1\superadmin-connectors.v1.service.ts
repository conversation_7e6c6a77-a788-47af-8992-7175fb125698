import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateConnectorDto } from '../../connectors/dtos/addConnectors.dto';
import { UpdateConnectorDto } from '../dtos/update-connector.dto';
import { PaginationDto } from '../dtos/pagination.dto';
import { ConnectorsStatus, ConnectorsType } from '@prisma/client';

@Injectable()
export class SuperAdminConnectorsService {
  constructor(private readonly prisma: PrismaService) {}

  async getAllConnectors(paginationDto: PaginationDto) {
    const { page = 1, limit = 10 } = paginationDto;

    const [connectors, total] = await Promise.all([
      this.prisma.connectors.findMany({
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.connectors.count(),
    ]);

    return {
      data: connectors,
      metadata: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getConnectorById(id: string) {
    const connector = await this.prisma.connectors.findUnique({
      where: { id },
      include: {
        UserConnectors: {
          select: {
            id: true,
            userId: true,
            createdAt: true,
          },
        },
      },
    });

    if (!connector) {
      throw new NotFoundException('Connector not found');
    }

    return {
      ...connector,
      userCount: connector.UserConnectors.length,
      recentUsers: connector.UserConnectors.slice(0, 5),
    };
  }

  async createConnector(createConnectorDto: CreateConnectorDto) {
    if (
      !Array.isArray(createConnectorDto.connectors) ||
      createConnectorDto.connectors.length === 0
    ) {
      throw new BadRequestException(
        'Invalid input: Expected non-empty array of connectors',
      );
    }

    const connectorsForDb = createConnectorDto.connectors.map((connector) => {
      if (!connector.name || !connector.logo || !connector.description) {
        throw new BadRequestException(
          'Each connector must have a name, logo, and description',
        );
      }

      if (
        connector.status &&
        !Object.values(ConnectorsStatus).includes(
          connector.status as ConnectorsStatus,
        )
      ) {
        throw new BadRequestException(`Invalid status: ${connector.status}`);
      }

      if (
        connector.type &&
        !Object.values(ConnectorsType).includes(
          connector.type as ConnectorsType,
        )
      ) {
        throw new BadRequestException(`Invalid type: ${connector.type}`);
      }

      const slug = connector.name.toLowerCase().replace(/\s+/g, '-');

      return {
        name: connector.name,
        logo: connector.logo,
        description: connector.description,
        status: connector.status,
        type: connector.type,
        slug,
      };
    });

    const result = await this.prisma.connectors.createMany({
      data: connectorsForDb,
      skipDuplicates: true,
    });

    return {
      success: true,
      count: result.count,
      message: `${result.count} connectors created successfully`,
    };
  }

  async updateConnector(id: string, updateConnectorDto: UpdateConnectorDto) {
    const existingConnector = await this.prisma.connectors.findUnique({
      where: { id },
    });

    if (!existingConnector) {
      throw new NotFoundException('Connector not found');
    }

    const updatedConnector = await this.prisma.connectors.update({
      where: { id },
      data: {
        ...updateConnectorDto,
        updatedAt: new Date(),
      },
    });

    return {
      success: true,
      message: 'Connector updated successfully',
      data: updatedConnector,
    };
  }

  async deleteConnector(id: string) {
    const existingConnector = await this.prisma.connectors.findUnique({
      where: { id },
      include: {
        UserConnectors: {
          select: {
            id: true,
          },
        },
      },
    });

    if (!existingConnector) {
      throw new NotFoundException('Connector not found');
    }

    if (existingConnector.UserConnectors.length > 0) {
      throw new BadRequestException(
        `Cannot delete connector that is in use by ${existingConnector.UserConnectors.length} users`,
      );
    }

    await this.prisma.connectors.delete({
      where: { id },
    });

    return {
      success: true,
      message: 'Connector deleted successfully',
    };
  }

  async getConnectorsStats() {
    const connectorsWithCount = await this.prisma.connectors.findMany({
      select: {
        id: true,
        name: true,
        type: true,
        status: true,
        _count: {
          select: {
            UserConnectors: true,
          },
        },
      },
    });

    const totalUsers = await this.prisma.user.count();

    const connectionsPerUser = await this.prisma.userConnectors.groupBy({
      by: ['userId'],
      _count: {
        _all: true,
      },
    });

    return {
      connectors: connectorsWithCount.map((c) => ({
        id: c.id,
        name: c.name,
        type: c.type,
        status: c.status,
        userCount: c._count.UserConnectors,
        percentage:
          totalUsers > 0 ? (c._count.UserConnectors / totalUsers) * 100 : 0,
      })),
      totalUsers,
      totalConnections: connectionsPerUser.length,
      avgConnectionsPerUser:
        connectionsPerUser.length > 0
          ? connectionsPerUser.reduce(
              (acc, user) => acc + user._count._all,
              0,
            ) / connectionsPerUser.length
          : 0,
    };
  }
}
