import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class FeedbackDto {
    @IsNotEmpty()
    @IsString()
    @ApiProperty({ description: 'The message of the feedback', example: 'This is a feedback message' })
    message: string;

    @IsNotEmpty()
    @IsEmail()
    @ApiProperty({ description: 'The email of the user', example: '<EMAIL>' })
    email: string;

    @IsNotEmpty()
    @IsNumber()
    @ApiProperty({ description: 'The rating of the feedback', example: 5 })
    rating: number;

    @IsNotEmpty()
    @IsString()
    @ApiProperty({ description: 'The area of the feedback', example: 'General' })
    area: string;
}