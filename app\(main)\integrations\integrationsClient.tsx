"use client"
import React from 'react'
import { useApiQuery } from '@/lib/apiClient'
import LayoutAnimation from '@/components/Animation/layoutAnimation'
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Image from 'next/image'
import Link from 'next/link'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
interface Integration {
    id: string;
    name: string;
    logo: string;
    description: string;
    isConnected?: boolean;
    slug: string;
    type?: string;
    status?: string;
}

const IntegrationsClient = () => {
    const { data, isLoading } = useApiQuery<{ result: Integration[] }>(
        ["integrations"],
        `/integrations`
    );

    return (
        <LayoutAnimation>
            <div className="px-6 md:px-20 py-5 rounded-lg space-y-8">
                <div className="flex flex-col md:flex-row justify-between items-start gap-6">
                    <div>
                        <h2 className="text-4xl font-bold text-text-primary">
                            Integrations
                        </h2>
                        <p className="text-text-secondary mt-2">Connect your tools to AIDE to enhance your workflow.</p>
                        <a
                            href="https://docs.stepsai.co"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:text-primary/90 inline-flex items-center mt-2 text-sm font-medium"
                        >
                            Learn how integrations work →
                        </a>
                    </div>
                    <Button variant="outline" className="w-full md:w-auto">Request an integration</Button>
                </div>

                <Separator />

                {isLoading ? (
                    <div className="flex items-center justify-center py-20">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <span className="ml-2 text-text-secondary">Loading integrations...</span>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {data?.result && data.result.length > 0 ? (
                            data.result.map((integration) => (
                                <Link href={`/integrations/${integration.slug}`} key={integration.id}>
                                    <Card
                                        className={cn(
                                            "hover:bg-muted/50 relative overflow-hidden",
                                            "h-[220px] sm:h-[180px] md:h-[220px] lg:h-[240px]",
                                        )}
                                    >
                                        <CardHeader className="flex flex-col items-start gap-4 justify-center space-y-0 p-3 md:p-4">
                                            <Image
                                                src={integration.logo}
                                                alt={`${integration.name} logo`}
                                                width={80}
                                                height={80}
                                                className="mx-auto mb-2 p-2 rounded-2xl border border-gray-300 dark:border-gray-800"
                                            />
                                        </CardHeader>
                                        <CardTitle className="items-start flex flex-col gap-2 justify-start px-3 md:px-4">
                                            {integration.type && (
                                                <Badge variant="secondary" className="text-xs">
                                                    {integration.type.charAt(0).toUpperCase() + integration.type.slice(1).toLowerCase()}
                                                </Badge>
                                            )}
                                            <span className="text-lg font-semibold truncate block lg:block md:hidden">
                                                {integration.name}
                                            </span>
                                        </CardTitle>
                                        <CardContent className="px-3 md:px-4 my-1">
                                            <CardDescription className="line-clamp-2 text-xs sm:text-sm text-start block">
                                                {integration.description}
                                            </CardDescription>
                                        </CardContent>
                                        <CardFooter></CardFooter>
                                    </Card>
                                </Link>
                            ))
                        ) : (
                            <div className="col-span-full text-center py-10">
                                <p className="text-text-secondary">No integrations available at the moment.</p>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </LayoutAnimation>
    )
}

export default IntegrationsClient;
