"use client"
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { ExternalLink, Activity, Database, Users, Signal } from "lucide-react";
import Link from 'next/link';

const usageData = [
    {
        id: 1,
        resource: "Requests per day",
        usage: "145",
        maximum: "1,000",
        href: "#",
        icon: <Activity className="w-5 h-5" />
    },
    {
        id: 2,
        resource: "Storage per month",
        usage: "1.1",
        maximum: "10 GB",
        href: "#",
        icon: <Database className="w-5 h-5" />
    },
    {
        id: 3,
        resource: "Members",
        usage: "10",
        maximum: "25",
        href: "#",
        icon: <Users className="w-5 h-5" />
    },
    {
        id: 4,
        resource: "Availability",
        usage: "95.1",
        maximum: "99.9%",
        href: "#",
        icon: <Signal className="w-5 h-5" />
    }
];

export default function UsagePage() {
    return (
        <div className="container mx-auto px-6 py-8 max-w-7xl">
            <div className="space-y-2 mb-8">
                <h1 className="text-3xl font-bold text-foreground">Usage Overview</h1>
                <p className="text-muted-foreground">
                    Monitor your workspace usage and resource allocation
                </p>
            </div>

            <div className="mb-8">
                <p className="text-sm text-muted-foreground">
                    Learn more about our{' '}
                    <Link href="/" className="inline-flex items-center gap-1 text-primary hover:underline">
                        upgrade options
                        <ExternalLink className="w-3 h-3" />
                    </Link>
                </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {usageData.map((item) => (
                    <Card 
                        key={item.id} 
                        className="group relative hover:shadow-md transition-shadow duration-200"
                    >
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="p-2 rounded-lg bg-primary/10 text-primary">
                                    {item.icon}
                                </div>
                                <h3 className="text-sm font-medium text-foreground">
                                    {item.resource}
                                </h3>
                            </div>
                            <div className="flex items-baseline gap-2">
                                <span className="text-2xl font-bold text-foreground">
                                    {item.usage}
                                </span>
                                <span className="text-sm text-muted-foreground">
                                    / {item.maximum}
                                </span>
                            </div>
                            <Link href={item.href} className="absolute inset-0" aria-label={`View ${item.resource} details`}>
                                <span className="sr-only">View details</span>
                            </Link>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
} 