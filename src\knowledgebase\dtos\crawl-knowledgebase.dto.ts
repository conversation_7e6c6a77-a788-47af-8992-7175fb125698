import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { FileDto } from './add-files.dto';
import { Type } from 'class-transformer';

export class CrawlKnowledgeBaseDto {
  @ApiProperty({ description: 'ID of the knowledge base' })
  @IsString()
  kid: string;

  @ApiProperty({ type: [FileDto], description: 'Array of file metadata' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}
