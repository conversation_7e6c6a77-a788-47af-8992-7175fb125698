import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class UpdateOrgAdminDto {
  @ApiProperty({
    description: 'Email of the new admin',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Organization ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  organizationId: string;
}
