import {
  IsEmail,
  IsNot<PERSON>mpty,
  Is<PERSON><PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserDto {
  @ApiProperty({
    example: 'profileImageUrl',
    description: 'User profile image url',
  })
  @IsString({ message: 'User profile image must be a string' })
  @IsOptional()
  profileImageUrl: string;

  @ApiProperty({
    example: '<PERSON> Do<PERSON>',
    description: 'User Name',
  })
  @IsString({ message: 'User name must be a string' })
  @IsOptional()
  name: string;
}
