"use client"

import { useState } from "react"
import { But<PERSON> } from "@steps-ai/ui"
import { Input } from "@steps-ai/ui"
import { Label } from "@steps-ai/ui"
import { toast } from "sonner"
import {
    useApiMutation,
    createMutationFn,
    queryClient
} from "@/lib/apiClient";
import { EyeIcon, EyeOffIcon } from 'lucide-react'
import Link from "next/link"
import { signOut } from "next-auth/react"
export default function PasswordChangeForm() {
    const [showNewPassword, setShowNewPassword] = useState(false)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)
    const [loading, setLoading] = useState(false)
    const [formData, setFormData] = useState({
        newPassword: "",
        confirmPassword: "",
    })
    
    const [validationErrors, setValidationErrors] = useState({
        minLength: false,
        uppercase: false,
        lowercase: false,
        special: false,
        number: false,
    })

    const validatePassword = (password: string) => {
        setValidationErrors({
            minLength: password.length >= 12,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
            number: /[0-9]/.test(password),
        })
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({ ...prev, [name]: value }))
        if (name === "newPassword") {
            validatePassword(value)
        }
    }

    const updatePassword = useApiMutation(
        createMutationFn.post('/user/update-password'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['user'] });
                    toast.success("Password updated successfully . You will be automatically logged out.");
                    signOut();
                } else {
                    setLoading(false)
                    toast.error("Something went wrong. Please try again.");
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again.";
                    toast.error(errorMessage);
                    setLoading(false)
                } else if (error.request) {
                    toast.error("No response received from server");
                    setLoading(false)
                } else {
                    toast.error("Error setting up the request");
                    setLoading(false)
                }
            }
        }
    );

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        setLoading(true)
        updatePassword.mutate({ password: formData.newPassword })
    }

    return (
        <form onSubmit={handleSubmit} className="w-full flex flex-col gap-5 pl-5">

            <div className="space-y-2">
                <Label htmlFor="newPassword">New Password</Label>
                <div className="relative">
                    <Input
                        id="newPassword"
                        name="newPassword"
                        type={showNewPassword ? "text" : "password"}
                        value={formData.newPassword}
                        onChange={handleInputChange}
                        className={`pr-10 ${formData.newPassword
                            ? Object.values(validationErrors).every(Boolean)
                                ? "border-green-500"
                                : "border-red-500"
                            : ""
                            }`}
                    />
                    <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                        {showNewPassword ? (
                            <EyeOffIcon className="h-4 w-4" />
                        ) : (
                            <EyeIcon className="h-4 w-4" />
                        )}
                    </button>
                </div>
                {formData.newPassword && (
                    <ul className="mt-2 space-y-1 text-sm">
                        <li
                            className={`flex items-center gap-1 ${validationErrors.minLength ? "text-green-500" : "text-gray-500"
                                }`}
                        >
                            {validationErrors.minLength ? "✓" : "•"} Minimum characters 12
                        </li>
                        <li
                            className={`flex items-center gap-1 ${validationErrors.uppercase ? "text-green-500" : "text-red-500"
                                }`}
                        >
                            {validationErrors.uppercase ? "✓" : "•"} One uppercase character
                        </li>
                        <li
                            className={`flex items-center gap-1 ${validationErrors.lowercase ? "text-green-500" : "text-gray-500"
                                }`}
                        >
                            {validationErrors.lowercase ? "✓" : "•"} One lowercase character
                        </li>
                        <li
                            className={`flex items-center gap-1 ${validationErrors.special ? "text-green-500" : "text-gray-500"
                                }`}
                        >
                            {validationErrors.special ? "✓" : "•"} One special character
                        </li>
                        <li
                            className={`flex items-center gap-1 ${validationErrors.number ? "text-green-500" : "text-gray-500"
                                }`}
                        >
                            {validationErrors.number ? "✓" : "•"} One number
                        </li>
                    </ul>
                )}
            </div>

            <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <div className="relative">
                    <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className="pr-10"
                    />
                    <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                        {showConfirmPassword ? (
                            <EyeOffIcon className="h-4 w-4" />
                        ) : (
                            <EyeIcon className="h-4 w-4" />
                        )}
                    </button>
                </div>
            </div>


            <div className="flex flex-col md:flex-row justify-between items-center">

                <Button
                    type="submit"
                    className="bg-blue-800 hover:bg-blue-700"
                    disabled={
                        !formData.newPassword ||
                        !formData.confirmPassword ||
                        !Object.values(validationErrors).every(Boolean) ||
                        formData.newPassword !== formData.confirmPassword ||
                        loading
                    }

                >
                    {loading ? (
                        "Updating..."
                    ) : (
                        "Update Password"
                    )}
                </Button>

                <div className="text-center">
                    <Link href="#" className="text-sm text-blue-500 hover:text-blue-600">
                        Forgot Password?
                    </Link>
                </div>
            </div>




        </form>
    )
}

