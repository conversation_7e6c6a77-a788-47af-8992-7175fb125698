interface User {
  name: string;
  token: string;
}

export const emailVerification = (data: User) => {
  const { name, token } = data;
  return `<html>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Welcome to StepsAI</title>
    <style>
      body {
        font-family: 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
      }
      .container {
        max-width: 600px;
        margin: 40px auto;
        background: #ffffff;
        padding: 40px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 30px;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #2d3748;
        font-size: 28px;
        margin: 0;
      }
      .content {
        text-align: center;
        color: #4a5568;
        padding: 0 20px;
      }
      .content p {
        margin: 16px 0;
      }
      .button {
        display: inline-block;
        padding: 14px 32px;
        background-color: #0066ff;
        color: #ffffff !important;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        margin: 25px 0;
        box-shadow: 0 2px 4px rgba(0, 102, 255, 0.2);
        transition: all 0.2s ease;
      }
      .button:hover {
        background-color: #0052cc;
        box-shadow: 0 4px 6px rgba(0, 102, 255, 0.3);
      }
      .link-text {
        color: #718096;
        font-size: 14px;
        word-break: break-all;
        background: #f8f9fa;
        padding: 12px;
        border-radius: 6px;
        margin: 20px 0;
      }
      .footer {
        text-align: center;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 2px solid #f0f0f0;
        color: #a0aec0;
        font-size: 14px;
      }
      a {
        color: #0066ff;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Welcome to StepsAI, ${name}!</h1>
      </div>
      <div class="content">
        <p>Thank you for signing up with StepsAI. We're excited to have you on board!</p>
        
        <p>To get started, please verify your email address by clicking the button below:</p>
        
        <a href='${process.env.FRONTEND_URL}/verify-email?token=${token}' class="button">
          Verify Email Address
        </a>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <div class="link-text">
          ${process.env.FRONTEND_URL}/verify-email?token=${token}
        </div>

        <p>If you have any questions or need help, feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        <p>Best regards,<br />The StepsAI Team</p>
      </div>
      <div class="footer">
        <p>This email was sent by StepsAI. If you didn't create this account, please ignore this email.</p>
        <p>&copy; ${new Date().getFullYear()} StepsAI. All rights reserved.</p>
      </div>
    </div>
  </body>
  </html>`;
};
