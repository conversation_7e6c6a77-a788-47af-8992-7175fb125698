import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { InvitationsService } from './invitations.service';
import { AdminAuthGuard } from '../../../auth/guards/admin-auth.guard';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../../common/decorators/getUser.decorator';
import { JwtPayload } from '../../../common/types/jwt-payload';
import { GetInvitationsDto, CancelInvitationDto, ResendInvitationDto } from '../dtos/invitation.dto';

@ApiTags('Invitations-V1')
@Controller({ version: '1', path: 'invitations' })
export class InvitationsController {
  constructor(private readonly invitationsService: InvitationsService) {}

  @Get()
  @ApiOperation({
    summary: 'Get all invitations for an organization',
    description: 'Retrieves all invitations for a specific organization. By default, excludes accepted invitations (shows only PENDING, EXPIRED, CANCELLED). To see accepted invitations, explicitly set status=ACCEPTED. Organization ID is required. Admins can only see invitations from their own organization.',
  })
  @ApiResponse({
    status: 200,
    description: 'Invitations fetched successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Invitations fetched successfully' },
        invitations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'invitation-uuid' },
              email: { type: 'string', example: '<EMAIL>' },
              type: { type: 'string', example: 'ORGANIZATION', enum: ['ORGANIZATION', 'TEAM'] },
              status: { type: 'string', example: 'PENDING', enum: ['PENDING', 'ACCEPTED', 'EXPIRED', 'CANCELLED'] },
              expiresAt: { type: 'string', format: 'date-time' },
              createdAt: { type: 'string', format: 'date-time' },
              acceptedAt: { type: 'string', format: 'date-time', nullable: true },
              inviter: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'user-uuid' },
                  name: { type: 'string', example: 'Admin Name' },
                  email: { type: 'string', example: '<EMAIL>' },
                },
              },
              organization: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'org-uuid' },
                  name: { type: 'string', example: 'Example Organization' },
                  logo: { type: 'string', example: 'https://example.com/logo.png', nullable: true },
                },
              },
              team: {
                type: 'object',
                nullable: true,
                properties: {
                  id: { type: 'string', example: 'team-uuid' },
                  name: { type: 'string', example: 'Development Team' },
                  description: { type: 'string', example: 'Team description' },
                },
              },
            },
          },
        },
        summary: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 10 },
            pending: { type: 'number', example: 5 },
            accepted: { type: 'number', example: 3 },
            expired: { type: 'number', example: 1 },
            cancelled: { type: 'number', example: 1 },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid organization ID or user not authorized',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'You can only view invitations from your own organization' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(HttpStatus.OK)
  async getInvitations(
    @Query() query: GetInvitationsDto,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.invitationsService.getInvitations({
      ...query,
      organizationId: user.orgId
    }, user.sub);
  }

  @Post('cancel')
  @ApiOperation({
    summary: 'Cancel a pending invitation',
    description: 'Cancels a pending invitation. Only admins can cancel invitations from their organization.',
  })
  @ApiResponse({
    status: 200,
    description: 'Invitation cancelled successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Invitation cancelled successfully' },
        invitation: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'invitation-uuid' },
            email: { type: 'string', example: '<EMAIL>' },
            status: { type: 'string', example: 'CANCELLED' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invitation cannot be cancelled',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Only pending invitations can be cancelled' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Invitation not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Invitation not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(HttpStatus.OK)
  async cancelInvitation(
    @Body() data: CancelInvitationDto,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.invitationsService.cancelInvitation(data, user.sub);
  }

  @Post('resend')
  @ApiOperation({
    summary: 'Resend an invitation',
    description: 'Resends a pending or expired invitation with a new token. Only admins can resend invitations from their organization.',
  })
  @ApiResponse({
    status: 200,
    description: 'Invitation resent successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Invitation resent successfully' },
        invitation: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'invitation-uuid' },
            email: { type: 'string', example: '<EMAIL>' },
            status: { type: 'string', example: 'PENDING' },
            expiresAt: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invitation cannot be resent',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Only pending or expired invitations can be resent' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Invitation not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Invitation not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(HttpStatus.OK)
  async resendInvitation(
    @Body() data: ResendInvitationDto,
    @GetCurrentUser() user: JwtPayload,
  ) {
    return this.invitationsService.resendInvitation(data, user.sub);
  }

  @Post('cleanup')
  @ApiOperation({
    summary: 'Clean up invitation data',
    description: 'Marks invitations as accepted for users who have already completed onboarding. This is useful for fixing any data inconsistencies.',
  })
  @ApiResponse({
    status: 200,
    description: 'Cleanup completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Cleanup completed successfully' },
        updated: { type: 'number', example: 5 },
      },
    },
  })
  @ApiBearerAuth()
  @UseGuards(AdminAuthGuard)
  @HttpCode(HttpStatus.OK)
  async cleanupInvitations(@GetCurrentUser() user: JwtPayload) {
    const currentUser = await this.invitationsService['prisma'].user.findUnique({
      where: { id: user.sub },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      throw new BadRequestException('User must be part of an organization');
    }

    const result = await this.invitationsService.cleanupOnboardedUserInvitations(
      currentUser.organizationId,
    );

    return {
      success: true,
      message: 'Cleanup completed successfully',
      updated: result.updated,
    };
  }
}
