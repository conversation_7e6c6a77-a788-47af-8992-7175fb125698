"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ConnectorProps } from '../types';
import {
    Loader2,
    CheckCircle2,
    MessageSquare,
    Power,
    ArrowRight,
    Shield,
    LogIn,
} from "lucide-react"
import Image from "next/image"
import { queryClient, createMutationFn, useApiMutation } from "@/lib/apiClient";
import { useRouter } from "next/navigation";
// import { useUI } from "@/contexts/UIContext";

interface SlackConnectionDetails {
    user: {
        name: string;
        email?: string;
        image?: string;
    };
    workspace: {
        name: string;
        domain: string;
        icon?: string;
    };
    channels: {
        total: number;
        channels: Array<{
            id: string;
            name: string;
            topic?: string;
            memberCount: number;
            isPrivate: boolean;
        }>;
    };
}

const LoadingSkeleton = () => (
    <div className="space-y-8 p-6 bg-card rounded-lg border shadow-sm dark:border-border animate-pulse">
        <div className="flex items-center justify-between">
            <div className="space-y-2">
                <div className="h-6 w-48 bg-muted rounded-md" />
                <div className="h-4 w-72 bg-muted rounded-md" />
            </div>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
            {[1, 2].map((i) => (
                <div key={i} className="space-y-6">
                    <div className="w-48 h-48 mx-auto bg-muted rounded-lg" />
                    <div className="space-y-2">
                        <div className="h-5 w-40 mx-auto bg-muted rounded-md" />
                        <div className="h-4 w-56 mx-auto bg-muted rounded-md" />
                    </div>
                </div>
            ))}
        </div>

        <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border flex justify-between items-center">
            <div className="flex items-center gap-4">
                <div className="h-6 w-6 bg-muted rounded-full" />
                <div className="h-4 w-64 bg-muted rounded-md" />
            </div>
            <div className="h-10 w-40 bg-muted rounded-md" />
        </div>
    </div>
)

export default function SlackConnector({ data }: ConnectorProps) {
    const router = useRouter()
    // const { setSelectedConnectors, setShowSelected } = useUI()
    // console.log(data)
    const [state, setState] = useState<"disconnected" | "connecting" | "connected">(
        data?.isConnected ? "connected" : "disconnected"
    )
    const [slackData, setSlackData] = useState<SlackConnectionDetails | null>(() => {
        if (data?.config?.metadata) {
            try {
                // Parse the metadata string into an object
                return JSON.parse(data.config.metadata)
            } catch (e) {
                console.error('Error parsing Slack metadata:', e)
                return null
            }
        }
        return null
    })
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [isDisconnecting, setIsDisconnecting] = useState(false)
    const isHandledRef = useRef(false)

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get("code");
        const state = urlParams.get("state");

        if (code && state && !isHandledRef.current) {
            isHandledRef.current = true;
            handleOAuthCallback(code, state);
        }

        return () => {
            // No need for cleanup as the ref persists between re-renders
        };
    }, []);

    const connectSlack = useApiMutation(
        createMutationFn.post('/slack/callback'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['slack'] });
                    try {
                        // Parse the metadata string from the response
                        const parsedData = JSON.parse(response.result.data.metadata)
                        setSlackData(parsedData)
                        setState("connected")
                        setError(null)
                        window.history.replaceState({}, document.title, window.location.pathname);
                    } catch (e) {
                        console.error('Error parsing Slack metadata:', e)
                        setError("Error processing server response")
                    }
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error setting up the request")
                }
                setState("disconnected")
            }
        }
    );

    const handleOAuthCallback = async (code: string, state: string) => {
        setState("connecting")
        try {
            await connectSlack.mutateAsync({ code, state })
        } finally {
            window.history.replaceState({}, document.title, window.location.pathname)
        }
    }

    const getAuthUrl = useApiMutation(
        createMutationFn.post('/slack/auth-url'),
        {
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['slack-auth-url'] });

                    router.push(response.result)
                    setError(null)
                } else {
                    setError("Unexpected response from server")
                }
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error setting up the request")
                }
            }
        }
    );

    const handleConnect = async () => {
        setIsLoading(true)
        await getAuthUrl.mutateAsync({})
        if (!getAuthUrl.isPending) {
            setIsLoading(false)
        }
    }

    const disconnectSlack = useApiMutation(
        createMutationFn.post('/slack/disconnect'),
        {
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['slack'] });
                setState("disconnected")
                setSlackData(null)
                setError(null)
                setIsDisconnecting(false)
            },
            onError: (error: any) => {
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong while disconnecting."
                    setError(errorMessage)
                } else if (error.request) {
                    setError("No response received from server")
                } else {
                    setError("Error disconnecting from Slack")
                }
                setIsDisconnecting(false)
            }
        }
    );

    const handleDisconnect = async () => {
        setIsDisconnecting(true)
        await disconnectSlack.mutateAsync({})
    }

    const handleStartChatting = () => {
        // setSelectedConnectors(prev => ["Slack"]);
        // setShowSelected(true);
        router.push('/chat');
    };

    if (state === "connecting") {
        return (
            <div className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Connecting to Slack...</span>
            </div>
        )
    }

    if (state === "connected" && slackData) {
        return (
            <div className="space-y-4 p-4 bg-card rounded-lg border shadow-sm dark:border-border">
                <div className="flex items-center justify-between bg-muted/50 p-3 rounded-lg dark:bg-muted/10">
                    <div className="flex items-center gap-3">
                        {slackData.workspace.icon ? (
                            <Image
                                src={slackData.workspace.icon}
                                alt={slackData.workspace.name}
                                width={40}
                                height={40}
                                className="rounded-md"
                            />
                        ) : (
                            <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                                <MessageSquare className="w-6 h-6 text-primary" />
                            </div>
                        )}
                        <div>
                            <div className="flex items-center gap-2">
                                <h3 className="font-medium">{slackData.workspace.name}</h3>
                                <span className="text-xs text-muted-foreground">({slackData.workspace.domain}.slack.com)</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <span>Connected as</span>
                                {slackData.user.image && (
                                    <Image
                                        src={slackData.user.image}
                                        alt={slackData.user.name}
                                        width={20}
                                        height={20}
                                        className="rounded-full"
                                    />
                                )}
                                <span className="font-medium text-foreground">{slackData.user.name}</span>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 bg-green-500/10 text-green-500 px-2 py-1 rounded-md">
                            <CheckCircle2 className="h-4 w-4" />
                            <span className="text-xs font-medium">Connected</span>
                        </div>
                    </div>
                </div>

                <div className="px-3 py-2 bg-muted/30 rounded-lg dark:bg-muted/10">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <MessageSquare className="w-4 h-4" />
                        <span>{slackData.channels.total} channels connected</span>
                        <div className="flex items-center gap-1 text-xs">
                            <span>•</span>
                            <span>{slackData.channels.channels.filter(c => c.isPrivate).length} private</span>
                            <span>•</span>
                            <span>{slackData.channels.channels.filter(c => !c.isPrivate).length} public</span>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col md:flex-row items-center gap-8 w-full p-6 rounded-xl hover:bg-muted/50 transition-colors border border-primary/10 dark:border-primary/20">
                    <div className="relative w-28 h-28 flex-shrink-0 flex items-center justify-center">
                        <div className="absolute inset-0 bg-primary/5 dark:bg-primary/10 rounded-full blur-xl" />
                        <Image
                            src="/new-logo.svg"
                            alt="AI Logo"
                            width={70}
                            height={70}
                            className="relative"
                        />
                    </div>
                    <div className="flex-grow text-center md:text-left">
                        <h3 className="font-semibold text-lg bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
                            Continue to Chat
                        </h3>
                        <div className="space-y-2 text-sm text-muted-foreground mt-3">
                            <p>✨ Search and analyze messages across all channels</p>
                            <p>🤖 Get AI-powered insights from your conversations</p>
                            <p>📊 Generate summaries of channel discussions</p>
                        </div>
                    </div>
                    <div 
                        onClick={handleStartChatting}
                        className="flex-shrink-0 w-full md:w-auto cursor-pointer"
                    >
                        <Button className="group bg-primary text-primary-foreground hover:bg-primary/90 w-full md:w-auto">
                            Start Chatting
                            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                    </div>
                </div>

                <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border border-primary/10 dark:border-primary/20 flex justify-between items-center">
                    <div className="flex items-center gap-4">
                        <Shield className="h-6 w-6 text-primary" />
                        <p className="text-sm">
                            <span className="font-medium">Secure & Private:</span>
                            {" "}Your data stays protected with real-time processing
                        </p>
                    </div>
                    <Button
                        onClick={handleDisconnect}
                        variant="outline"
                        size="sm"
                        className="hover:bg-destructive hover:text-destructive-foreground transition-colors"
                        disabled={isDisconnecting}
                    >
                        {isDisconnecting ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Disconnecting...
                            </>
                        ) : (
                            <>
                                <Power className="w-4 h-4 mr-2" />
                                Disconnect
                            </>
                        )}
                    </Button>
                </div>

                {/* <div className="flex justify-end items-center mt-4">
                    <Button
                        onClick={handleDisconnect}
                        variant="outline"
                        className="hover:bg-destructive hover:text-destructive-foreground"
                        disabled={isDisconnecting}
                    >
                        {isDisconnecting ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Disconnecting...
                            </>
                        ) : (
                            <>
                                <Power className="w-4 h-4 mr-2" />
                                Disconnect
                            </>
                        )}
                    </Button>
                </div> */}
            </div>
        )
    }

    if (isLoading) {
        return <LoadingSkeleton />
    }

    return (
        <div className="space-y-8 p-6 bg-card rounded-lg border shadow-sm dark:border-border">
            <div className="flex items-center justify-between">
                <div className="space-y-2">
                    <h3 className="font-semibold text-lg">Connect Slack</h3>
                    <p className="text-sm text-muted-foreground">Access your Slack messages with AI-powered insights</p>
                </div>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
                <div className="relative">
                    <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl" />
                    <div className="relative space-y-6">
                        <img
                            src="/message.svg"
                            alt="Search Messages"
                            className="w-48 h-48 mx-auto"
                        />
                        <div className="space-y-2">
                            <h3 className="font-semibold text-center">Smart Message Search</h3>
                            <p className="text-sm text-muted-foreground text-center">
                                Find any information across your Slack workspace instantly
                            </p>
                        </div>
                    </div>
                </div>

                <div className="relative">
                    <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl" />
                    <div className="relative space-y-6">
                        <Image
                            src="/new-logo.svg"
                            alt="AI Logo"
                            width={60}
                            height={60}
                            className="mx-auto w-48 h-48"
                        />
                        <div className="space-y-2">
                            <h3 className="font-semibold text-center">AIDE - Message Intelligence</h3>
                            <p className="text-sm text-muted-foreground text-center">
                                Get instant insights and summaries from your messages
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {error && (
                <div className="bg-destructive/10 text-destructive dark:bg-destructive/20 p-3 rounded-md text-sm">
                    {error}
                </div>
            )}

            <div className="bg-muted/30 dark:bg-muted/10 p-4 rounded-lg border border-primary/10 dark:border-primary/20 flex justify-between items-center">
                <div className="flex items-center gap-4">
                    <Shield className="h-6 w-6 text-primary" />
                    <p className="text-sm">
                        <span className="font-medium">Secure & Private:</span>
                        {" "}Your data stays protected with real-time processing
                    </p>
                </div>
                <Button
                    onClick={handleConnect}
                    className="bg-primary dark:text-white"
                    disabled={getAuthUrl.isPending}
                >
                    {getAuthUrl.isPending ? (
                        <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Connecting...
                        </>
                    ) : (
                        <>
                            <LogIn className="w-4 h-4 mr-2" />
                            Connect Slack
                        </>
                    )}
                </Button>
            </div>


        </div>
    )
}

