import { PrismaService } from '@/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UsageService {
  constructor(private prisma: PrismaService) {}

  async getCurrentPlan(userId: string) {
    const user = await this.prisma.user.findFirst({
      where: {
        id: userId,
      },
      include: {
        plan: true,
      },
    });
    return user.plan;
  }
}
