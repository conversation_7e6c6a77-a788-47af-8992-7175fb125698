import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  Headers,
  HttpCode,
  UseGuards,
  HttpStatus,
  Query,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { OneDriveService } from './onedrive.v1.service';
import {
  CrawlOneDriveDto,
  OneDriveCallbackDto,
  OneDriveRefreshTokenDto,
} from './dtos/onedrive.dto';

@ApiTags('OneDrive-V1')
@Controller({ version: '1', path: 'onedrive' })
export class OneDriveController {
  constructor(private oneDriveService: OneDriveService) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate OneDrive Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated OneDrive Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.oneDriveService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle OneDrive Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed OneDrive Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: OneDriveCallbackDto) {
    return this.oneDriveService.handleCallback(body.code, body.state);
  }

  @Post('/crawl')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Crawl Notion' })
  @ApiResponse({
    status: 200,
    description: 'Crawls a notion account',
  })
  async crawlOneDrive(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: CrawlOneDriveDto,
  ) {
    return this.oneDriveService.crawlOneDrive(user.sub, data);
  }

  @Post('refresh-token')
  @ApiOperation({ summary: 'Refresh OneDrive Auth Token' })
  @ApiResponse({
    status: 200,
    description: 'Successfully refreshed OneDrive Auth Token',
  })
  async refreshToken(
    @Body() body: OneDriveRefreshTokenDto,
    // @Headers('x-signature') signature: string,
  ) {
    // const isValid = this.webhookService.verifySignature(
    //   JSON.stringify(body),
    //   signature,
    // );

    // if (!isValid) {
    //   throw new UnauthorizedException('Invalid signature');
    // }
    return this.oneDriveService.refreshToken(body.refreshToken, body.userId);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect OneDrive Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected OneDrive Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.oneDriveService.disconnect(user.sub);
  }
}
