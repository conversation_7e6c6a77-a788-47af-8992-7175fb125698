import { Controller, Post, Get, Body, Param, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SupportV1Service } from './support.v1.service';
import { CreateSupportTicketDto, SupportTicketResponseDto } from '../dtos/support.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';

@ApiTags('Support')
@Controller({
    version: '1',
    path: 'support'
})
export class SupportV1Controller {
    constructor(private readonly supportService: SupportV1Service) { }

    @Post()
    @ApiOperation({ summary: 'Create a new support ticket' })
    @ApiResponse({
        status: 201,
        description: 'Support ticket created successfully',
        type: SupportTicketResponseDto
    })
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth()
    async createSupportTicket(@Body() dto: CreateSupportTicketDto, @GetCurrentUser() user: JwtPayload) {
        return this.supportService.createSupportTicket(dto, user.sub);
    }

    @Get()
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Get all support tickets for authenticated user' })
    @ApiResponse({
        status: 200,
        description: 'Returns all support tickets',
        type: [SupportTicketResponseDto]
    })
    async getUserSupportTickets(@Request() req) {
        return this.supportService.getSupportTickets(req.user.id);
    }

    @Get(':id')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Get a specific support ticket by ID' })
    @ApiResponse({
        status: 200,
        description: 'Returns the support ticket',
        type: SupportTicketResponseDto
    })
    async getSupportTicket(@Param('id') id: string) {
        return this.supportService.getSupportTicketById(id);
    }
}
