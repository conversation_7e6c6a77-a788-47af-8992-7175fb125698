export const teamInvitationEmail = (teamName: string, organizationName: string, url: string) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                margin: 0;
                padding: 0;
                background-color: #f6f9fc;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            }

            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 40px 20px;
            }

            .card {
                background-color: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .title {
                color: #1a1a1a;
                font-size: 24px;
                margin-bottom: 24px;
                text-align: center;
            }

            .text {
                color: #4a5568;
                font-size: 16px;
                line-height: 24px;
                margin-bottom: 32px;
            }

            .button {
                display: block;
                background-color: #4f46e5;
                color: white;
                text-decoration: none;
                padding: 12px 24px;
                border-radius: 4px;
                text-align: center;
                font-weight: 500;
                margin: 32px auto;
                max-width: 200px;
            }

            .footer {
                margin-top: 32px;
                text-align: center;
                color: #718096;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="card">
                <h1 class="title">You've been invited to join a team!</h1>
                
                <div class="text">
                    <p>You've been invited to join the <strong>${teamName}</strong> team at <strong>${organizationName}</strong>.</p>
                    
                    <p>Click the button below to accept the invitation and set up your account:</p>
                </div>
                
                <a href="${url}" class="button">Accept Invitation</a>
                
                <div class="text">
                    <p>If you didn't expect this invitation, you can safely ignore this email.</p>
                </div>
                
                <div class="footer">
                    <p>This invitation will expire in 7 days.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
};
