import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { ConfidentialClientApplication, LogLevel } from '@azure/msal-node';
import { PrismaService } from 'src/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { ConnectorsService } from '../../../v1/connectors.v1.service';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { OAuthConnectorService } from 'src/connectors/interfaces/oauth.interface';
import { CrawlOneDriveDto } from './dtos/onedrive.dto';
import { EtlStatus } from '@prisma/client';
import { EtlService } from '@/etl/etl.service';

@Injectable()
export class OneDriveService extends OAuthConnectorService {
  private oauth2Client: ConfidentialClientApplication;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
    private etlService: EtlService,
  ) {
    super();
    this.oauth2Client = new ConfidentialClientApplication({
      auth: {
        clientId: this.configService.get<string>('auth.microsoft.clientId'),
        authority: `https://login.microsoftonline.com/common`,
        clientSecret: this.configService.get<string>(
          'auth.microsoft.clientSecret',
        ),
      },
      system: {
        loggerOptions: {
          loggerCallback(loglevel, message, containsPii) {
            console.log(message);
          },
          piiLoggingEnabled: false,
          logLevel: LogLevel.Verbose,
        },
      },
    });
  }

  async generateRedirectUrl(userId: string) {
    const scopes = ['user.read', 'files.read.all', 'offline_access'];
    const redirectUri = 'http://localhost:3000/connectors/onedrive';
    const authCodeUrl = await this.oauth2Client.getAuthCodeUrl({
      scopes: scopes,
      redirectUri: redirectUri,
      state: userId,
      prompt: 'consent',
    });

    return authCodeUrl;
  }

  async handleCallback(code: string, state: string) {
    try {
      const scopes = ['user.read', 'files.read.all', 'offline_access'];
      const redirectUri = 'http://localhost:3000/connectors/onedrive';
      const tokenResponse = await this.oauth2Client.acquireTokenByCode({
        code: code,
        scopes: scopes,
        redirectUri: redirectUri,
      });

      console.log('Token Response:', tokenResponse);

      if (!tokenResponse?.accessToken) {
        throw new BadRequestException(
          'No access token received from Microsoft',
        );
      }

      const tokenCache = this.oauth2Client.getTokenCache().serialize();
      const refreshTokenObject = JSON.parse(tokenCache).RefreshToken;
      const refresh_token =
        refreshTokenObject[Object.keys(refreshTokenObject)[0]]?.secret;

      if (!refresh_token) {
        throw new BadRequestException(
          'No refresh token received from Microsoft',
        );
      }

      const access_token = tokenResponse.accessToken;
      const expiry_date = tokenResponse.expiresOn.getTime();
      const userId = state;

      const response = await fetch('https://graph.microsoft.com/v1.0/me', {
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });
      const userInfo = await response.json();
      console.log(userInfo);
      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'One Drive',
        },
        select: {
          id: true,
          type: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('OneDrive connector not found');
      }

      await this.prisma.userConnectors.create({
        data: {
          userId,
          connectorId: connector.id,
          config: {
            access_token,
            refresh_token,
            expiry_date,
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(access_token);
      const { encryptedData: refreshTokenEncrypted, iv: refreshTokenIv } =
        this.connectorsService.encrypt(refresh_token);

      const userConnectorDetails = {
        user_id: userId,
        connector_id: connector.id,
        connector_type: connector.type,
        name: 'One Drive',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
          refresh_token: refreshTokenEncrypted,
          refresh_token_iv: refreshTokenIv,
          expiry_date: expiry_date,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        userConnectorDetails,
      );

      return {
        success: true,
        message: 'OneDrive integration connected successfully',
        data: {
          email: userInfo.email,
          name: userInfo.displayName,
          picture: userInfo.photo,
        },
      };
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Failed to process OneDrive callback');
    }
  }

  async crawlOneDrive(userId: string, data: CrawlOneDriveDto) {
    const { userConnectorId } = data;
    const etlRun = await this.prisma.etl.findFirst({
      where: {
        userConnectorId: userConnectorId,
      },
    });

    if (etlRun && etlRun.status !== EtlStatus.FAILED) {
      throw new BadRequestException('ETL in progress!');
    }

    if (etlRun && etlRun.status === EtlStatus.FAILED) {
      await this.prisma.etl.delete({
        where: {
          id: etlRun.id,
        },
      });
    }

    // const etlResponse = await this.etlService.publish(
    //   userId,
    //   ConnectorName.ONEDRIVE,
    //   userConnectorId,
    // );

    // await this.prisma.etl.create({
    //   data: {
    //     dagId: etlResponse.dagId,
    //     dagRunId: etlResponse.dagRunId,
    //     status: etlResponse.state,
    //     startDate: etlResponse.executionDate,
    //     userConnectorId: userConnectorId,
    //   },
    // });

    return {
      success: true,
      message: 'OneDrive crawl started successfully',
    };
  }

  async refreshToken(refreshToken: string, userId: string) {
    try {
      const scopes = ['user.read', 'files.read.all'];
      const { accessToken, expiresOn } =
        await this.oauth2Client.acquireTokenByRefreshToken({
          refreshToken,
          scopes,
        });

      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'One Drive',
          },
        },
      );

      if (!userConnectors?.[0]) {
        throw new NotFoundException('User connector not found');
      }

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(accessToken);

      const updatedUserConnector = {
        ...userConnectors[0],
        updated_at: new Date().toISOString(),
        credentials: {
          ...userConnectors[0].credentials,
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
          expiry_date: expiresOn.getTime(),
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        updatedUserConnector,
      );

      return {
        accessToken,
        expiresAt: expiresOn.getTime(),
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      if (error.errorCode === 'invalid_grant') {
        throw new BadRequestException(
          'Refresh token is invalid or has expired. User needs to re-authenticate.',
        );
      }

      throw new BadRequestException(
        `Failed to refresh access token: ${error.message}`,
      );
    }
  }

  async disconnect(userId: string) {
    try {
      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'One Drive',
        },
        select: {
          id: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('OneDrive connector not found');
      }

      const userConnectors = await this.dynamoDBService.query(
        this.dynamoDBTableName,
        {
          keyConditionExpression:
            'user_id = :userId AND #connectorName = :name',
          expressionAttributeNames: {
            '#connectorName': 'name',
          },
          expressionAttributeValues: {
            ':userId': userId,
            ':name': 'One Drive',
          },
        },
      );

      const userConnector = userConnectors?.[0];
      if (!userConnector) {
        throw new NotFoundException(
          'No active OneDrive connection found for this user',
        );
      }

      await this.prisma.userConnectors.deleteMany({
        where: {
          userId,
          connectorId: connector.id,
        },
      });

      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'One Drive',
      });

      return {
        success: true,
        message: 'Successfully disconnected OneDrive integration',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to disconnect OneDrive integration: ${error.message}`,
      );
    }
  }
}
