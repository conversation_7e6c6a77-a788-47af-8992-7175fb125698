"use client";

import Image from 'next/image';
import { UserMessage as UserMessageType, Message } from '@/types/chat';
import { useUser } from '@/contexts/UserContext';

interface AssistantNameMessageProps {
    message: Message;
}

export default function AssistantNameMessage({ message }: AssistantNameMessageProps) {

    return (
        <div className='flex items-center gap-4 w-full max-w-3xl mx-auto'>
            <div className='h-[1px] bg-muted-foreground/30 flex-1'></div>
            <div className='rounded-lg'>
                <span className='text-sm text-muted-foreground'>
                    {message.message}
                </span>
            </div>
            <div className='h-[1px] bg-muted-foreground/30 flex-1'></div>
        </div>
    );
}