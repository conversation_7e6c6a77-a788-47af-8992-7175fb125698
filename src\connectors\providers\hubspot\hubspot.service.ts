import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseConnectorService } from 'src/connectors/interfaces/base.interface';
import { ConnectorsService } from 'src/connectors/v1/connectors.v1.service';
import { DynamoDBService } from 'src/dynamodb/dynamodb.service';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class HubspotService extends BaseConnectorService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private connectorsService: ConnectorsService,
    private dynamoDBService: DynamoDBService,
  ) {
    super();
  }

  async generateRedirectUrl(userId: string): Promise<string | { url: string }> {
    const baseUrl = 'https://app.hubspot.com/oauth/authorize';
    const clientId = this.configService.get('hubspot.clientId');
    const scopes = [
      'crm.objects.owners.read',
      'crm.objects.contacts.read',
      'crm.objects.companies.read',
      'crm.objects.deals.read',
    ];
    const redirectUri = this.configService.get('hubspot.redirectUri');

    const authUrl = `${baseUrl}?client_id=${clientId}&scope=${encodeURIComponent(scopes.join(' '))}&redirect_uri=${encodeURIComponent(redirectUri)}&state=${userId}`;
    return authUrl;
  }

  async handleCallback(code: string, state: string) {
    try {
      const tokenResponse = await fetch(
        'https://api.hubapi.com/oauth/v1/token',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            grant_type: 'authorization_code',
            client_id: this.configService.get('hubspot.clientId'),
            client_secret: this.configService.get('hubspot.clientSecret'),
            redirect_uri: this.configService.get('hubspot.redirectUri'),
            code: code,
          }),
        },
      );

      const tokenData = await tokenResponse.json();
      if (!tokenData.access_token) {
        throw new Error('Failed to obtain access token');
      }

      const accountDetails = await this.getAccountDetails(
        tokenData.access_token,
      );

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'HubSpot',
        },
        select: {
          id: true,
          type: true,
        },
      });

      if (!connector) {
        throw new NotFoundException('HubSpot connector not found');
      }

      await this.prisma.userConnectors.create({
        data: {
          userId: state,
          connectorId: connector.id,
          config: {
            name: accountDetails.name,
            domain: accountDetails.domain,
            metadata: JSON.stringify(accountDetails),
          },
        },
      });

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(tokenData.access_token);
      const { encryptedData: refreshTokenEncrypted, iv: refreshTokenIv } =
        this.connectorsService.encrypt(tokenData.refresh_token);

      const userConnectorDetails = {
        user_id: state,
        connector_id: connector.id,
        connector_type: connector.type,
        name: 'HubSpot',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        credentials: {
          access_token: accessTokenEncrypted,
          access_token_iv: accessTokenIv,
          refresh_token: refreshTokenEncrypted,
          refresh_token_iv: refreshTokenIv,
          expires_in: tokenData.expires_in,
        },
      };

      await this.dynamoDBService.put(
        this.dynamoDBTableName,
        userConnectorDetails,
      );

      return {
        success: true,
        message: 'HubSpot integration connected successfully',
        data: {
          name: accountDetails.name,
          domain: accountDetails.domain,
          metadata: JSON.stringify(accountDetails),
        },
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to process HubSpot callback: ${error.message}`,
      );
    }
  }

  private async getAccountDetails(accessToken: string) {
    try {
      const response = await fetch(
        'https://api.hubapi.com/account-info/v3/details',
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error('Failed to fetch HubSpot account details');
      }

      const data = await response.json();
      return {
        name: data.name,
        domain: data.domain,
        currency: data.currency,
        timeZone: data.timeZone,
        portalId: data.portalId,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to fetch HubSpot account details: ${error.message}`,
      );
    }
  }

  async disconnect(userId: string) {
    try {
      const userConnector = await this.dynamoDBService.get(
        this.dynamoDBTableName,
        {
          user_id: userId,
          name: 'HubSpot',
        },
      );

      if (!userConnector) {
        throw new NotFoundException('HubSpot connector not found');
      }

      const decryptedAccessToken = this.connectorsService.decrypt(
        userConnector.credentials.access_token,
        userConnector.credentials.access_token_iv,
      );

      if (decryptedAccessToken) {
        try {
          await fetch('https://api.hubapi.com/oauth/v1/refresh-tokens/delete', {
            method: 'DELETE',
            headers: {
              Authorization: `Bearer ${decryptedAccessToken}`,
            },
          });
        } catch (error) {
          console.error('Failed to revoke HubSpot token:', error.message);
        }
      }

      await this.dynamoDBService.delete(this.dynamoDBTableName, {
        user_id: userId,
        name: 'HubSpot',
      });

      const connector = await this.prisma.connectors.findFirst({
        where: {
          name: 'HubSpot',
        },
        select: {
          id: true,
        },
      });

      if (connector) {
        await this.prisma.userConnectors.deleteMany({
          where: {
            userId,
            connectorId: connector.id,
          },
        });
      }
      return {
        success: true,
        message: 'HubSpot integration disconnected successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to disconnect HubSpot: ${error.message}`,
      );
    }
  }

  async getHubSpotAccessToken(userId: string) {
    const userConnector = await this.dynamoDBService.get(
      this.dynamoDBTableName,
      {
        user_id: userId,
        name: 'HubSpot',
      },
    );
    if (!userConnector) {
      throw new NotFoundException('HubSpot connector not found');
    }
    const decryptedAccessToken = this.connectorsService.decrypt(
      userConnector.credentials.access_token,
      userConnector.credentials.access_token_iv,
    );

    return decryptedAccessToken;
  }

  async refreshToken(userId: string) {
    try {
      const userConnector = await this.dynamoDBService.get(
        this.dynamoDBTableName,
        {
          user_id: userId,
          name: 'HubSpot',
        },
      );

      if (!userConnector) {
        throw new NotFoundException('HubSpot connector not found');
      }

      const decryptedRefreshToken = this.connectorsService.decrypt(
        userConnector.credentials.refresh_token,
        userConnector.credentials.refresh_token_iv,
      );

      const tokenResponse = await fetch(
        'https://api.hubapi.com/oauth/v1/token',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: this.configService.get('hubspot.clientId'),
            client_secret: this.configService.get('hubspot.clientSecret'),
            refresh_token: decryptedRefreshToken,
          }),
        },
      );

      const tokenData = await tokenResponse.json();
      if (!tokenData.access_token) {
        throw new Error('Failed to refresh access token');
      }

      const { encryptedData: accessTokenEncrypted, iv: accessTokenIv } =
        this.connectorsService.encrypt(tokenData.access_token);
      const { encryptedData: refreshTokenEncrypted, iv: refreshTokenIv } =
        this.connectorsService.encrypt(tokenData.refresh_token);

      await this.dynamoDBService.update(this.dynamoDBTableName, {
        key: {
          user_id: userId,
          name: 'HubSpot',
        },
        updateExpression:
          'SET credentials = :credentials, updated_at = :updatedAt',
        expressionAttributeValues: {
          ':credentials': {
            access_token: accessTokenEncrypted,
            access_token_iv: accessTokenIv,
            refresh_token: refreshTokenEncrypted,
            refresh_token_iv: refreshTokenIv,
            expires_in: tokenData.expires_in,
          },
          ':updatedAt': new Date().toISOString(),
        },
      });

      return decryptedRefreshToken;
    } catch (error) {
      throw new BadRequestException(
        `Failed to refresh HubSpot token: ${error.message}`,
      );
    }
  }
}
