import { Module } from '@nestjs/common';
import { UserService } from './v1/user.v1.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { UserController } from './v1/user.v1.controller';
import { EmailModule } from 'src/email/email.module';
import { AuthModule } from 'src/auth/auth.module';

@Module({
  imports: [PrismaModule, EmailModule, AuthModule],
  controllers: [UserController],
  providers: [UserService],
})
export class UserModule {}
