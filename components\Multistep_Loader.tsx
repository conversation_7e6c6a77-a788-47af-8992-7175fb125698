"use client"
import { AnimatePresence, motion } from "framer-motion"
import { useState, useEffect } from "react"

type LoadingState = {
    text: string
}

const LoaderCore = ({
    loadingState,
}: {
    loadingState: LoadingState
}) => {
    return (
        <motion.div
            key={loadingState.text}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
            className="flex items-center space-x-2"
        >
            <div className="w-4 h-4 rounded-full bg-blue-500 animate-pulse" />
            <span className="text-sm text-gray-700 dark:text-gray-300">{loadingState.text}</span>
        </motion.div>
    )
}

export const MultiStepLoader = ({
    loadingStates,
    loading,
    duration = 1500,
}: {
    loadingStates: LoadingState[]
    loading?: boolean
    duration?: number
}) => {
    const [currentStateIndex, setCurrentStateIndex] = useState(0)

    useEffect(() => {
        if (!loading) {
            setCurrentStateIndex(0)
            return
        }
        const interval = setInterval(() => {
            setCurrentStateIndex((prevIndex) => {
                if (prevIndex === loadingStates.length - 1) {
                    return prevIndex
                }
                return prevIndex + 1
            })
        }, duration)

        return () => {
            clearInterval(interval)
        }
    }, [loading, loadingStates.length, duration])

    if (!loading) return null

    return (
        <div className="w-full">
            <AnimatePresence mode="wait">
                <LoaderCore loadingState={loadingStates[currentStateIndex]} />
            </AnimatePresence>
        </div>
    )
}

