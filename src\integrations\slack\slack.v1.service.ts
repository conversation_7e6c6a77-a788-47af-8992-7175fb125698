import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DynamoDBService } from '../../dynamodb/dynamodb.service';

export interface SlackCredentials {
    team_id: string;
    user_id: string;
    team_name: string;
    access_token: string;
    bot_user_id: string;
    bot_access_token: string;
    scope: string;
    created_at: number;
}

@Injectable()
export class SlackService {
    private readonly SLACK_INTEGRATIONS_TABLE = 'Integrations';

    constructor(
        private configService: ConfigService,
        private dynamoDBService: DynamoDBService,
    ) { }

    getAuthUrl(userId: string): string {
        const scopes = [
            'app_mentions:read',
            'channels:history',
            'channels:read',
            'chat:write',
            'im:history',
            'im:read',
            'im:write',
            'users:read',
        ];
        const authUrl = `https://slack.com/oauth/v2/authorize?client_id=${this.configService.get(
            'slack.clientId',
        )}&user_scope=${encodeURIComponent(
            scopes.join(','),
        )}&redirect_uri=${encodeURIComponent(
            this.configService.get('slack.integrationRedirectUri'),
        )}&state=${userId}`;

        return authUrl;
    }


    async handleCallback(code: string, state: string): Promise<SlackCredentials> {
        try {
            const formData = new FormData();
            formData.append('code', code);
            formData.append('client_id', this.configService.get('slackIntegration.clientId'));
            formData.append(
                'client_secret',
                this.configService.get('slackIntegration.clientSecret'),
            );

            const tokenResponse = await fetch('https://slack.com/api/oauth.v2.access', {
                method: 'POST',
                body: formData,
            });

            const data = await tokenResponse.json();

            if (!data.ok) {
                throw new Error(`Slack OAuth error: ${data.error}`);
            }

            const { team, authed_user, access_token, bot_user_id, scope } = data;

            const slackCredentials: SlackCredentials = {
                team_id: team.id,
                user_id: authed_user.id,
                team_name: team.name,
                access_token,
                bot_user_id,
                bot_access_token: access_token,
                scope,
                created_at: Date.now(),
            };
            await this.saveCredentials(slackCredentials);

            return slackCredentials;
        } catch (error) {
            console.error('Error exchanging code for token:', error);
            throw error;
        }
    }

    async saveCredentials(credentials: SlackCredentials): Promise<void> {
        try {
            await this.dynamoDBService.put(this.SLACK_INTEGRATIONS_TABLE, credentials);
        } catch (error) {
            console.error('Error saving Slack credentials:', error);
            throw error;
        }
    }

    async deleteCredentials(teamId: string, userId: string): Promise<void> {
        try {
            await this.dynamoDBService.delete(this.SLACK_INTEGRATIONS_TABLE, {
                team_id: teamId,
                user_id: userId,
            });
        } catch (error) {
            console.error('Error deleting Slack credentials:', error);
            throw error;
        }
    }
}