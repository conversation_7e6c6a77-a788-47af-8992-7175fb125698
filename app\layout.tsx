import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Toaster } from 'sonner';
import NextTopLoader from 'nextjs-toploader';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '@/lib/apiClient';
import { siteMetadata } from '@/lib/metadata'

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  metadataBase: new URL('https://stepsai.co'),
  title: {
    default: siteMetadata.title,
    template: `%s | ${siteMetadata.title}`
  },
  description: siteMetadata.description,
  keywords: siteMetadata.keywords,
  openGraph: {
    siteName: siteMetadata.title,
    ...siteMetadata.openGraph,
  },
  alternates: {
    canonical: 'https://stepsai.co'
  },
  authors: [
    {
      name: 'StepsAI',
      url: 'https://stepsai.co',
    }
  ],
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      {
        url: '/ai-logo.svg',
        type: 'image/svg+xml',
      },
      {
        url: '/logo_edit_new.png',
        type: 'image/png',
      }
    ],
  },
  verification: {
    google: 'google-verification-code',
    yandex: 'yandex-verification-code'
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <QueryClientProvider client={queryClient}>
            <NextTopLoader
              color="#DE4D4D"
              initialPosition={0.08}
              crawlSpeed={200}
              height={3}
              crawl={true}
              showSpinner={false}
              easing="ease"
              speed={200}
              shadow="0 0 10px #DE4D4D,0 0 5px #DE4D4D"
            />
            {children}
            <Toaster richColors expand={true} />
        </QueryClientProvider>
      </body>
    </html>
  );
}
