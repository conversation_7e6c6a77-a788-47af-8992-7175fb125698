import React from 'react';

interface SpinnerProps {
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({
    size = 'md',
    className
}) => {
    const sizeClasses = {
        sm: 'h-4 w-4',
        md: 'h-6 w-6',
        lg: 'h-8 w-8'
    };

    return (
        <div
            className={`animate-spin rounded-full border-2 border-t-2 border-gray-200 border-t-blue-600 ${sizeClasses[size]} ${className}`}
        />
    );
};