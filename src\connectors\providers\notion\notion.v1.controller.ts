import { Controller, Post, Body, UseGuards, Get, Param } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { NotionService } from './notion.v1.service';
import { CrawlNotionDto, NotionCallbackDto } from './dtos/notion.dto';

@ApiTags('Notion-V1')
@Controller({ version: '1', path: 'notion' })
export class NotionController {
  constructor(private notionService: NotionService) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate Notion Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated Notion Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.notionService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle Notion Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed Notion Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: NotionCallbackDto) {
    return this.notionService.handleCallback(body.code, body.state);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect Notion Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected Notion Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.notionService.disconnect(user.sub);
  }

  @Post('/crawl')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Crawl Notion' })
  @ApiResponse({
    status: 200,
    description: 'Crawls a notion account',
  })
  async crawlNotion(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: CrawlNotionDto,
  ) {
    return this.notionService.crawlNotion(user.sub, data);
  }

  @Get('/crawl/status')
  @ApiOperation({ summary: 'Get crawl status' })
  @ApiResponse({
    status: 200,
    description: 'Successfully got crawl status',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async getCrawlStatus(
    @GetCurrentUser() user: JwtPayload,
    @Param('userConnectorId') userConnectorId: string,
  ) {
    return this.notionService.getCrawlStatus(user.sub, userConnectorId);
  }

  @Get('/user-configs')
  @ApiOperation({ summary: 'Get Notion User Configs' })
  @ApiResponse({
    status: 200,
    description: 'Successfully got Notion User Configs',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async getNotionUserConfigs(@GetCurrentUser() user: JwtPayload) {
    return this.notionService.getNotionUserConfigs(user.sub);
  }
}
