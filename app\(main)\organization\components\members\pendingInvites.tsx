'use client';

import { useState, useEffect } from 'react';
import { organizationApi, type Invitation } from '@/lib/api/organization';
import { useUser } from '@/contexts/UserContext';
import { toast } from 'sonner';
import { Clock, CheckCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import InvitationCard from './invitationCard';
import InvitationTableSkeleton from './invitationTableSkeleton';

interface PendingInvitesProps {
  organizationId: string;
}

export default function PendingInvites({ organizationId }: PendingInvitesProps) {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const { isAdmin } = useUser();

  useEffect(() => {
    if (organizationId) {
      loadInvitations();
    }
  }, [organizationId]);

  const loadInvitations = async () => {
    try {
      setIsLoading(true);
      const response = await organizationApi.getInvitations({
        // organizationId,
        status: 'PENDING'
      });

      const invitationsData = response.result.invitations;
      setInvitations(invitationsData);
    } catch (error) {
      console.error('Failed to load invitations:', error);
      toast.error('Failed to load pending invitations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    if (!isAdmin) {
      toast.error('Only administrators can cancel invitations');
      return;
    }

    setActionLoading(invitationId);
    try {
      await organizationApi.cancelInvitation({ invitationId });
      toast.success('Invitation cancelled successfully');
      loadInvitations(); // Refresh the list
    } catch (error) {
      console.error('Failed to cancel invitation:', error);
      toast.error('Failed to cancel invitation');
    } finally {
      setActionLoading(null);
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    if (!isAdmin) {
      toast.error('Only administrators can resend invitations');
      return;
    }

    setActionLoading(invitationId);
    try {
      await organizationApi.resendInvitation({ invitationId });
      toast.success('Invitation resent successfully');
      loadInvitations(); // Refresh the list
    } catch (error) {
      console.error('Failed to resend invitation:', error);
      toast.error('Failed to resend invitation');
    } finally {
      setActionLoading(null);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-4 w-20" />
        </div>
        <InvitationTableSkeleton />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Pending Invitations</h2>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4 text-orange-500" />
          <span>{invitations.length} pending</span>
        </div>
      </div>

      {invitations.length === 0 ? (
        <div className="border rounded-lg p-8">
          <div className="text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="p-3 bg-muted/50 rounded-full">
                <CheckCircle className="h-8 w-8 text-muted-foreground" />
              </div>
              <div>
                <p className="font-medium text-muted-foreground">No pending invitations</p>
                <p className="text-sm text-muted-foreground">
                  All invited users have either accepted or their invitations have expired.
                </p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <div className="space-y-0">
            {invitations.map((invitation, index) => (
              <div key={invitation.id} className={index !== invitations.length - 1 ? "border-b" : ""}>
                <InvitationCard
                  invitation={invitation}
                  onCancel={handleCancelInvitation}
                  onResend={handleResendInvitation}
                  isActionLoading={actionLoading === invitation.id}
                  canPerformActions={isAdmin}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}