import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { getServerSession } from "next-auth/next";
import Google<PERSON>rovider from "next-auth/providers/google";
import { JWT } from "next-auth/jwt";
interface AuthEnv {
    NEXT_AUTH_SECRET: string;
    NEXTAUTH_URL: string;
    API_URL: string;
}
const env: AuthEnv = {
    NEXT_AUTH_SECRET: process.env.NEXTAUTH_SECRET || process.env.NEXT_AUTH_SECRET || "",
    NEXTAUTH_URL: process.env.NEXTAUTH_URL || "",
    API_URL: process.env.NEXT_PUBLIC_API_URL || "",
};




const refreshToken = async (token: JWT) => {
    const res = await fetch(`${env.API_URL}/auth/refresh`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            refreshToken: token.refreshToken,
        }),
    });
    const data = await res.json();
    if (!data.status) {
        return Promise.reject({ message: data.message });
    }
    return { ...token, accessToken: data.accessToken, refreshToken: data.refreshToken };
}
export const authOptions: NextAuthOptions = {
    session: {
        strategy: "jwt",
        maxAge: 24 * 60 * 60 * 7,
    },
    secret: process.env.NEXTAUTH_SECRET || process.env.NEXT_AUTH_SECRET || "uQ+BdTfzYTSRKFFd2kHFBkAQGstiDOE7grUCFbsUi9w=",
    providers: [
        CredentialsProvider({
            id: "token-verify",
            name: "Token Verification",
            credentials: {
                token: { label: "Token", type: "text" },
            },
            authorize: async (credentials: any) => {
                const token = credentials.token;
                try {
                    const res = await fetch(`${env.API_URL}/users/verify-admin`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ token }),
                    });
                    const data = await res.json();

                    if (!data.status) {
                        return Promise.reject({ message: data.message || 'Verification failed' });
                    }

                    const tokenParts = token.split('.');
                    if (tokenParts.length !== 3) {
                        return Promise.reject({ message: 'Invalid token format' });
                    }

                    const payload = JSON.parse(atob(tokenParts[1]));

                    return Promise.resolve({
                        id: payload.sub || payload.email,
                        email: payload.email,
                        name: payload.name,
                        image: "https://e7.pngegg.com/pngimages/442/477/png-clipart-computer-icons-user-profile-avatar-profile-heroes-profile.png",
                        accessToken: token,
                        refreshToken: token,
                        hasOnboarded: true,
                        organizationId: payload.organizationId,
                        role: payload.role,
                        organization: payload.organization,
                        teams: payload.teams || [],
                        currentTeam: payload.currentTeam
                    });
                } catch (error: any) {
                    console.error('Token verification error:', error);
                    return Promise.reject({ message: error.message || 'Token verification failed' });
                }
            },
        }),
        CredentialsProvider({
            id: "login",
            name: "Login",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                mfaCode: { label: "MFA Code", type: "text", optional: true },
            },
            authorize: async (credentials: any) => {
                const email = credentials.email;
                const password = credentials.password || " ";
                try {
                    const res = await fetch(`${env.API_URL}/auth/login`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ email, password, code: credentials.mfaCode || null }),
                    });
                    const data = await res.json();
                    if (!data.status) {
                        return Promise.reject({ message: data.message });
                    }
                    const userData = data.result;

                    // Fetch user details including organization and teams
                    let userDetails = null;
                    try {
                        const userResponse = await fetch(`${env.API_URL}/user/me`, {
                            headers: {
                                'Authorization': `Bearer ${userData.token.accessToken}`
                            }
                        });
                        if (userResponse.ok) {
                            const userResult = await userResponse.json();
                            userDetails = userResult.result || userResult;
                        }
                    } catch (error) {
                        console.warn('Failed to fetch user details:', error);
                    }

                    const finalUser = {
                        id: userData.user.id,
                        email: userData.user.email,
                        name: userData.user.name,
                        image: userData.user.image || "https://e7.pngegg.com/pngimages/442/477/png-clipart-computer-icons-user-profile-avatar-profile-heroes-profile.png",
                        accessToken: userData.token.accessToken,
                        refreshToken: userData.token.refreshToken,
                        hasOnboarded: userData.user.hasOnboarded,
                        role: userDetails?.role || userData.user.role,
                        organizationId: userDetails?.organizationId || userData.user.organizationId,
                        organization: userDetails?.organization,
                        teams: userDetails?.teams || [],
                        currentTeam: userDetails?.currentTeam
                    };

                    return Promise.resolve(finalUser);
                } catch (error: any) {
                    return Promise.reject({ message: error.message });
                }
            },
        }),
        CredentialsProvider({
            id: "register",
            name: "Register",
            credentials: {
                name: { label: "Name", type: "text" },
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
            },
            authorize: async (credentials: any) => {
                const email = credentials.email;
                const password = credentials.password;
                const name = credentials.name;

                try {
                    const res = await fetch(`${env.API_URL}/auth/register`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ email, password, name }),
                    });
                    const data = await res.json();
                    if (!data.status) {
                        return Promise.reject({ message: data.message });
                    }
                    const userData = data.result;
                    return Promise.resolve({
                        id: userData.user.id,
                        email: userData.user.email,
                        name: userData.user.name,
                        image: userData.user.image || "https://e7.pngegg.com/pngimages/442/477/png-clipart-computer-icons-user-profile-avatar-profile-heroes-profile.png",
                        accessToken: userData.token.accessToken,
                        refreshToken: userData.token.refreshToken,
                        hasOnboarded: userData.user.hasOnboarded,
                    });
                } catch (error: any) {
                    return Promise.reject({ message: error.message });
                }
            },
        }),
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID as string,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
            authorization: {
                params: {
                    prompt: "consent",
                    access_type: "offline",
                    response_type: "code",
                },
            },
            async profile(profile) {
                try {
                    const res = await fetch(`${env.API_URL}/auth/google`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            name: profile.name,
                            email: profile.email,
                            profileImageUrl: profile.picture,
                        }),
                    });

                    const data = await res.json();

                    if (!data.status) {
                        return Promise.reject({ message: data.message });
                    }

                    const userData = data.result;
                    return Promise.resolve({
                        id: userData.user.id,
                        email: userData.user.email,
                        name: userData.user.name,
                        image: userData.user.image || "/logo.png",
                        accessToken: userData.token.accessToken,
                        refreshToken: userData.token.refreshToken,
                        hasOnboarded: userData.user.hasOnboarded,
                        role: userData.user.role || userData.role,
                        organizationId: userData.user.organizationId,
                        organization: userData.user.organization,
                        teams: userData.user.teams || [],
                        currentTeam: userData.user.currentTeam || null,
                        initialOnboardingCompleted: userData.user.initialOnboardingCompleted,
                    });
                } catch (error: any) {
                    return Promise.reject({ message: error.message });
                }
            },
        }),
    ],
    cookies: {
        sessionToken: {
            name: "stepsai_secure",
            options: {
                httpOnly: true,
                sameSite: "lax",
                path: "/",
            },
        },
    },
    callbacks: {
        async jwt({ token, user, account }) {
            if (account && user) {
                const newToken = {
                    ...token,
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    image: user.image,
                    accessToken: user.accessToken,
                    refreshToken: user.refreshToken,
                    hasOnboarded: user.hasOnboarded,
                    initialOnboardingCompleted: user.initialOnboardingCompleted,
                    role: user.role,
                    organizationId: user.organizationId,
                    organization: user.organization,
                    teams: user.teams,
                    currentTeam: user.currentTeam,
                    expires: Date.now() + 7 * 24 * 60 * 60 * 1000,
                };
                return newToken;
            }

            // Handle token expiry and refresh
            if (token.expires < Date.now()) {
                try {
                    const newToken = await refreshToken(token);
                    const refreshedToken = {
                        ...token,
                        accessToken: newToken.accessToken,
                        refreshToken: newToken.refreshToken,
                        expires: Date.now() + 7 * 24 * 60 * 60 * 1000,
                    };
                    return refreshedToken;
                } catch (error) {
                    console.error("Error refreshing token:", error);
                    return { ...token, error: "RefreshAccessTokenError" };
                }
            }

            return token;
        },
        async session({ session, token }) {
            session.user = {
                id: token.id,
                email: token.email,
                name: token.name,
                image: token.image,
                hasOnboarded: token.hasOnboarded,
                initialOnboardingCompleted: token.initialOnboardingCompleted,
                role: token.role,
                organizationId: token.organizationId,
                organization: token.organization,
                teams: token.teams,
                currentTeam: token.currentTeam,
                accessToken: token.accessToken,
                refreshToken: token.refreshToken,
            };
            session.expires = token.expires;
            return session;
        },
    },
    pages: {
        signIn: "/",
        error: "/error",
    },
};

export async function getSession() {
    return await getServerSession(authOptions);
}
