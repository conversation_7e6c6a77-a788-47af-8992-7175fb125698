"use client"

import * as React from "react"
import {
  BookOpen,
  Bot,
  Command,
  Frame,
  LifeBuoy,
  Map,
  PieChart,
  Send,
  Settings2,
  SquareTerminal,
  Moon,
  Sun,
  Slack,
  Database,
  Boxes,
  Network,
  Building,
  Info,
} from "lucide-react"
import { FaGoogleDrive } from "react-icons/fa";
import Image from "next/image";
import { GrOnedrive } from "react-icons/gr";
import { useState } from "react";
import { useTheme } from "next-themes";
import { Button } from "@/components/ui/button";

import { NavMain } from "@/components/Navigation/nav-main"
import { NavProjects } from "@/components/Navigation/nav-projects"
import { NavSecondary } from "@/components/Navigation/nav-secondary"
import { NavUser } from "@/components/Navigation/nav-user"
import Link from "next/link";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { LogoSwitcher } from "./logo-switcher";
import { useUser } from "@/contexts/UserContext";


export function AppSidebar({ user, ...props }: { user: any, props?: React.ComponentProps<typeof Sidebar> }) {

  const { user: contextUser } = useUser();

  const getNavItems = () => {
    const baseItems = [
      {
        title: "Chat",
        url: "/chat",
        icon: SquareTerminal,
        isActive: true,
        items: [
          {
            title: "History",
            url: "/chat/history",
          },
          {
            title: "Starred",
            url: "/chat/starred",
          },
        ],
      },
      {
        title: "Knowledge",
        url: "/knowledgebase",
        icon: Database,
        id: "knowledgebase",
        items: []
      },
      {
        title: "Connectors",
        url: "/connectors",
        icon: Boxes,
        items: []
      },
      {
        title: "Integrations",
        url: "/integrations",
        icon: Network,
        items: []
      },
      {
        title: "Settings",
        url: "/settings",
        icon: Settings2,
        items: []
      }
    ];

    // Add organization option if user has organizationId
    if (contextUser.organizationId) {
      baseItems.unshift({
        title: "Organization",
        url: "/organization",
        icon: Building,
        items: []
      });
    }

    return baseItems;
  };


  return (
    <>
      <Sidebar variant="inset" {...props} collapsible="icon" >
        <LogoSwitcher />
        <SidebarContent>
          {/* <NavMain items={data.navMain} /> */}
          <NavMain items={getNavItems()} />
          {/* <NavProjects integrations={data.integrations} /> */}
          {/* <NavSecondary
            items={[
              {
                title: theme === "light" ? "Dark mode" : "Light mode",
                url: "#",
                icon: theme === "light" ? Moon : Sun,
                onClick: () => setTheme(theme === "light" ? "dark" : "light"),
                id: "theme-toggle"
              },
              {
                title: "Support",
                url: "#",
                icon: LifeBuoy,
                onClick: () => setShowSupportModal(true)
              },
              {
                title: "Feedback",
                url: "#",
                icon: Send,
                onClick: () => setShowFeedbackModal(true)
              }
            ]}
            className="mt-auto"
          /> */}
        </SidebarContent>
        <SidebarFooter>
          <NavUser />
        </SidebarFooter>
      </Sidebar>
    </>
  )
}
