"use client";

import React, { useState } from 'react';
import {
    ShieldCheck,
    QrCode,
    Smartphone,
    AlertCircle,
    CheckCircle,
    XCircle
} from 'lucide-react';
import { Button } from "@steps-ai/ui";
import {
    useApiMutation,
    createMutationFn,
    queryClient
} from "@/lib/apiClient";
import { useApiQuery } from "@/lib/apiClient";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle
} from "@steps-ai/ui";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription
} from "@steps-ai/ui";
import { Spinner } from "@steps-ai/ui"; // Assuming you have a Spinner component
import { toast } from 'sonner';
import { Input } from '@steps-ai/ui';

export default function MultiFactorAuthForm({ email }: { email: string }) {
    // State management
    const [setupStep, setSetupStep] = useState<'instructions' | 'qr' | 'verify'>('instructions');
    const [verificationCode, setVerificationCode] = useState('');
    const [loading, setLoading] = useState(false);
    const [qrCode, setQrCode] = useState('');
    const [isSetupDialogOpen, setIsSetupDialogOpen] = useState(false);

    // Fetch MFA status
    const {
        data: mfaStatus,
        isLoading: isStatusLoading,
        isError: isStatusError,
    } = useApiQuery<any>(
        ['mfa-status', email],
        `/auth/checktwofa/${email}`
    );

    // Mutation hooks with proper loading and error states
    const enableTwoFactor = useApiMutation(
        createMutationFn.post('/user/enable-two-factor-auth'),
        {
            onSuccess: (response: any) => {
                if (response?.status) {
                    queryClient.invalidateQueries({ queryKey: ['mfa-status'] });
                    setQrCode(response.result.code);
                    setSetupStep('qr');
                }
                setLoading(false);
            },
            onError: (error: any) => {
                console.error('Enable 2FA error:', error);
                setLoading(false);
            }
        }
    );

    const verifyTwoFactor = useApiMutation(
        createMutationFn.post('/user/verify-two-factor-auth'),
        {
            onSuccess: (response: any) => {
                if (response?.status) {
                    queryClient.invalidateQueries({ queryKey: ['mfa-status'] });
                    if (response?.result) {
                        toast.success('Two-factor authentication code verified.');
                        setIsSetupDialogOpen(false);
                    } else {
                        toast.error('Invalid two-factor authentication code.');
                    }
                }
                setLoading(false);
            },
            onError: (error: any) => {
                toast.error('Error verifying two-factor authentication code.');
                setLoading(false);
            }
        }
    );

    const disableTwoFactor = useApiMutation(
        createMutationFn.post('/user/disable-two-factor-auth'),
        {
            onSuccess: (response: any) => {
                if (response?.status) {
                    queryClient.invalidateQueries({ queryKey: ['mfa-status'] });
                    toast.success('Two-factor authentication disabled.');
                }
                setLoading(false);
            },
            onError: (error: any) => {
                toast.error('Error disabling two-factor authentication.');
                setLoading(false);
            }
        }
    );
    const handleStartMfa = () => {
        setIsSetupDialogOpen(true);
        setSetupStep('instructions');
    };

    const handleEnableMfa = () => {
        setLoading(true);
        enableTwoFactor.mutate({});
        
    };

    const handleVerification = () => {
        setLoading(true);
        verifyTwoFactor.mutate({ code: verificationCode });
    };

    const handleDisableMfa = () => {
        setLoading(true);
        disableTwoFactor.mutate({});
    };

    if (isStatusLoading) {
        return (
            <div className="flex justify-center items-center h-32">
                <Spinner />
            </div>
        );
    }

    if (isStatusError) {
        return (
            <div className="text-red-500 flex items-center justify-center h-32">
                <XCircle className="mr-2" /> Unable to fetch MFA status
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader>
                    <div className="flex items-center space-x-2">
                        <ShieldCheck className="h-6 w-6 text-green-600" />
                        <CardTitle>Multi-Factor Authentication</CardTitle>
                    </div>
                    <CardDescription>
                        Add an extra layer of security to your account
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {!mfaStatus?.result ? (
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 text-yellow-600">
                                <AlertCircle className="h-5 w-5" />
                                <span>Two-Factor Authentication is Currently Disabled</span>
                            </div>
                            <Button
                                onClick={handleStartMfa}
                                disabled={loading}
                                className='bg-blue-700 hover:bg-blue-800'
                            >
                                {loading ? <Spinner size="sm" /> : 'Enable MFA'}
                            </Button>
                        </div>
                    ) : (
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 text-green-600">
                                <CheckCircle className="h-5 w-5" />
                                <span>Two-Factor Authentication is Enabled</span>
                            </div>
                            <Button
                                variant="destructive"
                                onClick={handleDisableMfa}
                                disabled={loading}
                            >
                                {loading ? <Spinner size="sm" /> : 'Disable MFA'}
                            </Button>
                        </div>
                    )}
                </CardContent>
            </Card>

            <Dialog
                open={isSetupDialogOpen}
                onOpenChange={setIsSetupDialogOpen}
            >
                <DialogContent className="max-w-md">
                    {setupStep === 'instructions' && (
                        <div>
                            <DialogHeader>
                                <DialogTitle>Set Up Two-Factor Authentication</DialogTitle>
                                <DialogDescription>
                                    Follow these steps to secure your account
                                </DialogDescription>
                            </DialogHeader>
                            <div className=" mt-4 space-y-6">
                                <div className='space-y-2'>
                                    <div className="flex items-center space-x-3">
                                        <Smartphone className="h-6 w-6 text-blue-500" />
                                        <span>Install an Authenticator App</span>
                                    </div>
                                    <div className="pl-9 text-sm text-muted-foreground">
                                        Download Google Authenticator, Authy, or Microsoft Authenticator
                                    </div>
                                </div>

                                <div className='space-y-2'>
                                    <div className="flex items-center space-x-3 ">
                                        <QrCode className="h-6 w-6 text-green-500" />
                                        <span>Scan QR Code</span>
                                    </div>
                                    <div className="pl-9 text-sm text-muted-foreground">
                                        Open your authenticator app and scan the QR code
                                    </div>

                                </div>

                                <Button
                                    className="w-full mt-4 bg-blue-700 hover:bg-blue-800"
                                    onClick={handleEnableMfa}
                                    disabled={loading}
                                >
                                    I'm Ready to Set Up
                                </Button>
                            </div>
                        </div>
                    )}

                    {setupStep === 'qr' && (
                        <div>
                            <DialogHeader>
                                <DialogTitle>Scan QR Code</DialogTitle>
                                <DialogDescription>
                                    Scan this QR code with your authenticator app
                                </DialogDescription>
                            </DialogHeader>
                            <div className="flex justify-center my-6">
                                <img
                                    src={qrCode}
                                    alt="QR Code"
                                    className="w-48 h-48"
                                />
                            </div>
                            <div className="space-y-2">
                                <p className="text-sm text-muted-foreground">
                                    Enter the code from your authenticator app
                                </p>
                                <Input
                                    type="text"
                                    value={verificationCode}
                                    onChange={(e) => setVerificationCode(e.target.value)}
                                    className="w-full p-2 border rounded "
                                    maxLength={6}
                                />
                                <Button
                                    className="w-full bg-blue-700 hover:bg-blue-800"
                                    onClick={handleVerification}
                                    disabled={loading}

                                >
                                    {loading ? "Verifying" : 'Verify Code'}
                                </Button>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}