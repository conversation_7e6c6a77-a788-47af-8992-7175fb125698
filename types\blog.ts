import { TypedObject } from '@portabletext/types';

export interface Author {
  name: string;
  image?: string;
  bio?: TypedObject | TypedObject[]; 
  social?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
}

export interface BlogSection {
  _key: string;
  _type: 'section';
  title?: string;
  content: any[]; 
  image?: {
    asset: {
      _ref: string;
      _type: string;
    };
    caption?: string;
    hotspot?: boolean;
  };
}

export interface Blog {
  _id: string;
  title: string;
  slug: {
    current: string;
  };
  author?: Author;
  mainImage?: {
    asset: {
      _ref: string;
      _type: string;
    };
    hotspot?: boolean;
    caption?: string;
  };
  categories?: { title: string }[];
  publishedAt: string;
  excerpt?: string;
  readingTime?: string;
  sections: BlogSection[];
  body?: any; 
  type?: 'blog' | 'product-update' | 'documentation';
  featured?: boolean;
  tags?: string[];
}

export interface BlogCardProps {
  blog: Pick<Blog, 'title' | 'publishedAt' | 'excerpt' | 'mainImage' | 'categories' | 'author' | 'readingTime'> & {
    description?: string;
  };
}