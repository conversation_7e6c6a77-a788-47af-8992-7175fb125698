import { Metadata } from "next";
import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import ChatLayout from "@/components/Layouts/ChatLayout";

export const metadata: Metadata = {
    title: "Chat",
    description: "Chat with AIDE",
};

export default async function Layout({
    children,
}: {
    children: React.ReactNode;
}) {
    const session = await getSession();
    if (!session) {
        redirect("/");
    }

    return (
        <ChatLayout>
            {children}
        </ChatLayout>
    );
}
