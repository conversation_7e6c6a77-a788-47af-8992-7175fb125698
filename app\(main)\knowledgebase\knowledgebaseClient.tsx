"use client";

import CreateKnowledgeBase from "@/components/Modal/CreateKnowledgeBase";
import { useState, useEffect, useMemo } from "react"
import KnowledgeBaseCards from "@/components/Cards/KnowledgeBaseCards";
import { Progress, Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@steps-ai/ui";
import { Database, ShareIcon } from 'lucide-react';
import { useApiQuery } from "@/lib/apiClient";
import SkeletonCard from "@/components/ui/skeletonCard";
import { Separator } from "@steps-ai/ui";
import { Button } from "@steps-ai/ui";
import { Sparkles } from "lucide-react";
import { Input } from "@steps-ai/ui";
import { Search } from "lucide-react";
import { LayoutGrid, TableIcon } from "lucide-react";
import { ScrollArea } from "@steps-ai/ui";
import { usePathname, useRouter } from "next/navigation";

export default function KnowledgeBaseClient() {
    const [activeTab, setActiveTab] = useState("private");
    const [viewMode, setViewMode] = useState<"grid" | "table">("grid");
    const [searchQuery, setSearchQuery] = useState("");

    const {
        data,
        isLoading,
        isError,
    } = useApiQuery<any>(['knowledgebases'], '/knowledgebase');

    const [knowledgebases, setKnowledgebases] = useState<any[]>([]);
    const [sharedKnowledgebases, setSharedKnowledgebases] = useState<any[]>([]);

    const pathname = usePathname();
    const router = useRouter();

    useEffect(() => {
        if (pathname?.startsWith("/share")) {
            setActiveTab("shared");
        } else {
            setActiveTab("private");
        }
    }, [pathname]);

    useEffect(() => {
        if (data) {
            setKnowledgebases(data.result.knowledgeBases || []);
            setSharedKnowledgebases(data.result.sharedKnowledgebases || []);
        }
    }, [data]);

    const filteredKnowledgeBases = useMemo(() => {
        let bases = [];
        switch (activeTab) {
            case "private":
                bases = knowledgebases;
                break;
            case "shared":
                bases = sharedKnowledgebases;
                break;
            case "all":
            default:
                bases = [...knowledgebases, ...sharedKnowledgebases];
        }

        // Apply search filter
        if (searchQuery.trim() === "") return bases;
        return bases.filter(kb =>
            kb.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (kb.description && kb.description.toLowerCase().includes(searchQuery.toLowerCase()))
        );
    }, [activeTab, knowledgebases, sharedKnowledgebases, searchQuery]);

    const renderSharedEmptyState = () => (
        <div className='items-center text-center justify-center w-full grid col-span-1 md:col-span-2 lg:col-span-3'>
            <div className='text-gray-700 mx-auto text-6xl'>
                <ShareIcon className="w-16 h-16 text-gray-400 mb-4" />
            </div>
            <h2 className='text-xl text-gray-800 font-semibold mt-2'>No shared knowledge bases yet..</h2>
            <p className="text-gray-500 mb-4">
                Start collaborating by sharing your knowledge bases with team members
            </p>
        </div>
    );

    const renderEmptyState = () => (
        <div className='items-center text-center justify-center w-full grid col-span-1 md:col-span-2 lg:col-span-3'>
            <div className='text-gray-700 dark:text-gray-200 mx-auto text-6xl'>
                <Database size={50} />
            </div>
            <h2 className='text-xl text-gray-800 dark:text-gray-200 font-semibold mt-2'>It's Quiet Here...</h2>
            <p className='text-gray-600 dark:text-gray-400 max-w-xl mb-5'>
                Seems like you haven't created any knowledge base yet.
                Knowledge bases are the source of truth for your team. Let's get started!
            </p>
            <CreateKnowledgeBase />
        </div>
    );

    const renderTableView = () => {
        return (
            <div className="w-full overflow-auto rounded-lg border border-border">
                <ScrollArea className="max-h-[60vh] w-full overflow-y-auto scroll-smooth custom-scrollbar">
                    <table className="w-full table-auto border-collapse">
                        <thead>
                            <tr className="bg-muted/50 border-b">
                                <th className="text-left px-4 py-3 font-medium text-muted-foreground">Name</th>
                                <th className="text-left px-4 py-3 font-medium text-muted-foreground hidden md:table-cell">Description</th>
                                <th className="text-left px-4 py-3 font-medium text-muted-foreground hidden md:table-cell">Files</th>
                                <th className="text-left px-4 py-3 font-medium text-muted-foreground hidden sm:table-cell">Status</th>
                                <th className="text-left px-4 py-3 font-medium text-muted-foreground hidden lg:table-cell">Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredKnowledgeBases.map((kb, index) => (
                                <tr key={kb.id}
                                    className="border-b hover:bg-muted/30 cursor-pointer transition-colors"
                                    onClick={() => window.location.href = `/knowledgebase/configuration?id=${kb.id}`}>
                                    <td className="px-4 py-3">
                                        <div className="flex items-center gap-3">
                                            <div className="bg-primary/10 p-2 rounded-md">
                                                <Database className="h-4 w-4 text-primary" />
                                            </div>
                                            <span className="font-medium">{kb.name}</span>
                                        </div>
                                    </td>
                                    <td className="px-4 py-3 hidden md:table-cell text-muted-foreground">
                                        {kb.description ? kb.description.substring(0, 50) + (kb.description.length > 50 ? '...' : '') : ''}
                                    </td>
                                    <td className="px-4 py-3 hidden md:table-cell text-muted-foreground">
                                        {kb.files.length}
                                    </td>
                                    <td className="px-4 py-3 hidden sm:table-cell">
                                        <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${kb.status === "SUCCESS" ? "bg-green-100 text-green-800" :
                                            kb.status === "PROCESSING" ? "bg-yellow-100 text-yellow-800" :
                                                "bg-gray-100 text-gray-800"
                                            }`}>
                                            {kb.status}
                                        </span>
                                    </td>
                                    <td className="px-4 py-3 hidden lg:table-cell text-muted-foreground">
                                        {new Date(kb.createdAt).toLocaleDateString()}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </ScrollArea>
            </div>
        );
    };

    return (
        <div className="px-6 sm:px-8 md:px-15 mx-2 py-5 rounded-lg space-y-8">
            <div className="flex flex-col md:flex-row justify-between items-start gap-6">
                <div>
                    <h2 className="text-4xl font-bold text-text-primary">
                        Knowledge bases
                    </h2>
                    <p className="text-text-secondary mt-2">Add knowledge sources to shape your knowledge base.</p>
                    <a
                        href="https://docs.stepsai.co"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:text-primary/90 inline-flex items-center mt-2 text-sm font-medium"
                    >
                        Learn how knowledge bases work →
                    </a>
                </div>
                <CreateKnowledgeBase />
            </div>
            <Separator className="my-4" />
            <Tabs value={activeTab} className="w-full" onValueChange={(val) => {
                setActiveTab(val);
            }}>
                <TabsList className="mb-4">
                    <TabsTrigger value="private">Private</TabsTrigger>
                    <TabsTrigger value="shared">Shared</TabsTrigger>
                </TabsList>
            </Tabs>
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div className="relative w-full sm:w-64 md:w-80">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search knowledge bases..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-9 w-full"
                    />
                </div>

                <div className="flex items-center gap-4 w-full sm:w-auto">

                    <div className="hidden sm:flex border rounded-md overflow-hidden">
                        <Button
                            variant={viewMode === "grid" ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setViewMode("grid")}
                            className="rounded-none border-0"
                        >
                            <LayoutGrid className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === "table" ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setViewMode("table")}
                            className="rounded-none border-0"
                        >
                            <TableIcon className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>

            <div className="w-full">
                {isLoading ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {[...Array(6)].map((_, index) => (
                            <div key={index}>
                                <SkeletonCard />
                            </div>
                        ))}
                    </div>
                ) : filteredKnowledgeBases.length === 0 ? (
                    activeTab === "shared" ? renderSharedEmptyState() : renderEmptyState()
                ) : (
                    viewMode === "grid" ? (
                        <KnowledgeBaseCards data={filteredKnowledgeBases} />
                    ) : (
                        renderTableView()
                    )
                )}
            </div>
        </div>
    )
}
