"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { File, HardDrive, Users, Loader2 } from 'lucide-react'
import { Button } from "@steps-ai/ui"
import { Card, CardContent, CardHeader, CardTitle } from "@steps-ai/ui"
import { Input } from "@steps-ai/ui"
import { Label } from "@steps-ai/ui"
import { Textarea } from "@/components/ui/textarea"
import { Skeleton } from "@steps-ai/ui"
import { formatFileSize } from "@/lib/utils"
import { toast } from "sonner"
import { useSearchParams } from "next/navigation"
import {
    useApiMutation,
    createMutationFn,
    queryClient
} from "@/lib/apiClient";
import { useRouter } from "next/navigation"


const StatCardSkeleton = () => (
    <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-4 w-4 rounded-full" />
        </CardHeader>
        <CardContent>
            <Skeleton className="h-8 w-3/4" />
        </CardContent>
    </Card>
)

export default function KnowledgebaseStats({
    data,
    isLoading
}: {
    data: any,
    isLoading: boolean
}) {
    const [name, setName] = useState("")
    const router = useRouter()
    const [description, setDescription] = useState("")
    const [size, setSize] = useState<number>(0);
    const searchParams = useSearchParams();
    const id = searchParams.get("id") as string;
    const [files, setFiles] = useState();
    const [members, setMembers] = useState();

    const [isSubmitting, setIsSubmitting] = useState(false)
    const [loading, setLoading] = useState(false)

    const updateKnowledgebase = useApiMutation(
        createMutationFn.put(`/knowledgebase/${id}`),
        {
            onSuccess: (response: any) => {
                setIsSubmitting(false)
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['updateknowledgebase'] });
                    toast.success("Knowledgebase updated successfully");
                } else {
                    toast.error("Something went wrong. Please try again.");
                }
            },
            onError: (error: any) => {
                setIsSubmitting(false)
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again.";
                    toast.error(errorMessage);
                } else if (error.request) {
                    toast.error("No response received from server");
                } else {
                    toast.error("Error setting up the request");
                }
            },
            onSettled: () => {
                setIsSubmitting(false);
            }
        }
    );
    const deleteKnowledgebase = useApiMutation(
        createMutationFn.delete(`/knowledgebase/${id}`),
        {
            onMutate: () => {
                toast.loading("Deleting knowledgebase...");
            },
            onSuccess: (response: any) => {
                if (response && response.status) {
                    queryClient.invalidateQueries({ queryKey: ['knowledgebases'] });
                    toast.dismiss();
                    toast.success("Knowledgebase deleted successfully");
                    router.push("/knowledgebase");
                    setLoading(false)
                } else {
                    setLoading(false)
                    toast.error("Something went wrong. Please try again.");
                }
            },
            onError: (error: any) => {
                toast.dismiss();
                if (error.response) {
                    const errorMessage = error.response.data?.message ||
                        "Something went wrong. Please try again.";
                    toast.error(errorMessage);
                    setLoading(false)
                } else if (error.request) {
                    toast.error("No response received from server");
                    setLoading(false)
                } else {
                    toast.error("Error setting up the request");
                    setLoading(false)
                }
            }
        }
    );
    useEffect(() => {
        if (data) {
            const totalSize = data.result.totalFileSize;
            const members = data.result.members.length;
            setFiles(data.result.files.length);
            setSize(totalSize);
            setMembers(members);
            setName(data.result.name);
            setDescription(data.result.description);

        }
    }, [data]);
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)
        updateKnowledgebase.mutate({ name, description });
    }

    const handleDelete = async () => {
        setLoading(true)
        deleteKnowledgebase.mutate({});

    }

    return (
        <div className="space-y-8 my-2">
            <div >
                <h2 className="text-text-secondary font-bold text-md mb-2">Overview</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mx-2 sm:mx-5">
                    {isLoading ? (
                        <>
                            <StatCardSkeleton />
                            <StatCardSkeleton />
                            <StatCardSkeleton />
                        </>
                    ) : (
                        <>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-text-primary">Total Files</CardTitle>
                                    <File className="h-4 w-4 text-text-tertiary" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-text-primary">{files}</div>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-text-primary">Total Storage</CardTitle>
                                    <HardDrive className="h-4 w-4 text-text-tertiary" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-text-primary">{formatFileSize(size)}</div>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-text-primary">Total Members</CardTitle>
                                    <Users className="h-4 w-4 text-text-tertiary" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-text-primary">{members}</div>
                                </CardContent>
                            </Card>
                        </>
                    )}
                </div>
            </div>

            <div>
                <h2 className="text-text-secondary font-bold text-md mb-2">Settings</h2>
                <div className="flex flex-col space-y-8 shadow-md p-2 sm:p-4 rounded-lg">
                    {isLoading ? (
                        <div className="space-y-4">
                            <div>
                                <Skeleton className="h-6 w-1/4 mb-2" />
                                <Skeleton className="h-10 w-full" />
                            </div>
                            <div>
                                <Skeleton className="h-6 w-1/4 mb-2" />
                                <Skeleton className="h-24 w-full" />
                            </div>
                            <Skeleton className="h-10 w-1/3" />
                        </div>
                    ) : (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="flex flex-col gap-2">
                                <Label htmlFor="name" className="text-text-secondary">Name</Label>
                                <Input
                                    id="name"
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    placeholder="Enter knowledgebase name"
                                    className="w-full"
                                />
                            </div>
                            <div>
                                <Label htmlFor="description" className="text-text-secondary">Description</Label>
                                <Textarea
                                    id="description"
                                    value={description}
                                    onChange={(e) => setDescription(e.target.value)}
                                    placeholder="Enter knowledgebase description"
                                />
                            </div>
                            <Button
                                type="submit"
                                className="bg-blue-600 hover:bg-blue-700"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Saving...
                                    </>
                                ) : (
                                    "Save Changes"
                                )}
                            </Button>
                        </form>
                    )}

                    {!isLoading && (
                        <Card className="border-red-600">
                            <CardHeader>
                                <CardTitle className="text-red-600">Danger Zone</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="mb-4 text-sm text-text-tertiary">
                                    Deleting the knowledgebase will permanently remove all associated data. This action cannot be undone.
                                </p>
                                <Button variant="destructive" onClick={handleDelete} disabled={isLoading}>
                                    {loading ? (
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    ) : (
                                        "Delete Knowledgebase"
                                    )}
                                </Button>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </div>
    )
}