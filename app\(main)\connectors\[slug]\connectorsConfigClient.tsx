"use client"

import { ConnectorLayout } from '@/components/Layouts/ConnectorLayout';
import { useApiQuery } from '@/lib/apiClient';
import { useEffect, useState } from 'react';

interface ConnectorData {
    status: boolean;
    statusCode: number;
    result: {
        id: string;
        name: string;
        logo: string;
        category: string;
        description: string;
        slug: string;
        status: string;
        type: string;
        createdAt: string;
        updatedAt: string;
        isConnected: boolean;
        connectionDetails: {
            status: string;
        };
    };
}

interface ConnectorProps {
    data: {
        id: string;
        name: string;
        logo: string;
        category: string;
        description: string;
        slug: string;
        status: string;
        type: string;
        isConnected: boolean;
        connectionDetails: {
            status: string;
        };
        config?: any;
    };
    loading?: boolean;
}

export default function ConnectorsClient({ params }: { params: { slug: string } }) {
    const {
        data,
        isLoading,
        isError,
    } = useApiQuery<ConnectorData>([`${params.slug}`], `/connectors/${params.slug}`);

    const [ConnectorComponent, setConnectorComponent] = useState<React.ComponentType<ConnectorProps> | null>(null);

    useEffect(() => {
        const loadConnectorComponent = async () => {
            try {
                const { getConnectorComponent } = await import('@/components/Connectors/registry');
                const component = getConnectorComponent(params.slug);
                if (component) {
                    setConnectorComponent(() => component);
                }
            } catch (error) {
                console.error('Error loading connector component:', error);
            }
        };

        loadConnectorComponent();
    }, [params.slug]);

    if (isError) {
        return <div>Error loading connector details</div>;
    }

    const getStatusStyles = () => {
        if (data?.result?.status === 'COMING_SOON') {
            return 'bg-blue-100 text-blue-800';
        }
        return data?.result?.isConnected
            ? 'bg-green-100 text-green-800'
            : 'bg-yellow-100 text-yellow-800';
    };

    const getStatusText = () => {
        if (data?.result?.status === 'COMING_SOON') {
            return 'Coming Soon';
        }
        return data?.result?.isConnected ? 'Connected' : 'Not Connected';
    };

    const renderContent = () => {
        if (data?.result?.status === 'COMING_SOON') {
            return (
                <div className="mt-6 rounded-lg border border-blue-800/20 bg-blue-950/10 p-6">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg
                                className="h-6 w-6 text-blue-400"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                            >
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <h3 className="text-base font-semibold text-blue-400">Coming Soon!</h3>
                            <div className="mt-2 text-sm text-blue-300/90">
                                <p>This connector is currently under development and will be available soon. Stay tuned for updates!</p>
                            </div>
                            <div className="mt-4">
                                <button className="inline-flex items-center text-sm font-medium text-blue-400 hover:text-blue-300 transition-colors">
                                    Get notified when available
                                    <svg className="ml-1 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        if (ConnectorComponent && data?.result) {
            return <ConnectorComponent data={data.result} loading={isLoading} />;
        }

        return (
            <div>
                <p className="text-sm text-muted-foreground">Configure your connector settings here</p>
            </div>
        );
    };

    const statusBadge = (
        <span className={`px-2 py-1 rounded-full text-xs ${getStatusStyles()}`}>
            {getStatusText()}
        </span>
    );

    return (
        <ConnectorLayout
            title={data?.result?.name || ''}
            description={data?.result?.description || ''}
            isLoading={isLoading}
            logo={data?.result?.logo}
            statusComponent={statusBadge}
        >
            <div className=" space-y-4">
                {renderContent()}
            </div>
        </ConnectorLayout>
    );
}