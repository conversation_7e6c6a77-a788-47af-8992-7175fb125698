import { motion } from "framer-motion";

export const ChatLoader = () => (
    <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 grid place-items-center bg-background/80 backdrop-blur-sm z-50"
    >
        <div className="flex flex-col items-center gap-4">
            <div className="relative">
                <div className="h-16 w-16 rounded-full border-4 border-muted-foreground/20 border-t-primary animate-spin" />
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="h-8 w-8 rounded-full border-4 border-muted-foreground/20 border-t-primary animate-spin" />
                </div>
            </div>
            <span className="text-sm text-muted-foreground">Loading chats...</span>
        </div>
    </motion.div>
);
