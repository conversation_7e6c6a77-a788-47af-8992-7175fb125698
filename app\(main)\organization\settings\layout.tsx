import { Metadata } from "next"
import { Separator } from "@/components/ui/separator"
import { SidebarNav } from "@/components/Navigation/settings-nav"

export const metadata: Metadata = {
    title: "Organization Settings",
}

const sidebarNavItems = [
    {
        title: "Account",
        href: "/organization/settings",
    },
    {
        title: "Organization",
        href: "/organization/settings/organization",
    }
]

interface OrganizationSettingsLayoutProps {
    children: React.ReactNode
}

export default function OrganizationSettingsLayout({ children }: OrganizationSettingsLayoutProps) {
    return (
        <div className="min-h-screen bg-background">
            <div className="container mx-auto px-4 py-6 lg:px-8 max-w-7xl">
                <div className="space-y-6">
                    <div className="space-y-1">
                        <h1 className="text-3xl font-bold tracking-tight text-foreground">Organization Settings</h1>
                        <p className="text-lg text-muted-foreground">
                            Manage your account and organization settings.
                        </p>
                    </div>
                    <Separator className="my-6" />
                    <div className="flex flex-col lg:flex-row gap-8">
                        <aside className="lg:w-64 flex-shrink-0">
                            <div className="sticky top-6">
                                <SidebarNav items={sidebarNavItems} />
                            </div>
                        </aside>
                        <main className="flex-1 min-w-0">
                            {children}
                        </main>
                    </div>
                </div>
            </div>
        </div>
    )
}
