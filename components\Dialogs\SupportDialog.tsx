import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@steps-ai/ui";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@steps-ai/ui";
import { Input } from "@steps-ai/ui";
import { Textarea } from "@steps-ai/ui";
import { useState } from "react";
import { feedbackClient } from "@/lib/feedbackClient";

interface SupportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function SupportDialog({ open, onOpenChange }: SupportDialogProps) {
  const [supportForm, setSupportForm] = useState({
    area: '',
    email: '',
    message: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await feedbackClient.submitSupport(
        supportForm.email,
        supportForm.message,
        supportForm.area
      );
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting support request:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Get Support</DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Fill out the form below and we'll get back to you as soon as possible.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4 mt-4">
            <div className="space-y-2">
              <label htmlFor="area" className="text-sm font-medium">Area</label>
              <Input
                id="area"
                placeholder="Area of concern"
                value={supportForm.area}
                onChange={(e) => setSupportForm({ ...supportForm, area: e.target.value })}
                className="w-full"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="support-email" className="text-sm font-medium">Email</label>
              <Input
                id="support-email"
                type="email"
                placeholder="<EMAIL>"
                value={supportForm.email}
                onChange={(e) => setSupportForm({ ...supportForm, email: e.target.value })}
                className="w-full"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">Description</label>
              <Textarea
                id="description"
                placeholder="Detailed description of your issue"
                value={supportForm.message}
                onChange={(e) => setSupportForm({ ...supportForm, message: e.target.value })}
                className="min-h-[100px] w-full"
                required
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button type="submit" className="bg-primary text-primary-foreground hover:bg-primary/90">
                Submit
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </motion.div>
    </Dialog>
  );
} 