import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class VerifyTeamInvitationDto {
  @ApiProperty({
    description: 'Team invitation token',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  token: string;
}

export class CompleteTeamInvitationDto {
  @ApiProperty({
    description: 'Team invitation token',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'User name',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'User password',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
