import { Inject, Injectable } from '@nestjs/common';
import {
  EmailProviderStrategy,
  EmailOptions,
} from './interface/email.interface';
import { welcomeEmail } from './templates/welcome';
import { emailVerification } from './templates/email-verification';
import { resetPasswordTemplate } from './templates/reset-password';
import { magicLinkEmailToOrgAdmin } from './templates/magiclink';
import {
  shareKnowledgebaseExistingUser,
  shareKnowledgebaseNewUser,
} from './templates/share-knowledgebase';
import { teamInvitationEmail } from './templates/team-invitation';
import { orgInvitationEmail } from './templates/org-invitation';
import { teamAdditionEmail } from './templates/team-addition';
import { teamLeaderAssignmentEmail } from './templates/team-leader-assignment';

@Injectable()
export class EmailService {
  constructor(
    @Inject('EMAIL_PROVIDER')
    private readonly emailProvider: EmailProviderStrategy,
  ) {}

  async sendEmail(options: EmailOptions) {
    return this.emailProvider.sendEmail(options);
  }

  async magicLinkEmailToOrgAdmin(email: string, name: string, url: string) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: 'Login to Aide',
      html: magicLinkEmailToOrgAdmin(name, url),
    });
  }
  async sendWelcomeEmail(email: string, name: string) {
    console.log(email, name);
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: 'Welcome to Aide',
      html: welcomeEmail({ name }),
    });
  }

  async sendVerificationEmail(email: string, name: string, token: string) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: 'Verify your email',
      html: emailVerification({ name, token }),
    });
  }
  async sendResetPasswordEmail(email: string, name: string, url: string) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: 'Reset your password',
      html: resetPasswordTemplate(name, url),
    });
  }

  async sendShareKnowledgebaseEmail(
    email: string,
    name: string,
    knowledgebaseName: string,
  ) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: 'New Knowledgebase Shared',
      html: shareKnowledgebaseExistingUser({ name, knowledgebaseName }),
    });
  }

  async sendShareKnowledgebaseNewUserEmail(
    email: string,
    name: string,
    knowledgebaseName: string,
    password: string,
  ) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: 'New Knowledgebase Shared',
      html: shareKnowledgebaseNewUser({
        name,
        knowledgebaseName,
        email,
        password,
      }),
    });
  }

  async sendTeamInvitationEmail(
    email: string,
    teamName: string,
    organizationName: string,
    invitationUrl: string,
  ) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: `Invitation to join ${teamName} team`,
      html: teamInvitationEmail(teamName, organizationName, invitationUrl),
    });
  }

  async sendOrgInvitationEmail(
    email: string,
    organizationName: string,
    invitationUrl: string,
  ) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: `Invitation to join ${organizationName}`,
      html: orgInvitationEmail(organizationName, invitationUrl),
    });
  }

  async sendTeamAdditionEmail(
    email: string,
    userName: string,
    teamName: string,
    organizationName: string,
  ) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: `You've been added to ${teamName} team`,
      html: teamAdditionEmail(userName, teamName, organizationName),
    });
  }

  async sendTeamLeaderAssignmentEmail(
    email: string,
    userName: string,
    teamName: string,
    organizationName: string,
  ) {
    return this.sendEmail({
      from: 'Aide <<EMAIL>>',
      to: [email],
      subject: `🎉 You're now the Team Leader of ${teamName}`,
      html: teamLeaderAssignmentEmail(userName, teamName, organizationName),
    });
  }
}
