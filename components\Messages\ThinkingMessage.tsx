"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { CheckCircle, CheckCircle2, Loader2, Database, Search } from "lucide-react"
import { MdOutlineWebhook } from "react-icons/md";

import { assets } from "@/lib/assets"
import Image from "next/image"
interface ThinkingVisualizerProps {
    events: any[]
    isComplete: boolean
}

export function ThinkingVisualizer({ events, isComplete }: ThinkingVisualizerProps) {
    const [expandedJson, setExpandedJson] = useState<boolean>(false)

    const getEventIcon = (type: string) => {
        switch (type) {
            case "thinking":
                return (
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    >
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" x2="12" y1="8" y2="12"></line>
                        <line x1="12" x2="12.01" y1="16" y2="16"></line>
                    </svg>
                )
            case "Gmail":
                return (
                    <Image src={assets.Icons.Gmail} alt="Gmail" className="w-4 h-4" width={16} height={16} />
                )
            case "Knowledge Base":
                return (
                    <Database className="w-4 h-4" />
                )
            case "Web Search":
                return (
                    <MdOutlineWebhook className="w-4 h-4" />
                )
            case "Google Calendar":
                return (
                    <Image src={assets.Icons["Google Calendar"]} alt="Google Calendar" className="w-4 h-4" width={16} height={16} />
                )
            case "Google Drive":
                return (
                    <Image src={assets.Icons.Google} alt="Google Drive" className="w-4 h-4" width={16} height={16} />
                )
            case "AIDE":
                return (
                    <Image src="/ai-logo.svg" alt="AIDE" className="w-4 h-4" width={16} height={16} />
                )
            case "DONE":
                return (
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    >
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                )
            default:
                return (
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    >
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" x2="12" y1="8" y2="12"></line>
                        <line x1="12" x2="12.01" y1="16" y2="16"></line>
                    </svg>
                )
        }
    }

    return (
        <div className="space-y-4 m-3 border border-border rounded-lg overflow-hidden p-2">
            <div className="flex items-center justify-between">
                <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                    className="relative"
                > <div className="flex flex-col">
                        <div className="flex items-center space-x-2">
                            <div
                                className={`h-6 w-6 rounded-full flex items-center justify-center ${events[events.length - 1]?.type === "DONE"
                                    ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
                                    : events[events.length - 1]?.type === "chat_message"
                                        ? "bg-violet-100 text-violet-600 dark:bg-violet-900/30 dark:text-violet-400"
                                        : "bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400"
                                    }`}
                            >
                                {getEventIcon(events[events.length - 1].type)}
                            </div>
                            <span className="font-medium text-sm">{events[events.length - 1].type}</span>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1 ml-8">{events[events.length - 1].text}</p>
                    </div>
                </motion.div>
                <button
                    onClick={() => setExpandedJson(!expandedJson)}
                    className="text-xs text-indigo-600 dark:text-indigo-400 hover:underline"
                >
                    {expandedJson ? "Hide Details" : "Show Details"}
                </button>
            </div>

            {(!isComplete || expandedJson) && <div className="relative m-4">
                <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-gradient-to-b from-indigo-300 to-violet-300 dark:from-indigo-800 dark:to-violet-800"></div>
                <div className="space-y-4 ml-6">
                    <AnimatePresence>
                        {events.map((event, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.3 }}
                                className="relative"
                            > <div className="flex flex-col">
                                    <div className="flex items-center space-x-2">
                                        <div
                                            className={`h-6 w-6 rounded-full flex items-center justify-center ${event.type === "DONE"
                                                ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
                                                : event.type === "chat_message"
                                                    ? "bg-violet-100 text-violet-600 dark:bg-violet-900/30 dark:text-violet-400"
                                                    : "bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400"
                                                }`}
                                        >
                                            {getEventIcon(event.type)}
                                        </div>
                                        <span className="font-medium text-sm">{event.type}</span>
                                    </div>
                                    <p className="text-xs text-muted-foreground mt-1 ml-8">{event.text}</p>
                                </div>
                            </motion.div>
                        ))}
                    </AnimatePresence>
                </div>
            </div>}

        </div>
    )
}
