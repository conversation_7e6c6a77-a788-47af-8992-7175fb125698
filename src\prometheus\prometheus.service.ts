import { Injectable } from '@nestjs/common';
import * as client from 'prom-client';

@Injectable()
export class PrometheusService {
  private readonly register: client.Registry;

  // Example custom metrics
  private readonly httpRequestDurationMicroseconds: client.Histogram;
  private readonly activeRequests: client.Counter;

  constructor() {
    this.register = new client.Registry();
    this.register.setDefaultLabels({ app: 'nestjs-prometheus' });

    // Default metrics
    client.collectDefaultMetrics({ register: this.register });

    // Custom HTTP request duration histogram
    this.httpRequestDurationMicroseconds = new client.Histogram({
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'code'],
      registers: [this.register],
    });

    // Counter for active requests
    this.activeRequests = new client.Counter({
      name: 'http_active_requests_total',
      help: 'Total number of active requests',
      registers: [this.register],
    });
  }

  // Measure request duration
  observeRequestDuration(
    method: string,
    route: string,
    statusCode: number,
    startTime: number,
  ) {
    const duration = Date.now() - startTime;
    this.httpRequestDurationMicroseconds
      .labels(method, route, statusCode.toString())
      .observe(duration / 1000); // Convert to seconds
  }

  // Increment active requests
  incrementActiveRequests() {
    this.activeRequests.inc();
  }

  getMetrics(): Promise<string> {
    return this.register.metrics();
  }
}
