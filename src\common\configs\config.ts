import type { Config } from './config.interface';

const config: Config = {
  nest: {
    port: 3000,
  },
  cors: {
    enabled: true,
  },
  swagger: {
    enabled: true,
    title: 'StepsAI Api',
    description: 'StepsAI Backend API description',
    version: '1.0',
    path: 'docs',
  },
  auth: {
    accessTokenKey: {
      secret: process.env.JWT_ACCESS_TOKEN_SECRET || 'super-secret',
      expiresIn: '7d',
    },
    refreshTokenKey: {
      secret: process.env.JWT_REFRESH_TOKEN_SECRET || 'super-refresh-secret',
      expiresIn: '30d',
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectUri: process.env.GOOGLE_REDIRECT_URI || '',
    },
    microsoft: {
      clientId: process.env.AZURE_CLIENT_ID || '',
      clientSecret: process.env.AZURE_CLIENT_SECRET || '',
      tenantId: process.env.AZURE_TENANT_ID || '',
    },
    jwt: {
      secret: process.env.JWT_SECRET || 'super-secret',
      expiresIn: '24h',
    },
  },
  resend: {
    apiKey: process.env.RESEND_API_KEY || '',
  },
  encryption: {
    key: process.env.ENCRYPTION_KEY || '',
  },
  environment: {
    environment: process.env.ENVIRONMENT || 'development',
  },
  slack: {
    clientId: process.env.SLACK_CLIENT_ID || '',
    clientSecret: process.env.SLACK_CLIENT_SECRET || '',
    redirectUri: process.env.SLACK_REDIRECT_URI || '',
  },
  github: {
    clientId: process.env.GITHUB_CLIENT_ID || '',
    clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
    redirectUri: process.env.GITHUB_REDIRECT_URI || '',
  },
  notion: {
    clientId: process.env.NOTION_CLIENT_ID || '',
    clientSecret: process.env.NOTION_CLIENT_SECRET || '',
    redirectUri: process.env.NOTION_REDIRECT_URI || '',
  },
  hubspot: {
    clientId: process.env.HUBSPOT_CLIENT_ID || '',
    clientSecret: process.env.HUBSPOT_CLIENT_SECRET || '',
    redirectUri: process.env.HUBSPOT_REDIRECT_URI || '',
    appId: process.env.HUBSPOT_APP_ID || '',
  },
  airflow: {
    username: process.env.AIRFLOW_USERNAME || '',
    password: process.env.AIRFLOW_PASSWORD || '',
    baseUrl: process.env.AIRFLOW_BASE_URL || '',
  },
  slackIntegration: {
    clientId: process.env.SLACK_INTEGRATION_CLIENT_ID || '',
    clientSecret: process.env.SLACK_INTEGRATION_CLIENT_SECRET || '',
    redirectUri: process.env.SLACK_INTEGRATION_REDIRECT_URI || '',
  },
  celery: {
    baseUrl: process.env.CELERY_BASE_URL || '',
  },
  milvus: {
    baseUrl: process.env.MILVUS_BASE_URL_V2 || '',
  },
};

export default (): Config => config;
