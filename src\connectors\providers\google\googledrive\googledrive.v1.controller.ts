import {
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  Body,
  Headers,
  HttpCode,
  UseGuards,
  HttpStatus,
  Query,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiResponse,
  ApiBearerAuth,
  ApiOperation,
} from '@nestjs/swagger';
import { GetCurrentUser } from 'src/common/decorators/getUser.decorator';
import { JwtPayload } from 'src/common/types/jwt-payload';
import { GoogleService } from './googledrive.v1.service';
import {
  CrawlGoogleDriveDto,
  GoogleCallbackDto,
  GoogleRefreshTokenDto,
} from './dtos/google.dto';

@ApiTags('Google Drive-V1')
@Controller({ version: '1', path: 'google-drive' })
export class GoogleController {
  constructor(private googleService: GoogleService) {}

  @Post('auth-url')
  @ApiOperation({ summary: 'Generate Google Auth URL' })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated Google Auth URL',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async generateRedirectUrl(@GetCurrentUser() user: JwtPayload) {
    return this.googleService.generateRedirectUrl(user.sub);
  }

  @Post('callback')
  @ApiOperation({ summary: 'Handle Google Auth Callback' })
  @ApiResponse({
    status: 200,
    description: 'Successfully processed Google Auth Callback',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async handleCallback(@Body() body: GoogleCallbackDto) {
    return this.googleService.handleCallback(body.code, body.state);
  }

  @Post('refresh-token')
  @ApiOperation({ summary: 'Refresh Google Auth Token' })
  @ApiResponse({
    status: 201,
    description: 'Successfully refreshed Google Auth Token',
    schema: {
      properties: {
        accessToken: { type: 'string' },
        expiresAt: { type: 'string' },
      },
    },
  })
  async refreshToken(
    @Body() body: GoogleRefreshTokenDto,
    // @Headers('x-signature') signature: string,
  ) {
    // const isValid = this.webhookService.verifySignature(
    //   JSON.stringify(body),
    //   signature,
    // );

    // if (!isValid) {
    //   throw new UnauthorizedException('Invalid signature');
    // }
    return this.googleService.refreshToken(body.refreshToken, body.userId);
  }

  @Post('disconnect')
  @ApiOperation({ summary: 'Disconnect Google Connector' })
  @ApiResponse({
    status: 200,
    description: 'Successfully disconnected Google Connector',
  })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async disconnect(@GetCurrentUser() user: JwtPayload) {
    return this.googleService.disconnect(user.sub);
  }

  @Post('/crawl')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Crawl Google Drive' })
  @ApiResponse({
    status: 200,
    description: 'Crawls a google drive account',
  })
  async crawlGoogleDrive(
    @GetCurrentUser() user: JwtPayload,
    @Body() data: CrawlGoogleDriveDto,
  ) {
    return this.googleService.crawlGoogleDrive(user.sub, data);
  }
}
